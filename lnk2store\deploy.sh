#!/bin/bash

# lnk2store Deployment Script for Hostinger KVM4
# Usage: ./deploy.sh [production|staging]

set -e  # Exit on any error

ENVIRONMENT=${1:-production}
PROJECT_DIR="/var/www/lnk2store"
VENV_DIR="$PROJECT_DIR/venv"
BACKUP_DIR="/var/backups/lnk2store"

echo "🚀 Starting deployment for $ENVIRONMENT environment..."

# Create backup directory if it doesn't exist
sudo mkdir -p $BACKUP_DIR

# Create project directory if it doesn't exist
sudo mkdir -p $PROJECT_DIR

# Backup current deployment if it exists
if [ -d "$PROJECT_DIR/lnk2store" ]; then
    echo "📦 Creating backup..."
    sudo tar -czf "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S).tar.gz" -C "$PROJECT_DIR" lnk2store
fi

# Update system packages
echo "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install required system packages
echo "📦 Installing system dependencies..."
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    postgresql \
    postgresql-contrib \
    redis-server \
    nginx \
    supervisor \
    git \
    curl \
    wget \
    unzip \
    certbot \
    python3-certbot-nginx

# Create virtual environment
echo "🐍 Setting up Python virtual environment..."
if [ ! -d "$VENV_DIR" ]; then
    sudo python3 -m venv $VENV_DIR
fi

# Activate virtual environment
source $VENV_DIR/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

# Set up environment variables
echo "⚙️ Setting up environment configuration..."
if [ "$ENVIRONMENT" = "production" ]; then
    sudo cp .env.production /var/www/lnk2store/.env
else
    sudo cp .env.example /var/www/lnk2store/.env
fi

# Set proper permissions
echo "🔐 Setting file permissions..."
sudo chown -R www-data:www-data $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR

# Create log directory
sudo mkdir -p /var/log/lnk2store
sudo chown -R www-data:www-data /var/log/lnk2store

# Set up database
echo "🗄️ Setting up database..."
sudo -u postgres psql -c "CREATE DATABASE lnk2store_prod;" || true
sudo -u postgres psql -c "CREATE USER lnk2store_user WITH PASSWORD 'CHANGE_THIS_PASSWORD';" || true
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE lnk2store_prod TO lnk2store_user;" || true

# Run Django migrations
echo "🔄 Running database migrations..."
export DJANGO_ENVIRONMENT=$ENVIRONMENT
python manage.py migrate

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

# Create superuser (only if it doesn't exist)
echo "👤 Creating superuser..."
python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created')
else:
    print('Superuser already exists')
"

# Set up Nginx configuration
echo "🌐 Configuring Nginx..."
sudo tee /etc/nginx/sites-available/lnk2store.com > /dev/null <<EOF
server {
    listen 80;
    server_name lnk2store.com www.lnk2store.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name lnk2store.com www.lnk2store.com;
    
    # SSL Configuration (will be updated by certbot)
    ssl_certificate /etc/letsencrypt/live/lnk2store.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/lnk2store.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Static files
    location /static/ {
        alias $PROJECT_DIR/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Media files
    location /media/ {
        alias $PROJECT_DIR/media/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Django application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_redirect off;
    }
    
    # React frontend (if serving separately)
    location /app/ {
        alias $PROJECT_DIR/frontend/build/;
        try_files \$uri \$uri/ /app/index.html;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/lnk2store.com /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Set up Supervisor for Gunicorn
echo "⚙️ Configuring Supervisor..."
sudo tee /etc/supervisor/conf.d/lnk2store.conf > /dev/null <<EOF
[program:lnk2store]
command=$VENV_DIR/bin/gunicorn lnk2store.wsgi:application --bind 127.0.0.1:8000 --workers 3
directory=$PROJECT_DIR
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/lnk2store/gunicorn.log
environment=DJANGO_ENVIRONMENT="$ENVIRONMENT"

[program:lnk2store-celery]
command=$VENV_DIR/bin/celery -A lnk2store worker --loglevel=info
directory=$PROJECT_DIR
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/lnk2store/celery.log
environment=DJANGO_ENVIRONMENT="$ENVIRONMENT"
EOF

# Reload supervisor
sudo supervisorctl reread
sudo supervisorctl update

# Start services
echo "🚀 Starting services..."
sudo systemctl enable redis-server
sudo systemctl start redis-server
sudo systemctl enable postgresql
sudo systemctl start postgresql
sudo systemctl enable nginx
sudo systemctl restart nginx
sudo supervisorctl restart lnk2store
sudo supervisorctl restart lnk2store-celery

echo "✅ Deployment completed successfully!"
echo ""
echo "🔧 Next steps:"
echo "1. Update the .env file with your actual configuration"
echo "2. Run: sudo certbot --nginx -d lnk2store.com -d www.lnk2store.com"
echo "3. Update DNS records to point to your server IP"
echo "4. Test the application at https://lnk2store.com"
echo ""
echo "📊 Service status:"
sudo supervisorctl status
echo ""
echo "🌐 Nginx status:"
sudo systemctl status nginx --no-pager -l
