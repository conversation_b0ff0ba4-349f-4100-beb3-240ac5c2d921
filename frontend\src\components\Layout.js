import React from 'react';
import { AppB<PERSON>, Too<PERSON><PERSON>, Button, Box } from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import Logo from './Logo';

const Layout = ({ children }) => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar
        position="static"
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}
      >
        <Toolbar sx={{ py: 1 }}>
          <Box sx={{ flexGrow: 1 }}>
            <Logo height="45px" forHeader={true} />
          </Box>
          
          {isAuthenticated ? (
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Button
                color="inherit"
                component={Link}
                to="/dashboard"
                sx={{ fontWeight: 500 }}
              >
                لوحة التحكم
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/products"
                sx={{ fontWeight: 500 }}
              >
                المنتجات
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/my-pages"
                sx={{ fontWeight: 500 }}
              >
                صفحاتي
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/templates"
                sx={{ fontWeight: 500 }}
              >
                القوالب
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/leads"
                sx={{ fontWeight: 500 }}
              >
                الطلبات
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/wallet"
                sx={{ fontWeight: 500 }}
              >
                المحفظة
              </Button>
              {(user?.is_staff || user?.is_superuser) && (
                <Button
                  color="inherit"
                  component={Link}
                  to="/admin"
                  sx={{ fontWeight: 500 }}
                >
                  إدارة النظام
                </Button>
              )}
              <Button
                color="inherit"
                onClick={handleLogout}
                sx={{
                  fontWeight: 500,
                  bgcolor: 'rgba(255,255,255,0.1)',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.2)'
                  }
                }}
              >
                تسجيل الخروج ({user?.username})
              </Button>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                color="inherit"
                component={Link}
                to="/login"
                sx={{ fontWeight: 500 }}
              >
                تسجيل الدخول
              </Button>
              <Button
                variant="contained"
                component={Link}
                to="/register"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'grey.100'
                  }
                }}
              >
                إنشاء حساب
              </Button>
            </Box>
          )}
        </Toolbar>
      </AppBar>

      <Box
        sx={{
          minHeight: 'calc(100vh - 80px)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          width: '100%'
        }}
      >
        <Box sx={{ width: '100%', maxWidth: '1200px', px: { xs: 2, md: 3 } }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
