import React from 'react';
import { AppB<PERSON>, Too<PERSON>bar, Typography, Button, Box, Container } from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

const Layout = ({ children }) => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            <Link to="/" style={{ color: 'white', textDecoration: 'none' }}>
              Lnk2Store
            </Link>
          </Typography>
          
          {isAuthenticated ? (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button color="inherit" component={Link} to="/dashboard">
                لوحة التحكم
              </Button>
              <Button color="inherit" component={Link} to="/products">
                المنتجات
              </Button>
              <Button color="inherit" component={Link} to="/leads">
                الطلبات
              </Button>
              <Button color="inherit" component={Link} to="/wallet">
                المحفظة
              </Button>
              {(user?.is_staff || user?.is_superuser) && (
                <Button color="inherit" component={Link} to="/admin">
                  إدارة النظام
                </Button>
              )}
              <Button color="inherit" onClick={handleLogout}>
                تسجيل الخروج ({user?.username})
              </Button>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button color="inherit" component={Link} to="/login">
                تسجيل الدخول
              </Button>
              <Button color="inherit" component={Link} to="/register">
                إنشاء حساب
              </Button>
            </Box>
          )}
        </Toolbar>
      </AppBar>
      
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {children}
      </Container>
    </Box>
  );
};

export default Layout;
