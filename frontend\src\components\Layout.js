import React from 'react';
import { AppBar, Toolbar, Button, Box } from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import Logo from './Logo';
import SettingsMenu from './SettingsMenu';

const Layout = ({ children }) => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar
        position="static"
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }}
      >
        <Toolbar sx={{ py: 1, minHeight: '64px' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Logo height="40px" forHeader={true} />
          </Box>

          {isAuthenticated ? (
            <Box sx={{
              display: 'flex',
              gap: { xs: 0.5, md: 1 },
              alignItems: 'center',
              flexWrap: 'wrap'
            }}>
              <Button
                color="inherit"
                component={Link}
                to="/dashboard"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  px: { xs: 1, md: 2 }
                }}
              >
                لوحة التحكم
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/products"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  px: { xs: 1, md: 2 }
                }}
              >
                المنتجات
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/my-pages"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  px: { xs: 1, md: 2 }
                }}
              >
                صفحاتي
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/templates"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  px: { xs: 1, md: 2 }
                }}
              >
                القوالب
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/leads"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  px: { xs: 1, md: 2 }
                }}
              >
                الطلبات
              </Button>
              <Button
                color="inherit"
                component={Link}
                to="/wallet"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  px: { xs: 1, md: 2 }
                }}
              >
                المحفظة
              </Button>
              {(user?.is_staff || user?.is_superuser) && (
                <Button
                  color="inherit"
                  component={Link}
                  to="/admin"
                  sx={{
                    fontWeight: 500,
                    fontSize: { xs: '0.8rem', md: '0.875rem' },
                    px: { xs: 1, md: 2 }
                  }}
                >
                  إدارة النظام
                </Button>
              )}
              <SettingsMenu />
              <Button
                color="inherit"
                onClick={handleLogout}
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.8rem', md: '0.875rem' },
                  px: { xs: 1, md: 2 },
                  bgcolor: 'rgba(255,255,255,0.1)',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.2)'
                  }
                }}
              >
                تسجيل الخروج ({user?.username})
              </Button>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', gap: { xs: 1, md: 2 }, alignItems: 'center' }}>
              <Button
                color="inherit"
                component={Link}
                to="/login"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.9rem', md: '1rem' },
                  px: { xs: 2, md: 3 }
                }}
              >
                تسجيل الدخول
              </Button>
              <Button
                variant="contained"
                component={Link}
                to="/register"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  fontWeight: 600,
                  fontSize: { xs: '0.9rem', md: '1rem' },
                  px: { xs: 2, md: 3 },
                  '&:hover': {
                    bgcolor: 'grey.100'
                  }
                }}
              >
                إنشاء حساب
              </Button>
            </Box>
          )}
        </Toolbar>
      </AppBar>

      <Box
        sx={{
          minHeight: 'calc(100vh - 80px)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          width: '100%'
        }}
      >
        <Box sx={{ width: '100%', maxWidth: '1200px', px: { xs: 2, md: 3 } }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
