from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import Template, UserPage, TemplateReview
from .template_serializers import (
    TemplateListSerializer,
    TemplateDetailSerializer,
    UserPageListSerializer,
    UserPageDetailSerializer,
    UserPageCreateSerializer,
    UserPageUpdateSerializer,
    TemplateReviewSerializer,
    TemplateReviewCreateSerializer
)

class TemplateListView(generics.ListAPIView):
    """عرض قائمة القوالب - متاح للجميع"""
    serializer_class = TemplateListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = Template.objects.filter(is_active=True)
        
        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)
        
        # Filter by type
        template_type = self.request.query_params.get('type')
        if template_type:
            queryset = queryset.filter(template_type=template_type)
        
        # Filter by premium status
        is_premium = self.request.query_params.get('premium')
        if is_premium is not None:
            queryset = queryset.filter(is_premium=is_premium.lower() == 'true')
        
        return queryset

class TemplateDetailView(generics.RetrieveAPIView):
    """عرض تفاصيل قالب - متاح للجميع"""
    queryset = Template.objects.filter(is_active=True)
    serializer_class = TemplateDetailSerializer
    permission_classes = [AllowAny]

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        # Increment usage count
        instance.increment_usage()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

class UserPageListView(generics.ListAPIView):
    """عرض صفحات المستخدم"""
    serializer_class = UserPageListSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserPage.objects.filter(user=self.request.user)

class UserPageCreateView(generics.CreateAPIView):
    """إنشاء صفحة جديدة للمستخدم"""
    serializer_class = UserPageCreateSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class UserPageDetailView(generics.RetrieveAPIView):
    """عرض تفاصيل صفحة المستخدم"""
    serializer_class = UserPageDetailSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserPage.objects.filter(user=self.request.user)

class UserPageUpdateView(generics.UpdateAPIView):
    """تحديث صفحة المستخدم"""
    serializer_class = UserPageUpdateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserPage.objects.filter(user=self.request.user)

class UserPageDeleteView(generics.DestroyAPIView):
    """حذف صفحة المستخدم"""
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserPage.objects.filter(user=self.request.user)

@api_view(['GET'])
@permission_classes([AllowAny])
def render_user_page(request, slug):
    """عرض الصفحة المعروضة للزوار"""
    try:
        user_page = UserPage.objects.get(slug=slug, status='published')
        
        # Increment view count
        user_page.increment_views()
        
        # Get rendered HTML
        rendered_html = user_page.get_rendered_html()
        
        return Response({
            'html': rendered_html,
            'title': user_page.title,
            'meta_title': user_page.meta_title,
            'meta_description': user_page.meta_description,
            'meta_keywords': user_page.meta_keywords,
        })
        
    except UserPage.DoesNotExist:
        return Response({
            'error': 'الصفحة غير موجودة'
        }, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def publish_user_page(request, pk):
    """نشر صفحة المستخدم"""
    try:
        user_page = UserPage.objects.get(pk=pk, user=request.user)
        user_page.status = 'published'
        user_page.published_at = timezone.now()
        user_page.save()
        
        return Response({
            'message': 'تم نشر الصفحة بنجاح',
            'page': UserPageDetailSerializer(user_page).data
        })
        
    except UserPage.DoesNotExist:
        return Response({
            'error': 'الصفحة غير موجودة'
        }, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def unpublish_user_page(request, pk):
    """إلغاء نشر صفحة المستخدم"""
    try:
        user_page = UserPage.objects.get(pk=pk, user=request.user)
        user_page.status = 'draft'
        user_page.save()
        
        return Response({
            'message': 'تم إلغاء نشر الصفحة',
            'page': UserPageDetailSerializer(user_page).data
        })
        
    except UserPage.DoesNotExist:
        return Response({
            'error': 'الصفحة غير موجودة'
        }, status=status.HTTP_404_NOT_FOUND)

class TemplateReviewListView(generics.ListAPIView):
    """عرض تقييمات القالب"""
    serializer_class = TemplateReviewSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        template_id = self.kwargs['template_id']
        return TemplateReview.objects.filter(template_id=template_id)

class TemplateReviewCreateView(generics.CreateAPIView):
    """إضافة تقييم للقالب"""
    serializer_class = TemplateReviewCreateSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

@api_view(['GET'])
@permission_classes([AllowAny])
def template_categories(request):
    """الحصول على تصنيفات القوالب"""
    categories = Template.CATEGORIES
    return Response([
        {'value': value, 'label': label}
        for value, label in categories
    ])

@api_view(['GET'])
@permission_classes([AllowAny])
def template_types(request):
    """الحصول على أنواع القوالب"""
    types = Template.TEMPLATE_TYPES
    return Response([
        {'value': value, 'label': label}
        for value, label in types
    ])
