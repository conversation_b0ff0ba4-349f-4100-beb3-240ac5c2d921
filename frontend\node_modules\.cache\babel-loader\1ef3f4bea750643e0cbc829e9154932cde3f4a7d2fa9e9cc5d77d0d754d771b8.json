{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport refType from '@mui/utils/refType';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport debounce from \"../utils/debounce.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport Grow from \"../Grow/index.js\";\nimport Modal from \"../Modal/index.js\";\nimport PaperBase from \"../Paper/index.js\";\nimport { getPopoverUtilityClass } from \"./popoverClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getOffsetTop(rect, vertical) {\n  let offset = 0;\n  if (typeof vertical === 'number') {\n    offset = vertical;\n  } else if (vertical === 'center') {\n    offset = rect.height / 2;\n  } else if (vertical === 'bottom') {\n    offset = rect.height;\n  }\n  return offset;\n}\nexport function getOffsetLeft(rect, horizontal) {\n  let offset = 0;\n  if (typeof horizontal === 'number') {\n    offset = horizontal;\n  } else if (horizontal === 'center') {\n    offset = rect.width / 2;\n  } else if (horizontal === 'right') {\n    offset = rect.width;\n  }\n  return offset;\n}\nfunction getTransformOriginValue(transformOrigin) {\n  return [transformOrigin.horizontal, transformOrigin.vertical].map(n => typeof n === 'number' ? `${n}px` : n).join(' ');\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPopoverUtilityClass, classes);\n};\nexport const PopoverRoot = styled(Modal, {\n  name: 'MuiPopover',\n  slot: 'Root'\n})({});\nexport const PopoverPaper = styled(PaperBase, {\n  name: 'MuiPopover',\n  slot: 'Paper'\n})({\n  position: 'absolute',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // So we see the popover when it's empty.\n  // It's most likely on issue on userland.\n  minWidth: 16,\n  minHeight: 16,\n  maxWidth: 'calc(100% - 32px)',\n  maxHeight: 'calc(100% - 32px)',\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Popover = /*#__PURE__*/React.forwardRef(function Popover(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPopover'\n  });\n  const {\n    action,\n    anchorEl,\n    anchorOrigin = {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    anchorPosition,\n    anchorReference = 'anchorEl',\n    children,\n    className,\n    container: containerProp,\n    elevation = 8,\n    marginThreshold = 16,\n    open,\n    PaperProps: PaperPropsProp = {},\n    // TODO: remove in v7\n    slots = {},\n    slotProps = {},\n    transformOrigin = {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    TransitionComponent,\n    // TODO: remove in v7\n    transitionDuration: transitionDurationProp = 'auto',\n    TransitionProps = {},\n    // TODO: remove in v7\n    disableScrollLock = false,\n    ...other\n  } = props;\n  const paperRef = React.useRef();\n  const ownerState = {\n    ...props,\n    anchorOrigin,\n    anchorReference,\n    elevation,\n    marginThreshold,\n    transformOrigin,\n    TransitionComponent,\n    transitionDuration: transitionDurationProp,\n    TransitionProps\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // Returns the top/left offset of the position\n  // to attach to on the anchor element (or body if none is provided)\n  const getAnchorOffset = React.useCallback(() => {\n    if (anchorReference === 'anchorPosition') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!anchorPosition) {\n          console.error('MUI: You need to provide a `anchorPosition` prop when using ' + '<Popover anchorReference=\"anchorPosition\" />.');\n        }\n      }\n      return anchorPosition;\n    }\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n\n    // If an anchor element wasn't provided, just use the parent body element of this Popover\n    const anchorElement = resolvedAnchorEl && resolvedAnchorEl.nodeType === 1 ? resolvedAnchorEl : ownerDocument(paperRef.current).body;\n    const anchorRect = anchorElement.getBoundingClientRect();\n    if (process.env.NODE_ENV !== 'production') {\n      const box = anchorElement.getBoundingClientRect();\n      if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n        console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n      }\n    }\n    return {\n      top: anchorRect.top + getOffsetTop(anchorRect, anchorOrigin.vertical),\n      left: anchorRect.left + getOffsetLeft(anchorRect, anchorOrigin.horizontal)\n    };\n  }, [anchorEl, anchorOrigin.horizontal, anchorOrigin.vertical, anchorPosition, anchorReference]);\n\n  // Returns the base transform origin using the element\n  const getTransformOrigin = React.useCallback(elemRect => {\n    return {\n      vertical: getOffsetTop(elemRect, transformOrigin.vertical),\n      horizontal: getOffsetLeft(elemRect, transformOrigin.horizontal)\n    };\n  }, [transformOrigin.horizontal, transformOrigin.vertical]);\n  const getPositioningStyle = React.useCallback(element => {\n    const elemRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n\n    // Get the transform origin point on the element itself\n    const elemTransformOrigin = getTransformOrigin(elemRect);\n    if (anchorReference === 'none') {\n      return {\n        top: null,\n        left: null,\n        transformOrigin: getTransformOriginValue(elemTransformOrigin)\n      };\n    }\n\n    // Get the offset of the anchoring element\n    const anchorOffset = getAnchorOffset();\n\n    // Calculate element positioning\n    let top = anchorOffset.top - elemTransformOrigin.vertical;\n    let left = anchorOffset.left - elemTransformOrigin.horizontal;\n    const bottom = top + elemRect.height;\n    const right = left + elemRect.width;\n\n    // Use the parent window of the anchorEl if provided\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n\n    // Window thresholds taking required margin into account\n    const heightThreshold = containerWindow.innerHeight - marginThreshold;\n    const widthThreshold = containerWindow.innerWidth - marginThreshold;\n\n    // Check if the vertical axis needs shifting\n    if (marginThreshold !== null && top < marginThreshold) {\n      const diff = top - marginThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    } else if (marginThreshold !== null && bottom > heightThreshold) {\n      const diff = bottom - heightThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (elemRect.height > heightThreshold && elemRect.height && heightThreshold) {\n        console.error(['MUI: The popover component is too tall.', `Some part of it can not be seen on the screen (${elemRect.height - heightThreshold}px).`, 'Please consider adding a `max-height` to improve the user-experience.'].join('\\n'));\n      }\n    }\n\n    // Check if the horizontal axis needs shifting\n    if (marginThreshold !== null && left < marginThreshold) {\n      const diff = left - marginThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    } else if (right > widthThreshold) {\n      const diff = right - widthThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    }\n    return {\n      top: `${Math.round(top)}px`,\n      left: `${Math.round(left)}px`,\n      transformOrigin: getTransformOriginValue(elemTransformOrigin)\n    };\n  }, [anchorEl, anchorReference, getAnchorOffset, getTransformOrigin, marginThreshold]);\n  const [isPositioned, setIsPositioned] = React.useState(open);\n  const setPositioningStyles = React.useCallback(() => {\n    const element = paperRef.current;\n    if (!element) {\n      return;\n    }\n    const positioning = getPositioningStyle(element);\n    if (positioning.top !== null) {\n      element.style.setProperty('top', positioning.top);\n    }\n    if (positioning.left !== null) {\n      element.style.left = positioning.left;\n    }\n    element.style.transformOrigin = positioning.transformOrigin;\n    setIsPositioned(true);\n  }, [getPositioningStyle]);\n  React.useEffect(() => {\n    if (disableScrollLock) {\n      window.addEventListener('scroll', setPositioningStyles);\n    }\n    return () => window.removeEventListener('scroll', setPositioningStyles);\n  }, [anchorEl, disableScrollLock, setPositioningStyles]);\n  const handleEntering = () => {\n    setPositioningStyles();\n  };\n  const handleExited = () => {\n    setIsPositioned(false);\n  };\n  React.useEffect(() => {\n    if (open) {\n      setPositioningStyles();\n    }\n  });\n  React.useImperativeHandle(action, () => open ? {\n    updatePosition: () => {\n      setPositioningStyles();\n    }\n  } : null, [open, setPositioningStyles]);\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n    const handleResize = debounce(() => {\n      setPositioningStyles();\n    });\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [anchorEl, open, setPositioningStyles]);\n  let transitionDuration = transitionDurationProp;\n  const externalForwardedProps = {\n    slots: {\n      transition: TransitionComponent,\n      ...slots\n    },\n    slotProps: {\n      transition: TransitionProps,\n      paper: PaperPropsProp,\n      ...slotProps\n    }\n  };\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Grow,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onEntering: (element, isAppearing) => {\n        handlers.onEntering?.(element, isAppearing);\n        handleEntering();\n      },\n      onExited: element => {\n        handlers.onExited?.(element);\n        handleExited();\n      }\n    }),\n    additionalProps: {\n      appear: true,\n      in: open\n    }\n  });\n  if (transitionDurationProp === 'auto' && !TransitionSlot.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  const container = containerProp || (anchorEl ? ownerDocument(resolveAnchorEl(anchorEl)).body : undefined);\n  const [RootSlot, {\n    slots: rootSlotsProp,\n    slotProps: rootSlotPropsProp,\n    ...rootProps\n  }] = useSlot('root', {\n    ref,\n    elementType: PopoverRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      slots: {\n        backdrop: slots.backdrop\n      },\n      slotProps: {\n        backdrop: mergeSlotProps(typeof slotProps.backdrop === 'function' ? slotProps.backdrop(ownerState) : slotProps.backdrop, {\n          invisible: true\n        })\n      },\n      container,\n      open\n    },\n    ownerState,\n    className: clsx(classes.root, className)\n  });\n  const [PaperSlot, paperProps] = useSlot('paper', {\n    ref: paperRef,\n    className: classes.paper,\n    elementType: PopoverPaper,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      elevation,\n      style: isPositioned ? undefined : {\n        opacity: 0\n      }\n    },\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(RootSlot, {\n    ...rootProps,\n    ...(!isHostComponent(RootSlot) && {\n      slots: rootSlotsProp,\n      slotProps: rootSlotPropsProp,\n      disableScrollLock\n    }),\n    children: /*#__PURE__*/_jsx(TransitionSlot, {\n      ...transitionSlotProps,\n      timeout: transitionDuration,\n      children: /*#__PURE__*/_jsx(PaperSlot, {\n        ...paperProps,\n        children: children\n      })\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Popover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions.\n   * It currently only supports updatePosition() action.\n   */\n  action: refType,\n  /**\n   * An HTML element, [PopoverVirtualElement](https://mui.com/material-ui/react-popover/#virtual-element),\n   * or a function that returns either.\n   * It's used to set the position of the popover.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.func]), props => {\n    if (props.open && (!props.anchorReference || props.anchorReference === 'anchorEl')) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', `It should be an Element or PopoverVirtualElement instance but it's \\`${resolvedAnchorEl}\\` instead.`].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * This is the point on the anchor where the popover's\n   * `anchorEl` will attach to. This is not used when the\n   * anchorReference is 'anchorPosition'.\n   *\n   * Options:\n   * vertical: [top, center, bottom];\n   * horizontal: [left, center, right].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * This is the position that may be used to set the position of the popover.\n   * The coordinates are relative to the application's client area.\n   */\n  anchorPosition: PropTypes.shape({\n    left: PropTypes.number.isRequired,\n    top: PropTypes.number.isRequired\n  }),\n  /**\n   * This determines which anchor prop to refer to when setting\n   * the position of the popover.\n   * @default 'anchorEl'\n   */\n  anchorReference: PropTypes.oneOf(['anchorEl', 'anchorPosition', 'none']),\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * An HTML element, component instance, or function that returns either.\n   * The `container` will passed to the Modal component.\n   *\n   * By default, it uses the body of the anchorEl's top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * The elevation of the popover.\n   * @default 8\n   */\n  elevation: integerPropType,\n  /**\n   * Specifies how close to the edge of the window the popover can appear.\n   * If null, the popover will not be constrained by the window.\n   * @default 16\n   */\n  marginThreshold: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Props applied to the [`Paper`](https://mui.com/material-ui/api/paper/) element.\n   *\n   * This prop is an alias for `slotProps.paper` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.paper` instead.\n   *\n   * @default {}\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * This is the point on the popover which\n   * will attach to the anchor's origin.\n   *\n   * Options:\n   * vertical: [top, center, bottom, x(px)];\n   * horizontal: [left, center, right, x(px)].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  transformOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @deprecated use the `slots.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Popover;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "HTMLElementType", "refType", "elementTypeAcceptingRef", "integerPropType", "chainPropTypes", "isHostComponent", "styled", "useDefaultProps", "debounce", "ownerDocument", "ownerWindow", "Grow", "Modal", "PaperBase", "getPopoverUtilityClass", "useSlot", "mergeSlotProps", "jsx", "_jsx", "getOffsetTop", "rect", "vertical", "offset", "height", "getOffsetLeft", "horizontal", "width", "getTransformOriginValue", "transform<PERSON><PERSON>in", "map", "n", "join", "resolveAnchorEl", "anchorEl", "useUtilityClasses", "ownerState", "classes", "slots", "root", "paper", "PopoverRoot", "name", "slot", "PopoverPaper", "position", "overflowY", "overflowX", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "outline", "Popover", "forwardRef", "inProps", "ref", "props", "action", "anchor<PERSON><PERSON><PERSON>", "anchorPosition", "anchorReference", "children", "className", "container", "containerProp", "elevation", "marginT<PERSON><PERSON>old", "open", "PaperProps", "PaperPropsProp", "slotProps", "TransitionComponent", "transitionDuration", "transitionDurationProp", "TransitionProps", "disableScrollLock", "other", "paperRef", "useRef", "getAnchorOffset", "useCallback", "process", "env", "NODE_ENV", "console", "error", "resolvedAnchorEl", "anchorElement", "nodeType", "current", "body", "anchorRect", "getBoundingClientRect", "box", "top", "left", "right", "bottom", "warn", "getTransformOrigin", "elemRect", "getPositioningStyle", "element", "offsetWidth", "offsetHeight", "elemTransformOrigin", "anchorOffset", "containerWindow", "heightThreshold", "innerHeight", "widthThreshold", "innerWidth", "diff", "Math", "round", "isPositioned", "setIsPositioned", "useState", "setPositioningStyles", "positioning", "style", "setProperty", "useEffect", "window", "addEventListener", "removeEventListener", "handleEntering", "handleExited", "useImperativeHandle", "updatePosition", "undefined", "handleResize", "clear", "externalForwardedProps", "transition", "TransitionSlot", "transitionSlotProps", "elementType", "getSlotProps", "handlers", "onEntering", "isAppearing", "onExited", "additionalProps", "appear", "in", "muiSupportAuto", "RootSlot", "rootSlotsProp", "rootSlotPropsProp", "rootProps", "shouldForwardComponentProp", "backdrop", "invisible", "PaperSlot", "paperProps", "opacity", "timeout", "propTypes", "oneOfType", "func", "Error", "shape", "oneOf", "number", "isRequired", "BackdropComponent", "BackdropProps", "object", "node", "string", "bool", "onClose", "component", "sx", "arrayOf", "enter", "exit"], "sources": ["D:/apps/lnk2store/frontend/node_modules/@mui/material/esm/Popover/Popover.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport refType from '@mui/utils/refType';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport debounce from \"../utils/debounce.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport Grow from \"../Grow/index.js\";\nimport Modal from \"../Modal/index.js\";\nimport PaperBase from \"../Paper/index.js\";\nimport { getPopoverUtilityClass } from \"./popoverClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getOffsetTop(rect, vertical) {\n  let offset = 0;\n  if (typeof vertical === 'number') {\n    offset = vertical;\n  } else if (vertical === 'center') {\n    offset = rect.height / 2;\n  } else if (vertical === 'bottom') {\n    offset = rect.height;\n  }\n  return offset;\n}\nexport function getOffsetLeft(rect, horizontal) {\n  let offset = 0;\n  if (typeof horizontal === 'number') {\n    offset = horizontal;\n  } else if (horizontal === 'center') {\n    offset = rect.width / 2;\n  } else if (horizontal === 'right') {\n    offset = rect.width;\n  }\n  return offset;\n}\nfunction getTransformOriginValue(transformOrigin) {\n  return [transformOrigin.horizontal, transformOrigin.vertical].map(n => typeof n === 'number' ? `${n}px` : n).join(' ');\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPopoverUtilityClass, classes);\n};\nexport const PopoverRoot = styled(Modal, {\n  name: 'MuiPopover',\n  slot: 'Root'\n})({});\nexport const PopoverPaper = styled(PaperBase, {\n  name: 'MuiPopover',\n  slot: 'Paper'\n})({\n  position: 'absolute',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // So we see the popover when it's empty.\n  // It's most likely on issue on userland.\n  minWidth: 16,\n  minHeight: 16,\n  maxWidth: 'calc(100% - 32px)',\n  maxHeight: 'calc(100% - 32px)',\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Popover = /*#__PURE__*/React.forwardRef(function Popover(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPopover'\n  });\n  const {\n    action,\n    anchorEl,\n    anchorOrigin = {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    anchorPosition,\n    anchorReference = 'anchorEl',\n    children,\n    className,\n    container: containerProp,\n    elevation = 8,\n    marginThreshold = 16,\n    open,\n    PaperProps: PaperPropsProp = {},\n    // TODO: remove in v7\n    slots = {},\n    slotProps = {},\n    transformOrigin = {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    TransitionComponent,\n    // TODO: remove in v7\n    transitionDuration: transitionDurationProp = 'auto',\n    TransitionProps = {},\n    // TODO: remove in v7\n    disableScrollLock = false,\n    ...other\n  } = props;\n  const paperRef = React.useRef();\n  const ownerState = {\n    ...props,\n    anchorOrigin,\n    anchorReference,\n    elevation,\n    marginThreshold,\n    transformOrigin,\n    TransitionComponent,\n    transitionDuration: transitionDurationProp,\n    TransitionProps\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // Returns the top/left offset of the position\n  // to attach to on the anchor element (or body if none is provided)\n  const getAnchorOffset = React.useCallback(() => {\n    if (anchorReference === 'anchorPosition') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!anchorPosition) {\n          console.error('MUI: You need to provide a `anchorPosition` prop when using ' + '<Popover anchorReference=\"anchorPosition\" />.');\n        }\n      }\n      return anchorPosition;\n    }\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n\n    // If an anchor element wasn't provided, just use the parent body element of this Popover\n    const anchorElement = resolvedAnchorEl && resolvedAnchorEl.nodeType === 1 ? resolvedAnchorEl : ownerDocument(paperRef.current).body;\n    const anchorRect = anchorElement.getBoundingClientRect();\n    if (process.env.NODE_ENV !== 'production') {\n      const box = anchorElement.getBoundingClientRect();\n      if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n        console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n      }\n    }\n    return {\n      top: anchorRect.top + getOffsetTop(anchorRect, anchorOrigin.vertical),\n      left: anchorRect.left + getOffsetLeft(anchorRect, anchorOrigin.horizontal)\n    };\n  }, [anchorEl, anchorOrigin.horizontal, anchorOrigin.vertical, anchorPosition, anchorReference]);\n\n  // Returns the base transform origin using the element\n  const getTransformOrigin = React.useCallback(elemRect => {\n    return {\n      vertical: getOffsetTop(elemRect, transformOrigin.vertical),\n      horizontal: getOffsetLeft(elemRect, transformOrigin.horizontal)\n    };\n  }, [transformOrigin.horizontal, transformOrigin.vertical]);\n  const getPositioningStyle = React.useCallback(element => {\n    const elemRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n\n    // Get the transform origin point on the element itself\n    const elemTransformOrigin = getTransformOrigin(elemRect);\n    if (anchorReference === 'none') {\n      return {\n        top: null,\n        left: null,\n        transformOrigin: getTransformOriginValue(elemTransformOrigin)\n      };\n    }\n\n    // Get the offset of the anchoring element\n    const anchorOffset = getAnchorOffset();\n\n    // Calculate element positioning\n    let top = anchorOffset.top - elemTransformOrigin.vertical;\n    let left = anchorOffset.left - elemTransformOrigin.horizontal;\n    const bottom = top + elemRect.height;\n    const right = left + elemRect.width;\n\n    // Use the parent window of the anchorEl if provided\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n\n    // Window thresholds taking required margin into account\n    const heightThreshold = containerWindow.innerHeight - marginThreshold;\n    const widthThreshold = containerWindow.innerWidth - marginThreshold;\n\n    // Check if the vertical axis needs shifting\n    if (marginThreshold !== null && top < marginThreshold) {\n      const diff = top - marginThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    } else if (marginThreshold !== null && bottom > heightThreshold) {\n      const diff = bottom - heightThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (elemRect.height > heightThreshold && elemRect.height && heightThreshold) {\n        console.error(['MUI: The popover component is too tall.', `Some part of it can not be seen on the screen (${elemRect.height - heightThreshold}px).`, 'Please consider adding a `max-height` to improve the user-experience.'].join('\\n'));\n      }\n    }\n\n    // Check if the horizontal axis needs shifting\n    if (marginThreshold !== null && left < marginThreshold) {\n      const diff = left - marginThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    } else if (right > widthThreshold) {\n      const diff = right - widthThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    }\n    return {\n      top: `${Math.round(top)}px`,\n      left: `${Math.round(left)}px`,\n      transformOrigin: getTransformOriginValue(elemTransformOrigin)\n    };\n  }, [anchorEl, anchorReference, getAnchorOffset, getTransformOrigin, marginThreshold]);\n  const [isPositioned, setIsPositioned] = React.useState(open);\n  const setPositioningStyles = React.useCallback(() => {\n    const element = paperRef.current;\n    if (!element) {\n      return;\n    }\n    const positioning = getPositioningStyle(element);\n    if (positioning.top !== null) {\n      element.style.setProperty('top', positioning.top);\n    }\n    if (positioning.left !== null) {\n      element.style.left = positioning.left;\n    }\n    element.style.transformOrigin = positioning.transformOrigin;\n    setIsPositioned(true);\n  }, [getPositioningStyle]);\n  React.useEffect(() => {\n    if (disableScrollLock) {\n      window.addEventListener('scroll', setPositioningStyles);\n    }\n    return () => window.removeEventListener('scroll', setPositioningStyles);\n  }, [anchorEl, disableScrollLock, setPositioningStyles]);\n  const handleEntering = () => {\n    setPositioningStyles();\n  };\n  const handleExited = () => {\n    setIsPositioned(false);\n  };\n  React.useEffect(() => {\n    if (open) {\n      setPositioningStyles();\n    }\n  });\n  React.useImperativeHandle(action, () => open ? {\n    updatePosition: () => {\n      setPositioningStyles();\n    }\n  } : null, [open, setPositioningStyles]);\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n    const handleResize = debounce(() => {\n      setPositioningStyles();\n    });\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [anchorEl, open, setPositioningStyles]);\n  let transitionDuration = transitionDurationProp;\n  const externalForwardedProps = {\n    slots: {\n      transition: TransitionComponent,\n      ...slots\n    },\n    slotProps: {\n      transition: TransitionProps,\n      paper: PaperPropsProp,\n      ...slotProps\n    }\n  };\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Grow,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onEntering: (element, isAppearing) => {\n        handlers.onEntering?.(element, isAppearing);\n        handleEntering();\n      },\n      onExited: element => {\n        handlers.onExited?.(element);\n        handleExited();\n      }\n    }),\n    additionalProps: {\n      appear: true,\n      in: open\n    }\n  });\n  if (transitionDurationProp === 'auto' && !TransitionSlot.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  const container = containerProp || (anchorEl ? ownerDocument(resolveAnchorEl(anchorEl)).body : undefined);\n  const [RootSlot, {\n    slots: rootSlotsProp,\n    slotProps: rootSlotPropsProp,\n    ...rootProps\n  }] = useSlot('root', {\n    ref,\n    elementType: PopoverRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      slots: {\n        backdrop: slots.backdrop\n      },\n      slotProps: {\n        backdrop: mergeSlotProps(typeof slotProps.backdrop === 'function' ? slotProps.backdrop(ownerState) : slotProps.backdrop, {\n          invisible: true\n        })\n      },\n      container,\n      open\n    },\n    ownerState,\n    className: clsx(classes.root, className)\n  });\n  const [PaperSlot, paperProps] = useSlot('paper', {\n    ref: paperRef,\n    className: classes.paper,\n    elementType: PopoverPaper,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      elevation,\n      style: isPositioned ? undefined : {\n        opacity: 0\n      }\n    },\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(RootSlot, {\n    ...rootProps,\n    ...(!isHostComponent(RootSlot) && {\n      slots: rootSlotsProp,\n      slotProps: rootSlotPropsProp,\n      disableScrollLock\n    }),\n    children: /*#__PURE__*/_jsx(TransitionSlot, {\n      ...transitionSlotProps,\n      timeout: transitionDuration,\n      children: /*#__PURE__*/_jsx(PaperSlot, {\n        ...paperProps,\n        children: children\n      })\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Popover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions.\n   * It currently only supports updatePosition() action.\n   */\n  action: refType,\n  /**\n   * An HTML element, [PopoverVirtualElement](https://mui.com/material-ui/react-popover/#virtual-element),\n   * or a function that returns either.\n   * It's used to set the position of the popover.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.func]), props => {\n    if (props.open && (!props.anchorReference || props.anchorReference === 'anchorEl')) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', `It should be an Element or PopoverVirtualElement instance but it's \\`${resolvedAnchorEl}\\` instead.`].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * This is the point on the anchor where the popover's\n   * `anchorEl` will attach to. This is not used when the\n   * anchorReference is 'anchorPosition'.\n   *\n   * Options:\n   * vertical: [top, center, bottom];\n   * horizontal: [left, center, right].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * This is the position that may be used to set the position of the popover.\n   * The coordinates are relative to the application's client area.\n   */\n  anchorPosition: PropTypes.shape({\n    left: PropTypes.number.isRequired,\n    top: PropTypes.number.isRequired\n  }),\n  /**\n   * This determines which anchor prop to refer to when setting\n   * the position of the popover.\n   * @default 'anchorEl'\n   */\n  anchorReference: PropTypes.oneOf(['anchorEl', 'anchorPosition', 'none']),\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * An HTML element, component instance, or function that returns either.\n   * The `container` will passed to the Modal component.\n   *\n   * By default, it uses the body of the anchorEl's top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * The elevation of the popover.\n   * @default 8\n   */\n  elevation: integerPropType,\n  /**\n   * Specifies how close to the edge of the window the popover can appear.\n   * If null, the popover will not be constrained by the window.\n   * @default 16\n   */\n  marginThreshold: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Props applied to the [`Paper`](https://mui.com/material-ui/api/paper/) element.\n   *\n   * This prop is an alias for `slotProps.paper` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.paper` instead.\n   *\n   * @default {}\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * This is the point on the popover which\n   * will attach to the anchor's origin.\n   *\n   * Options:\n   * vertical: [top, center, bottom, x(px)];\n   * horizontal: [left, center, right, x(px)].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  transformOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @deprecated use the `slots.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Popover;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC3C,IAAIC,MAAM,GAAG,CAAC;EACd,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGD,QAAQ;EACnB,CAAC,MAAM,IAAIA,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGF,IAAI,CAACG,MAAM,GAAG,CAAC;EAC1B,CAAC,MAAM,IAAIF,QAAQ,KAAK,QAAQ,EAAE;IAChCC,MAAM,GAAGF,IAAI,CAACG,MAAM;EACtB;EACA,OAAOD,MAAM;AACf;AACA,OAAO,SAASE,aAAaA,CAACJ,IAAI,EAAEK,UAAU,EAAE;EAC9C,IAAIH,MAAM,GAAG,CAAC;EACd,IAAI,OAAOG,UAAU,KAAK,QAAQ,EAAE;IAClCH,MAAM,GAAGG,UAAU;EACrB,CAAC,MAAM,IAAIA,UAAU,KAAK,QAAQ,EAAE;IAClCH,MAAM,GAAGF,IAAI,CAACM,KAAK,GAAG,CAAC;EACzB,CAAC,MAAM,IAAID,UAAU,KAAK,OAAO,EAAE;IACjCH,MAAM,GAAGF,IAAI,CAACM,KAAK;EACrB;EACA,OAAOJ,MAAM;AACf;AACA,SAASK,uBAAuBA,CAACC,eAAe,EAAE;EAChD,OAAO,CAACA,eAAe,CAACH,UAAU,EAAEG,eAAe,CAACP,QAAQ,CAAC,CAACQ,GAAG,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,GAAG,GAAGA,CAAC,IAAI,GAAGA,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AACxH;AACA,SAASC,eAAeA,CAACC,QAAQ,EAAE;EACjC,OAAO,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC,CAAC,GAAGA,QAAQ;AAC/D;AACA,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAOxC,cAAc,CAACsC,KAAK,EAAEvB,sBAAsB,EAAEsB,OAAO,CAAC;AAC/D,CAAC;AACD,OAAO,MAAMI,WAAW,GAAGlC,MAAM,CAACM,KAAK,EAAE;EACvC6B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,OAAO,MAAMC,YAAY,GAAGrC,MAAM,CAACO,SAAS,EAAE;EAC5C4B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDE,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,QAAQ;EACnB;EACA;EACAC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,EAAE;EACbC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,mBAAmB;EAC9B;EACAC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMC,OAAO,GAAG,aAAaxD,KAAK,CAACyD,UAAU,CAAC,SAASD,OAAOA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3E,MAAMC,KAAK,GAAGjD,eAAe,CAAC;IAC5BiD,KAAK,EAAEF,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJgB,MAAM;IACNxB,QAAQ;IACRyB,YAAY,GAAG;MACbrC,QAAQ,EAAE,KAAK;MACfI,UAAU,EAAE;IACd,CAAC;IACDkC,cAAc;IACdC,eAAe,GAAG,UAAU;IAC5BC,QAAQ;IACRC,SAAS;IACTC,SAAS,EAAEC,aAAa;IACxBC,SAAS,GAAG,CAAC;IACbC,eAAe,GAAG,EAAE;IACpBC,IAAI;IACJC,UAAU,EAAEC,cAAc,GAAG,CAAC,CAAC;IAC/B;IACAhC,KAAK,GAAG,CAAC,CAAC;IACViC,SAAS,GAAG,CAAC,CAAC;IACd1C,eAAe,GAAG;MAChBP,QAAQ,EAAE,KAAK;MACfI,UAAU,EAAE;IACd,CAAC;IACD8C,mBAAmB;IACnB;IACAC,kBAAkB,EAAEC,sBAAsB,GAAG,MAAM;IACnDC,eAAe,GAAG,CAAC,CAAC;IACpB;IACAC,iBAAiB,GAAG,KAAK;IACzB,GAAGC;EACL,CAAC,GAAGpB,KAAK;EACT,MAAMqB,QAAQ,GAAGjF,KAAK,CAACkF,MAAM,CAAC,CAAC;EAC/B,MAAM3C,UAAU,GAAG;IACjB,GAAGqB,KAAK;IACRE,YAAY;IACZE,eAAe;IACfK,SAAS;IACTC,eAAe;IACftC,eAAe;IACf2C,mBAAmB;IACnBC,kBAAkB,EAAEC,sBAAsB;IAC1CC;EACF,CAAC;EACD,MAAMtC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;;EAE7C;EACA;EACA,MAAM4C,eAAe,GAAGnF,KAAK,CAACoF,WAAW,CAAC,MAAM;IAC9C,IAAIpB,eAAe,KAAK,gBAAgB,EAAE;MACxC,IAAIqB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,CAACxB,cAAc,EAAE;UACnByB,OAAO,CAACC,KAAK,CAAC,8DAA8D,GAAG,+CAA+C,CAAC;QACjI;MACF;MACA,OAAO1B,cAAc;IACvB;IACA,MAAM2B,gBAAgB,GAAGtD,eAAe,CAACC,QAAQ,CAAC;;IAElD;IACA,MAAMsD,aAAa,GAAGD,gBAAgB,IAAIA,gBAAgB,CAACE,QAAQ,KAAK,CAAC,GAAGF,gBAAgB,GAAG7E,aAAa,CAACoE,QAAQ,CAACY,OAAO,CAAC,CAACC,IAAI;IACnI,MAAMC,UAAU,GAAGJ,aAAa,CAACK,qBAAqB,CAAC,CAAC;IACxD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMU,GAAG,GAAGN,aAAa,CAACK,qBAAqB,CAAC,CAAC;MACjD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIU,GAAG,CAACC,GAAG,KAAK,CAAC,IAAID,GAAG,CAACE,IAAI,KAAK,CAAC,IAAIF,GAAG,CAACG,KAAK,KAAK,CAAC,IAAIH,GAAG,CAACI,MAAM,KAAK,CAAC,EAAE;QAC7Gb,OAAO,CAACc,IAAI,CAAC,CAAC,gEAAgE,EAAE,2DAA2D,EAAE,iFAAiF,CAAC,CAACnE,IAAI,CAAC,IAAI,CAAC,CAAC;MAC7O;IACF;IACA,OAAO;MACL+D,GAAG,EAAEH,UAAU,CAACG,GAAG,GAAG3E,YAAY,CAACwE,UAAU,EAAEjC,YAAY,CAACrC,QAAQ,CAAC;MACrE0E,IAAI,EAAEJ,UAAU,CAACI,IAAI,GAAGvE,aAAa,CAACmE,UAAU,EAAEjC,YAAY,CAACjC,UAAU;IAC3E,CAAC;EACH,CAAC,EAAE,CAACQ,QAAQ,EAAEyB,YAAY,CAACjC,UAAU,EAAEiC,YAAY,CAACrC,QAAQ,EAAEsC,cAAc,EAAEC,eAAe,CAAC,CAAC;;EAE/F;EACA,MAAMuC,kBAAkB,GAAGvG,KAAK,CAACoF,WAAW,CAACoB,QAAQ,IAAI;IACvD,OAAO;MACL/E,QAAQ,EAAEF,YAAY,CAACiF,QAAQ,EAAExE,eAAe,CAACP,QAAQ,CAAC;MAC1DI,UAAU,EAAED,aAAa,CAAC4E,QAAQ,EAAExE,eAAe,CAACH,UAAU;IAChE,CAAC;EACH,CAAC,EAAE,CAACG,eAAe,CAACH,UAAU,EAAEG,eAAe,CAACP,QAAQ,CAAC,CAAC;EAC1D,MAAMgF,mBAAmB,GAAGzG,KAAK,CAACoF,WAAW,CAACsB,OAAO,IAAI;IACvD,MAAMF,QAAQ,GAAG;MACf1E,KAAK,EAAE4E,OAAO,CAACC,WAAW;MAC1BhF,MAAM,EAAE+E,OAAO,CAACE;IAClB,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAGN,kBAAkB,CAACC,QAAQ,CAAC;IACxD,IAAIxC,eAAe,KAAK,MAAM,EAAE;MAC9B,OAAO;QACLkC,GAAG,EAAE,IAAI;QACTC,IAAI,EAAE,IAAI;QACVnE,eAAe,EAAED,uBAAuB,CAAC8E,mBAAmB;MAC9D,CAAC;IACH;;IAEA;IACA,MAAMC,YAAY,GAAG3B,eAAe,CAAC,CAAC;;IAEtC;IACA,IAAIe,GAAG,GAAGY,YAAY,CAACZ,GAAG,GAAGW,mBAAmB,CAACpF,QAAQ;IACzD,IAAI0E,IAAI,GAAGW,YAAY,CAACX,IAAI,GAAGU,mBAAmB,CAAChF,UAAU;IAC7D,MAAMwE,MAAM,GAAGH,GAAG,GAAGM,QAAQ,CAAC7E,MAAM;IACpC,MAAMyE,KAAK,GAAGD,IAAI,GAAGK,QAAQ,CAAC1E,KAAK;;IAEnC;IACA,MAAMiF,eAAe,GAAGjG,WAAW,CAACsB,eAAe,CAACC,QAAQ,CAAC,CAAC;;IAE9D;IACA,MAAM2E,eAAe,GAAGD,eAAe,CAACE,WAAW,GAAG3C,eAAe;IACrE,MAAM4C,cAAc,GAAGH,eAAe,CAACI,UAAU,GAAG7C,eAAe;;IAEnE;IACA,IAAIA,eAAe,KAAK,IAAI,IAAI4B,GAAG,GAAG5B,eAAe,EAAE;MACrD,MAAM8C,IAAI,GAAGlB,GAAG,GAAG5B,eAAe;MAClC4B,GAAG,IAAIkB,IAAI;MACXP,mBAAmB,CAACpF,QAAQ,IAAI2F,IAAI;IACtC,CAAC,MAAM,IAAI9C,eAAe,KAAK,IAAI,IAAI+B,MAAM,GAAGW,eAAe,EAAE;MAC/D,MAAMI,IAAI,GAAGf,MAAM,GAAGW,eAAe;MACrCd,GAAG,IAAIkB,IAAI;MACXP,mBAAmB,CAACpF,QAAQ,IAAI2F,IAAI;IACtC;IACA,IAAI/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIiB,QAAQ,CAAC7E,MAAM,GAAGqF,eAAe,IAAIR,QAAQ,CAAC7E,MAAM,IAAIqF,eAAe,EAAE;QAC3ExB,OAAO,CAACC,KAAK,CAAC,CAAC,yCAAyC,EAAE,kDAAkDe,QAAQ,CAAC7E,MAAM,GAAGqF,eAAe,MAAM,EAAE,uEAAuE,CAAC,CAAC7E,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3O;IACF;;IAEA;IACA,IAAImC,eAAe,KAAK,IAAI,IAAI6B,IAAI,GAAG7B,eAAe,EAAE;MACtD,MAAM8C,IAAI,GAAGjB,IAAI,GAAG7B,eAAe;MACnC6B,IAAI,IAAIiB,IAAI;MACZP,mBAAmB,CAAChF,UAAU,IAAIuF,IAAI;IACxC,CAAC,MAAM,IAAIhB,KAAK,GAAGc,cAAc,EAAE;MACjC,MAAME,IAAI,GAAGhB,KAAK,GAAGc,cAAc;MACnCf,IAAI,IAAIiB,IAAI;MACZP,mBAAmB,CAAChF,UAAU,IAAIuF,IAAI;IACxC;IACA,OAAO;MACLlB,GAAG,EAAE,GAAGmB,IAAI,CAACC,KAAK,CAACpB,GAAG,CAAC,IAAI;MAC3BC,IAAI,EAAE,GAAGkB,IAAI,CAACC,KAAK,CAACnB,IAAI,CAAC,IAAI;MAC7BnE,eAAe,EAAED,uBAAuB,CAAC8E,mBAAmB;IAC9D,CAAC;EACH,CAAC,EAAE,CAACxE,QAAQ,EAAE2B,eAAe,EAAEmB,eAAe,EAAEoB,kBAAkB,EAAEjC,eAAe,CAAC,CAAC;EACrF,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGxH,KAAK,CAACyH,QAAQ,CAAClD,IAAI,CAAC;EAC5D,MAAMmD,oBAAoB,GAAG1H,KAAK,CAACoF,WAAW,CAAC,MAAM;IACnD,MAAMsB,OAAO,GAAGzB,QAAQ,CAACY,OAAO;IAChC,IAAI,CAACa,OAAO,EAAE;MACZ;IACF;IACA,MAAMiB,WAAW,GAAGlB,mBAAmB,CAACC,OAAO,CAAC;IAChD,IAAIiB,WAAW,CAACzB,GAAG,KAAK,IAAI,EAAE;MAC5BQ,OAAO,CAACkB,KAAK,CAACC,WAAW,CAAC,KAAK,EAAEF,WAAW,CAACzB,GAAG,CAAC;IACnD;IACA,IAAIyB,WAAW,CAACxB,IAAI,KAAK,IAAI,EAAE;MAC7BO,OAAO,CAACkB,KAAK,CAACzB,IAAI,GAAGwB,WAAW,CAACxB,IAAI;IACvC;IACAO,OAAO,CAACkB,KAAK,CAAC5F,eAAe,GAAG2F,WAAW,CAAC3F,eAAe;IAC3DwF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,CAACf,mBAAmB,CAAC,CAAC;EACzBzG,KAAK,CAAC8H,SAAS,CAAC,MAAM;IACpB,IAAI/C,iBAAiB,EAAE;MACrBgD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEN,oBAAoB,CAAC;IACzD;IACA,OAAO,MAAMK,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEP,oBAAoB,CAAC;EACzE,CAAC,EAAE,CAACrF,QAAQ,EAAE0C,iBAAiB,EAAE2C,oBAAoB,CAAC,CAAC;EACvD,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3BR,oBAAoB,CAAC,CAAC;EACxB,CAAC;EACD,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBX,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EACDxH,KAAK,CAAC8H,SAAS,CAAC,MAAM;IACpB,IAAIvD,IAAI,EAAE;MACRmD,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EACF1H,KAAK,CAACoI,mBAAmB,CAACvE,MAAM,EAAE,MAAMU,IAAI,GAAG;IAC7C8D,cAAc,EAAEA,CAAA,KAAM;MACpBX,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,GAAG,IAAI,EAAE,CAACnD,IAAI,EAAEmD,oBAAoB,CAAC,CAAC;EACvC1H,KAAK,CAAC8H,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvD,IAAI,EAAE;MACT,OAAO+D,SAAS;IAClB;IACA,MAAMC,YAAY,GAAG3H,QAAQ,CAAC,MAAM;MAClC8G,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC;IACF,MAAMX,eAAe,GAAGjG,WAAW,CAACsB,eAAe,CAACC,QAAQ,CAAC,CAAC;IAC9D0E,eAAe,CAACiB,gBAAgB,CAAC,QAAQ,EAAEO,YAAY,CAAC;IACxD,OAAO,MAAM;MACXA,YAAY,CAACC,KAAK,CAAC,CAAC;MACpBzB,eAAe,CAACkB,mBAAmB,CAAC,QAAQ,EAAEM,YAAY,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,CAAClG,QAAQ,EAAEkC,IAAI,EAAEmD,oBAAoB,CAAC,CAAC;EAC1C,IAAI9C,kBAAkB,GAAGC,sBAAsB;EAC/C,MAAM4D,sBAAsB,GAAG;IAC7BhG,KAAK,EAAE;MACLiG,UAAU,EAAE/D,mBAAmB;MAC/B,GAAGlC;IACL,CAAC;IACDiC,SAAS,EAAE;MACTgE,UAAU,EAAE5D,eAAe;MAC3BnC,KAAK,EAAE8B,cAAc;MACrB,GAAGC;IACL;EACF,CAAC;EACD,MAAM,CAACiE,cAAc,EAAEC,mBAAmB,CAAC,GAAGzH,OAAO,CAAC,YAAY,EAAE;IAClE0H,WAAW,EAAE9H,IAAI;IACjB0H,sBAAsB;IACtBlG,UAAU;IACVuG,YAAY,EAAEC,QAAQ,KAAK;MACzB,GAAGA,QAAQ;MACXC,UAAU,EAAEA,CAACtC,OAAO,EAAEuC,WAAW,KAAK;QACpCF,QAAQ,CAACC,UAAU,GAAGtC,OAAO,EAAEuC,WAAW,CAAC;QAC3Cf,cAAc,CAAC,CAAC;MAClB,CAAC;MACDgB,QAAQ,EAAExC,OAAO,IAAI;QACnBqC,QAAQ,CAACG,QAAQ,GAAGxC,OAAO,CAAC;QAC5ByB,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,CAAC;IACFgB,eAAe,EAAE;MACfC,MAAM,EAAE,IAAI;MACZC,EAAE,EAAE9E;IACN;EACF,CAAC,CAAC;EACF,IAAIM,sBAAsB,KAAK,MAAM,IAAI,CAAC8D,cAAc,CAACW,cAAc,EAAE;IACvE1E,kBAAkB,GAAG0D,SAAS;EAChC;;EAEA;EACA;EACA;EACA,MAAMnE,SAAS,GAAGC,aAAa,KAAK/B,QAAQ,GAAGxB,aAAa,CAACuB,eAAe,CAACC,QAAQ,CAAC,CAAC,CAACyD,IAAI,GAAGwC,SAAS,CAAC;EACzG,MAAM,CAACiB,QAAQ,EAAE;IACf9G,KAAK,EAAE+G,aAAa;IACpB9E,SAAS,EAAE+E,iBAAiB;IAC5B,GAAGC;EACL,CAAC,CAAC,GAAGvI,OAAO,CAAC,MAAM,EAAE;IACnBwC,GAAG;IACHkF,WAAW,EAAEjG,WAAW;IACxB6F,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGzD;IACL,CAAC;IACD2E,0BAA0B,EAAE,IAAI;IAChCR,eAAe,EAAE;MACf1G,KAAK,EAAE;QACLmH,QAAQ,EAAEnH,KAAK,CAACmH;MAClB,CAAC;MACDlF,SAAS,EAAE;QACTkF,QAAQ,EAAExI,cAAc,CAAC,OAAOsD,SAAS,CAACkF,QAAQ,KAAK,UAAU,GAAGlF,SAAS,CAACkF,QAAQ,CAACrH,UAAU,CAAC,GAAGmC,SAAS,CAACkF,QAAQ,EAAE;UACvHC,SAAS,EAAE;QACb,CAAC;MACH,CAAC;MACD1F,SAAS;MACTI;IACF,CAAC;IACDhC,UAAU;IACV2B,SAAS,EAAEhE,IAAI,CAACsC,OAAO,CAACE,IAAI,EAAEwB,SAAS;EACzC,CAAC,CAAC;EACF,MAAM,CAAC4F,SAAS,EAAEC,UAAU,CAAC,GAAG5I,OAAO,CAAC,OAAO,EAAE;IAC/CwC,GAAG,EAAEsB,QAAQ;IACbf,SAAS,EAAE1B,OAAO,CAACG,KAAK;IACxBkG,WAAW,EAAE9F,YAAY;IACzB0F,sBAAsB;IACtBkB,0BAA0B,EAAE,IAAI;IAChCR,eAAe,EAAE;MACf9E,SAAS;MACTuD,KAAK,EAAEL,YAAY,GAAGe,SAAS,GAAG;QAChC0B,OAAO,EAAE;MACX;IACF,CAAC;IACDzH;EACF,CAAC,CAAC;EACF,OAAO,aAAajB,IAAI,CAACiI,QAAQ,EAAE;IACjC,GAAGG,SAAS;IACZ,IAAI,CAACjJ,eAAe,CAAC8I,QAAQ,CAAC,IAAI;MAChC9G,KAAK,EAAE+G,aAAa;MACpB9E,SAAS,EAAE+E,iBAAiB;MAC5B1E;IACF,CAAC,CAAC;IACFd,QAAQ,EAAE,aAAa3C,IAAI,CAACqH,cAAc,EAAE;MAC1C,GAAGC,mBAAmB;MACtBqB,OAAO,EAAErF,kBAAkB;MAC3BX,QAAQ,EAAE,aAAa3C,IAAI,CAACwI,SAAS,EAAE;QACrC,GAAGC,UAAU;QACb9F,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/B,OAAO,CAAC0G,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACErG,MAAM,EAAExD,OAAO;EACf;AACF;AACA;AACA;AACA;EACEgC,QAAQ,EAAE7B,cAAc,CAACP,SAAS,CAACkK,SAAS,CAAC,CAAC/J,eAAe,EAAEH,SAAS,CAACmK,IAAI,CAAC,CAAC,EAAExG,KAAK,IAAI;IACxF,IAAIA,KAAK,CAACW,IAAI,KAAK,CAACX,KAAK,CAACI,eAAe,IAAIJ,KAAK,CAACI,eAAe,KAAK,UAAU,CAAC,EAAE;MAClF,MAAM0B,gBAAgB,GAAGtD,eAAe,CAACwB,KAAK,CAACvB,QAAQ,CAAC;MACxD,IAAIqD,gBAAgB,IAAIA,gBAAgB,CAACE,QAAQ,KAAK,CAAC,EAAE;QACvD,MAAMK,GAAG,GAAGP,gBAAgB,CAACM,qBAAqB,CAAC,CAAC;QACpD,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIU,GAAG,CAACC,GAAG,KAAK,CAAC,IAAID,GAAG,CAACE,IAAI,KAAK,CAAC,IAAIF,GAAG,CAACG,KAAK,KAAK,CAAC,IAAIH,GAAG,CAACI,MAAM,KAAK,CAAC,EAAE;UAC7G,OAAO,IAAIgE,KAAK,CAAC,CAAC,gEAAgE,EAAE,2DAA2D,EAAE,iFAAiF,CAAC,CAAClI,IAAI,CAAC,IAAI,CAAC,CAAC;QACjP;MACF,CAAC,MAAM;QACL,OAAO,IAAIkI,KAAK,CAAC,CAAC,gEAAgE,EAAE,wEAAwE3E,gBAAgB,aAAa,CAAC,CAACvD,IAAI,CAAC,IAAI,CAAC,CAAC;MACxM;IACF;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE2B,YAAY,EAAE7D,SAAS,CAACqK,KAAK,CAAC;IAC5BzI,UAAU,EAAE5B,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACsK,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAEtK,SAAS,CAACuK,MAAM,CAAC,CAAC,CAACC,UAAU;IAC5GhJ,QAAQ,EAAExB,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACsK,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAEtK,SAAS,CAACuK,MAAM,CAAC,CAAC,CAACC;EAClG,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE1G,cAAc,EAAE9D,SAAS,CAACqK,KAAK,CAAC;IAC9BnE,IAAI,EAAElG,SAAS,CAACuK,MAAM,CAACC,UAAU;IACjCvE,GAAG,EAAEjG,SAAS,CAACuK,MAAM,CAACC;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEzG,eAAe,EAAE/D,SAAS,CAACsK,KAAK,CAAC,CAAC,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;EACxE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,iBAAiB,EAAEzK,SAAS,CAAC4I,WAAW;EACxC;AACF;AACA;AACA;EACE8B,aAAa,EAAE1K,SAAS,CAAC2K,MAAM;EAC/B;AACF;AACA;EACE3G,QAAQ,EAAEhE,SAAS,CAAC4K,IAAI;EACxB;AACF;AACA;EACErI,OAAO,EAAEvC,SAAS,CAAC2K,MAAM;EACzB;AACF;AACA;EACE1G,SAAS,EAAEjE,SAAS,CAAC6K,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;EACE3G,SAAS,EAAElE,SAAS,CAAC,sCAAsCkK,SAAS,CAAC,CAAC/J,eAAe,EAAEH,SAAS,CAACmK,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;EACErF,iBAAiB,EAAE9E,SAAS,CAAC8K,IAAI;EACjC;AACF;AACA;AACA;EACE1G,SAAS,EAAE9D,eAAe;EAC1B;AACF;AACA;AACA;AACA;EACE+D,eAAe,EAAErE,SAAS,CAACuK,MAAM;EACjC;AACF;AACA;AACA;EACEQ,OAAO,EAAE/K,SAAS,CAACmK,IAAI;EACvB;AACF;AACA;EACE7F,IAAI,EAAEtE,SAAS,CAAC8K,IAAI,CAACN,UAAU;EAC/B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEjG,UAAU,EAAEvE,SAAS,CAAC,sCAAsCqK,KAAK,CAAC;IAChEW,SAAS,EAAE3K;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEoE,SAAS,EAAEzE,SAAS,CAACqK,KAAK,CAAC;IACzBV,QAAQ,EAAE3J,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACmK,IAAI,EAAEnK,SAAS,CAAC2K,MAAM,CAAC,CAAC;IACjEjI,KAAK,EAAE1C,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACmK,IAAI,EAAEnK,SAAS,CAAC2K,MAAM,CAAC,CAAC;IAC9DlI,IAAI,EAAEzC,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACmK,IAAI,EAAEnK,SAAS,CAAC2K,MAAM,CAAC,CAAC;IAC7DlC,UAAU,EAAEzI,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACmK,IAAI,EAAEnK,SAAS,CAAC2K,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnI,KAAK,EAAExC,SAAS,CAACqK,KAAK,CAAC;IACrBV,QAAQ,EAAE3J,SAAS,CAAC4I,WAAW;IAC/BlG,KAAK,EAAE1C,SAAS,CAAC4I,WAAW;IAC5BnG,IAAI,EAAEzC,SAAS,CAAC4I,WAAW;IAC3BH,UAAU,EAAEzI,SAAS,CAAC4I;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACEqC,EAAE,EAAEjL,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACkL,OAAO,CAAClL,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACmK,IAAI,EAAEnK,SAAS,CAAC2K,MAAM,EAAE3K,SAAS,CAAC8K,IAAI,CAAC,CAAC,CAAC,EAAE9K,SAAS,CAACmK,IAAI,EAAEnK,SAAS,CAAC2K,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE5I,eAAe,EAAE/B,SAAS,CAACqK,KAAK,CAAC;IAC/BzI,UAAU,EAAE5B,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACsK,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAEtK,SAAS,CAACuK,MAAM,CAAC,CAAC,CAACC,UAAU;IAC5GhJ,QAAQ,EAAExB,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACsK,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAEtK,SAAS,CAACuK,MAAM,CAAC,CAAC,CAACC;EAClG,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;EACE9F,mBAAmB,EAAE1E,SAAS,CAAC4I,WAAW;EAC1C;AACF;AACA;AACA;EACEjE,kBAAkB,EAAE3E,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACsK,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEtK,SAAS,CAACuK,MAAM,EAAEvK,SAAS,CAACqK,KAAK,CAAC;IACpGlB,MAAM,EAAEnJ,SAAS,CAACuK,MAAM;IACxBY,KAAK,EAAEnL,SAAS,CAACuK,MAAM;IACvBa,IAAI,EAAEpL,SAAS,CAACuK;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;AACA;EACE1F,eAAe,EAAE7E,SAAS,CAAC2K;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAepH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}