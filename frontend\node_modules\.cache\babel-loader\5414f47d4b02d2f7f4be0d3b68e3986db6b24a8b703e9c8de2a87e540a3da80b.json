{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\Logo.js\";\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Logo = ({\n  height = '40px',\n  width = 'auto',\n  variant = 'light',\n  // 'light' or 'dark'\n  linkTo = '/',\n  forHeader = false,\n  // Special handling for header\n  ...props\n}) => {\n  const logoStyle = {\n    height,\n    width,\n    objectFit: 'contain',\n    // Apply filter only for header on dark background\n    filter: forHeader ? 'brightness(0) invert(1)' : 'none'\n  };\n  const LogoImage = () => /*#__PURE__*/_jsxDEV(\"img\", {\n    src: \"/logo.png\",\n    alt: \"Lnk2Store\",\n    style: logoStyle,\n    onError: e => {\n      // Fallback to text if image fails to load\n      e.target.style.display = 'none';\n      e.target.nextSibling.style.display = 'block';\n    },\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n  const FallbackText = () => /*#__PURE__*/_jsxDEV(Box, {\n    component: \"span\",\n    sx: {\n      display: 'none',\n      fontSize: '1.5rem',\n      fontWeight: 'bold',\n      color: variant === 'light' ? 'white' : 'primary.main',\n      fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n    },\n    children: \"Lnk2Store\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n  if (linkTo) {\n    return /*#__PURE__*/_jsxDEV(Link, {\n      to: linkTo,\n      style: {\n        textDecoration: 'none',\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(LogoImage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FallbackText, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(LogoImage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FallbackText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_c = Logo;\nexport default Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");", "map": {"version": 3, "names": ["React", "Box", "Link", "jsxDEV", "_jsxDEV", "Logo", "height", "width", "variant", "linkTo", "for<PERSON><PERSON><PERSON>", "props", "logoStyle", "objectFit", "filter", "LogoImage", "src", "alt", "style", "onError", "e", "target", "display", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FallbackText", "component", "sx", "fontSize", "fontWeight", "color", "fontFamily", "children", "to", "textDecoration", "alignItems", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/Logo.js"], "sourcesContent": ["import React from 'react';\nimport { Box } from '@mui/material';\nimport { Link } from 'react-router-dom';\n\nconst Logo = ({\n  height = '40px',\n  width = 'auto',\n  variant = 'light', // 'light' or 'dark'\n  linkTo = '/',\n  forHeader = false, // Special handling for header\n  ...props\n}) => {\n  const logoStyle = {\n    height,\n    width,\n    objectFit: 'contain',\n    // Apply filter only for header on dark background\n    filter: forHeader ? 'brightness(0) invert(1)' : 'none'\n  };\n\n  const LogoImage = () => (\n    <img \n      src=\"/logo.png\" \n      alt=\"Lnk2Store\" \n      style={logoStyle}\n      onError={(e) => {\n        // Fallback to text if image fails to load\n        e.target.style.display = 'none';\n        e.target.nextSibling.style.display = 'block';\n      }}\n      {...props}\n    />\n  );\n\n  const FallbackText = () => (\n    <Box\n      component=\"span\"\n      sx={{\n        display: 'none',\n        fontSize: '1.5rem',\n        fontWeight: 'bold',\n        color: variant === 'light' ? 'white' : 'primary.main',\n        fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n      }}\n    >\n      Lnk2Store\n    </Box>\n  );\n\n  if (linkTo) {\n    return (\n      <Link \n        to={linkTo} \n        style={{ \n          textDecoration: 'none', \n          display: 'flex', \n          alignItems: 'center' \n        }}\n      >\n        <LogoImage />\n        <FallbackText />\n      </Link>\n    );\n  }\n\n  return (\n    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n      <LogoImage />\n      <FallbackText />\n    </Box>\n  );\n};\n\nexport default Logo;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAQ,eAAe;AACnC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,IAAI,GAAGA,CAAC;EACZC,MAAM,GAAG,MAAM;EACfC,KAAK,GAAG,MAAM;EACdC,OAAO,GAAG,OAAO;EAAE;EACnBC,MAAM,GAAG,GAAG;EACZC,SAAS,GAAG,KAAK;EAAE;EACnB,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,SAAS,GAAG;IAChBN,MAAM;IACNC,KAAK;IACLM,SAAS,EAAE,SAAS;IACpB;IACAC,MAAM,EAAEJ,SAAS,GAAG,yBAAyB,GAAG;EAClD,CAAC;EAED,MAAMK,SAAS,GAAGA,CAAA,kBAChBX,OAAA;IACEY,GAAG,EAAC,WAAW;IACfC,GAAG,EAAC,WAAW;IACfC,KAAK,EAAEN,SAAU;IACjBO,OAAO,EAAGC,CAAC,IAAK;MACd;MACAA,CAAC,CAACC,MAAM,CAACH,KAAK,CAACI,OAAO,GAAG,MAAM;MAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACL,KAAK,CAACI,OAAO,GAAG,OAAO;IAC9C,CAAE;IAAA,GACEX;EAAK;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACF;EAED,MAAMC,YAAY,GAAGA,CAAA,kBACnBxB,OAAA,CAACH,GAAG;IACF4B,SAAS,EAAC,MAAM;IAChBC,EAAE,EAAE;MACFR,OAAO,EAAE,MAAM;MACfS,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAEzB,OAAO,KAAK,OAAO,GAAG,OAAO,GAAG,cAAc;MACrD0B,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,EACH;EAED;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CACN;EAED,IAAIlB,MAAM,EAAE;IACV,oBACEL,OAAA,CAACF,IAAI;MACHkC,EAAE,EAAE3B,MAAO;MACXS,KAAK,EAAE;QACLmB,cAAc,EAAE,MAAM;QACtBf,OAAO,EAAE,MAAM;QACfgB,UAAU,EAAE;MACd,CAAE;MAAAH,QAAA,gBAEF/B,OAAA,CAACW,SAAS;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACbvB,OAAA,CAACwB,YAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEX;EAEA,oBACEvB,OAAA,CAACH,GAAG;IAAC6B,EAAE,EAAE;MAAER,OAAO,EAAE,MAAM;MAAEgB,UAAU,EAAE;IAAS,CAAE;IAAAH,QAAA,gBACjD/B,OAAA,CAACW,SAAS;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbvB,OAAA,CAACwB,YAAY;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAACY,EAAA,GAnEIlC,IAAI;AAqEV,eAAeA,IAAI;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}