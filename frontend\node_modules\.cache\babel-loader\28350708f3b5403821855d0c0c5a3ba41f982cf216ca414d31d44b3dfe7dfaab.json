{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Button, Container, Grid, Card, CardContent, CardActions } from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport ProductCard from '../components/ProductCard';\nimport { useApi } from '../hooks/useApi';\nimport { productsAPI } from '../services/api';\nimport Logo from '../components/Logo';\nimport VideoBackground from '../components/VideoBackground';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const {\n    data: products,\n    loading\n  } = useApi(() => productsAPI.getProducts());\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        color: 'white',\n        py: {\n          xs: 8,\n          md: 12\n        },\n        textAlign: 'center',\n        position: 'relative',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        component: \"video\",\n        autoPlay: true,\n        muted: true,\n        loop: true,\n        playsInline: true,\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover',\n          zIndex: 0,\n          opacity: 0.3\n        },\n        children: /*#__PURE__*/_jsxDEV(\"source\", {\n          src: \"/logo_video.mp4\",\n          type: \"video/mp4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        sx: {\n          position: 'relative',\n          zIndex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Logo, {\n            height: {\n              xs: '80px',\n              md: '120px'\n            },\n            linkTo: null\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h2\",\n          component: \"h1\",\n          sx: {\n            fontWeight: 700,\n            mb: 3,\n            fontSize: {\n              xs: '2rem',\n              md: '3.5rem'\n            }\n          },\n          children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A Lnk2Store\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            mb: 6,\n            opacity: 0.9,\n            fontSize: {\n              xs: '1.1rem',\n              md: '1.5rem'\n            },\n            maxWidth: '800px',\n            mx: 'auto'\n          },\n          children: \"\\u0645\\u0646\\u0635\\u0629 SaaS \\u0644\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0635\\u0641\\u062D\\u0627\\u062A \\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u0627\\u062D\\u062A\\u0631\\u0627\\u0641\\u064A\\u0629 \\u0645\\u0639 \\u0646\\u0638\\u0627\\u0645 \\u062C\\u0645\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), !isAuthenticated ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 3,\n            justifyContent: 'center',\n            flexDirection: {\n              xs: 'column',\n              sm: 'row'\n            },\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            component: Link,\n            to: \"/register\",\n            sx: {\n              bgcolor: 'white',\n              color: 'primary.main',\n              px: 4,\n              py: 1.5,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              '&:hover': {\n                bgcolor: 'grey.100'\n              }\n            },\n            children: \"\\u0627\\u0628\\u062F\\u0623 \\u0627\\u0644\\u0622\\u0646 \\u0645\\u062C\\u0627\\u0646\\u0627\\u064B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            component: Link,\n            to: \"/login\",\n            sx: {\n              borderColor: 'white',\n              color: 'white',\n              px: 4,\n              py: 1.5,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              '&:hover': {\n                borderColor: 'white',\n                bgcolor: 'rgba(255,255,255,0.1)'\n              }\n            },\n            children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"large\",\n          component: Link,\n          to: \"/dashboard\",\n          sx: {\n            bgcolor: 'white',\n            color: 'primary.main',\n            px: 4,\n            py: 1.5,\n            fontSize: '1.1rem',\n            fontWeight: 600\n          },\n          children: \"\\u0627\\u0646\\u062A\\u0642\\u0644 \\u0625\\u0644\\u0649 \\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          children: \"\\u0627\\u0644\\u0645\\u0632\\u0627\\u064A\\u0627 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83D\\uDCB0 \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u062F\\u0641\\u0639 \\u0644\\u0643\\u0644 \\u0637\\u0644\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u062F\\u0641\\u0639 \\u0641\\u0642\\u0637 \\u0645\\u0642\\u0627\\u0628\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u062A\\u064A \\u062A\\u0633\\u062A\\u0642\\u0628\\u0644\\u0647\\u0627. \\u0646\\u0638\\u0627\\u0645 \\u0639\\u0627\\u062F\\u0644 \\u0648\\u0634\\u0641\\u0627\\u0641.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83D\\uDCF1 \\u0625\\u0631\\u0633\\u0627\\u0644 \\u0641\\u0648\\u0631\\u064A \\u0644\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u0633\\u062A\\u0642\\u0628\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A \\u0645\\u0628\\u0627\\u0634\\u0631\\u0629 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628 \\u0641\\u0648\\u0631 \\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u0631\\u0635\\u064A\\u062F.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83C\\uDFA8 \\u0642\\u0648\\u0627\\u0644\\u0628 \\u062C\\u0627\\u0647\\u0632\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0645\\u0646 \\u0645\\u062C\\u0645\\u0648\\u0639\\u0629 \\u0642\\u0648\\u0627\\u0644\\u0628 \\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u0627\\u062D\\u062A\\u0631\\u0627\\u0641\\u064A\\u0629 \\u0642\\u0627\\u0628\\u0644\\u0629 \\u0644\\u0644\\u062A\\u062E\\u0635\\u064A\\u0635.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), products && products.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          children: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0645\\u064A\\u0632\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          sx: {\n            mt: 2\n          },\n          children: products.slice(0, 6).map(product => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(ProductCard, {\n              product: product\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this)\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            component: Link,\n            to: \"/products\",\n            children: \"\\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          bgcolor: 'grey.100',\n          p: 6,\n          borderRadius: 2,\n          textAlign: 'center',\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          children: \"\\u062C\\u0627\\u0647\\u0632 \\u0644\\u0644\\u0628\\u062F\\u0621\\u061F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mb: 3\n          },\n          children: \"\\u0627\\u0646\\u0636\\u0645 \\u0625\\u0644\\u0649 \\u0622\\u0644\\u0627\\u0641 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631 \\u0627\\u0644\\u0630\\u064A\\u0646 \\u064A\\u0633\\u062A\\u062E\\u062F\\u0645\\u0648\\u0646 Lnk2Store \\u0644\\u062A\\u0646\\u0645\\u064A\\u0629 \\u0623\\u0639\\u0645\\u0627\\u0644\\u0647\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"large\",\n          component: Link,\n          to: \"/register\",\n          children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628 \\u0645\\u062C\\u0627\\u0646\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"lBTdndtKRF+Sc2sQ09iVtGMQeZg=\", false, function () {\n  return [useAuth, useApi];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Link", "useAuth", "ProductCard", "useApi", "productsAPI", "Logo", "VideoBackground", "jsxDEV", "_jsxDEV", "HomePage", "_s", "isAuthenticated", "data", "products", "loading", "getProducts", "children", "sx", "background", "color", "py", "xs", "md", "textAlign", "position", "overflow", "component", "autoPlay", "muted", "loop", "playsInline", "top", "left", "width", "height", "objectFit", "zIndex", "opacity", "src", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "mb", "linkTo", "variant", "fontWeight", "fontSize", "mx", "display", "gap", "justifyContent", "flexDirection", "sm", "alignItems", "size", "to", "bgcolor", "px", "borderColor", "gutterBottom", "container", "spacing", "mt", "item", "length", "slice", "map", "product", "id", "p", "borderRadius", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON>,\n  <PERSON>po<PERSON>,\n  <PERSON>ton,\n  Container,\n  Grid,\n  Card,\n  CardContent,\n  CardActions\n} from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport ProductCard from '../components/ProductCard';\nimport { useApi } from '../hooks/useApi';\nimport { productsAPI } from '../services/api';\nimport Logo from '../components/Logo';\nimport VideoBackground from '../components/VideoBackground';\n\nconst HomePage = () => {\n  const { isAuthenticated } = useAuth();\n  const { data: products, loading } = useApi(() => productsAPI.getProducts());\n\n  return (\n    <Box>\n      {/* Hero Section */}\n      <Box\n        sx={{\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white',\n          py: { xs: 8, md: 12 },\n          textAlign: 'center',\n          position: 'relative',\n          overflow: 'hidden'\n        }}\n      >\n        {/* Background Video */}\n        <Box\n          component=\"video\"\n          autoPlay\n          muted\n          loop\n          playsInline\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            zIndex: 0,\n            opacity: 0.3\n          }}\n        >\n          <source src=\"/logo_video.mp4\" type=\"video/mp4\" />\n        </Box>\n\n        {/* Content */}\n        <Container maxWidth=\"lg\" sx={{ position: 'relative', zIndex: 1 }}>\n          <Box sx={{ mb: 4 }}>\n            <Logo\n              height={{ xs: '80px', md: '120px' }}\n              linkTo={null}\n            />\n          </Box>\n\n          <Typography\n            variant=\"h2\"\n            component=\"h1\"\n            sx={{\n              fontWeight: 700,\n              mb: 3,\n              fontSize: { xs: '2rem', md: '3.5rem' }\n            }}\n          >\n            مرحباً بك في Lnk2Store\n          </Typography>\n\n          <Typography\n            variant=\"h5\"\n            sx={{\n              mb: 6,\n              opacity: 0.9,\n              fontSize: { xs: '1.1rem', md: '1.5rem' },\n              maxWidth: '800px',\n              mx: 'auto'\n            }}\n          >\n            منصة SaaS لإنشاء صفحات تسويقية احترافية مع نظام جمع الطلبات\n          </Typography>\n\n          {!isAuthenticated ? (\n            <Box sx={{\n              display: 'flex',\n              gap: 3,\n              justifyContent: 'center',\n              flexDirection: { xs: 'column', sm: 'row' },\n              alignItems: 'center'\n            }}>\n              <Button\n                variant=\"contained\"\n                size=\"large\"\n                component={Link}\n                to=\"/register\"\n                sx={{\n                  bgcolor: 'white',\n                  color: 'primary.main',\n                  px: 4,\n                  py: 1.5,\n                  fontSize: '1.1rem',\n                  fontWeight: 600,\n                  '&:hover': {\n                    bgcolor: 'grey.100'\n                  }\n                }}\n              >\n                ابدأ الآن مجاناً\n              </Button>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                component={Link}\n                to=\"/login\"\n                sx={{\n                  borderColor: 'white',\n                  color: 'white',\n                  px: 4,\n                  py: 1.5,\n                  fontSize: '1.1rem',\n                  fontWeight: 600,\n                  '&:hover': {\n                    borderColor: 'white',\n                    bgcolor: 'rgba(255,255,255,0.1)'\n                  }\n                }}\n              >\n                تسجيل الدخول\n              </Button>\n            </Box>\n          ) : (\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              component={Link}\n              to=\"/dashboard\"\n              sx={{\n                bgcolor: 'white',\n                color: 'primary.main',\n                px: 4,\n                py: 1.5,\n                fontSize: '1.1rem',\n                fontWeight: 600\n              }}\n            >\n              انتقل إلى لوحة التحكم\n            </Button>\n          )}\n        </Container>\n      </Box>\n\n      <Container maxWidth=\"lg\">\n        {/* Features Section */}\n        <Box sx={{ mb: 8 }}>\n          <Typography variant=\"h3\" textAlign=\"center\" gutterBottom>\n            المزايا الرئيسية\n          </Typography>\n          <Grid container spacing={4} sx={{ mt: 2 }}>\n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    💰 نظام الدفع لكل طلب\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    ادفع فقط مقابل الطلبات التي تستقبلها. نظام عادل وشفاف.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    📱 إرسال فوري للواتساب\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    استقبل الطلبات مباشرة على الواتساب فور تأكيد الرصيد.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    🎨 قوالب جاهزة\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    اختر من مجموعة قوالب تسويقية احترافية قابلة للتخصيص.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Products Preview */}\n        {products && products.length > 0 && (\n          <Box sx={{ mb: 8 }}>\n            <Typography variant=\"h3\" textAlign=\"center\" gutterBottom>\n              منتجات مميزة\n            </Typography>\n            <Grid container spacing={3} sx={{ mt: 2 }}>\n              {products.slice(0, 6).map((product) => (\n                <Grid item xs={12} sm={6} md={4} key={product.id}>\n                  <ProductCard product={product} />\n                </Grid>\n              ))}\n            </Grid>\n            <Box sx={{ textAlign: 'center', mt: 4 }}>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                component={Link}\n                to=\"/products\"\n              >\n                عرض جميع المنتجات\n              </Button>\n            </Box>\n          </Box>\n        )}\n\n        {/* CTA Section */}\n        <Box\n          sx={{\n            bgcolor: 'grey.100',\n            p: 6,\n            borderRadius: 2,\n            textAlign: 'center',\n            mb: 4\n          }}\n        >\n          <Typography variant=\"h4\" gutterBottom>\n            جاهز للبدء؟\n          </Typography>\n          <Typography variant=\"body1\" sx={{ mb: 3 }}>\n            انضم إلى آلاف التجار الذين يستخدمون Lnk2Store لتنمية أعمالهم\n          </Typography>\n          {!isAuthenticated && (\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              component={Link}\n              to=\"/register\"\n            >\n              إنشاء حساب مجاني\n            </Button>\n          )}\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,QACN,eAAe;AACtB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,eAAe,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAgB,CAAC,GAAGV,OAAO,CAAC,CAAC;EACrC,MAAM;IAAEW,IAAI,EAAEC,QAAQ;IAAEC;EAAQ,CAAC,GAAGX,MAAM,CAAC,MAAMC,WAAW,CAACW,WAAW,CAAC,CAAC,CAAC;EAE3E,oBACEP,OAAA,CAAChB,GAAG;IAAAwB,QAAA,gBAEFR,OAAA,CAAChB,GAAG;MACFyB,EAAE,EAAE;QACFC,UAAU,EAAE,mDAAmD;QAC/DC,KAAK,EAAE,OAAO;QACdC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAG,CAAC;QACrBC,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,gBAGFR,OAAA,CAAChB,GAAG;QACFkC,SAAS,EAAC,OAAO;QACjBC,QAAQ;QACRC,KAAK;QACLC,IAAI;QACJC,WAAW;QACXb,EAAE,EAAE;UACFO,QAAQ,EAAE,UAAU;UACpBO,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,CAAC;UACTC,OAAO,EAAE;QACX,CAAE;QAAArB,QAAA,eAEFR,OAAA;UAAQ8B,GAAG,EAAC,iBAAiB;UAACC,IAAI,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eAGNnC,OAAA,CAACb,SAAS;QAACiD,QAAQ,EAAC,IAAI;QAAC3B,EAAE,EAAE;UAAEO,QAAQ,EAAE,UAAU;UAAEY,MAAM,EAAE;QAAE,CAAE;QAAApB,QAAA,gBAC/DR,OAAA,CAAChB,GAAG;UAACyB,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,eACjBR,OAAA,CAACH,IAAI;YACH6B,MAAM,EAAE;cAAEb,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAQ,CAAE;YACpCwB,MAAM,EAAE;UAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnC,OAAA,CAACf,UAAU;UACTsD,OAAO,EAAC,IAAI;UACZrB,SAAS,EAAC,IAAI;UACdT,EAAE,EAAE;YACF+B,UAAU,EAAE,GAAG;YACfH,EAAE,EAAE,CAAC;YACLI,QAAQ,EAAE;cAAE5B,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAS;UACvC,CAAE;UAAAN,QAAA,EACH;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbnC,OAAA,CAACf,UAAU;UACTsD,OAAO,EAAC,IAAI;UACZ9B,EAAE,EAAE;YACF4B,EAAE,EAAE,CAAC;YACLR,OAAO,EAAE,GAAG;YACZY,QAAQ,EAAE;cAAE5B,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAS,CAAC;YACxCsB,QAAQ,EAAE,OAAO;YACjBM,EAAE,EAAE;UACN,CAAE;UAAAlC,QAAA,EACH;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ,CAAChC,eAAe,gBACfH,OAAA,CAAChB,GAAG;UAACyB,EAAE,EAAE;YACPkC,OAAO,EAAE,MAAM;YACfC,GAAG,EAAE,CAAC;YACNC,cAAc,EAAE,QAAQ;YACxBC,aAAa,EAAE;cAAEjC,EAAE,EAAE,QAAQ;cAAEkC,EAAE,EAAE;YAAM,CAAC;YAC1CC,UAAU,EAAE;UACd,CAAE;UAAAxC,QAAA,gBACAR,OAAA,CAACd,MAAM;YACLqD,OAAO,EAAC,WAAW;YACnBU,IAAI,EAAC,OAAO;YACZ/B,SAAS,EAAE1B,IAAK;YAChB0D,EAAE,EAAC,WAAW;YACdzC,EAAE,EAAE;cACF0C,OAAO,EAAE,OAAO;cAChBxC,KAAK,EAAE,cAAc;cACrByC,EAAE,EAAE,CAAC;cACLxC,EAAE,EAAE,GAAG;cACP6B,QAAQ,EAAE,QAAQ;cAClBD,UAAU,EAAE,GAAG;cACf,SAAS,EAAE;gBACTW,OAAO,EAAE;cACX;YACF,CAAE;YAAA3C,QAAA,EACH;UAED;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnC,OAAA,CAACd,MAAM;YACLqD,OAAO,EAAC,UAAU;YAClBU,IAAI,EAAC,OAAO;YACZ/B,SAAS,EAAE1B,IAAK;YAChB0D,EAAE,EAAC,QAAQ;YACXzC,EAAE,EAAE;cACF4C,WAAW,EAAE,OAAO;cACpB1C,KAAK,EAAE,OAAO;cACdyC,EAAE,EAAE,CAAC;cACLxC,EAAE,EAAE,GAAG;cACP6B,QAAQ,EAAE,QAAQ;cAClBD,UAAU,EAAE,GAAG;cACf,SAAS,EAAE;gBACTa,WAAW,EAAE,OAAO;gBACpBF,OAAO,EAAE;cACX;YACF,CAAE;YAAA3C,QAAA,EACH;UAED;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENnC,OAAA,CAACd,MAAM;UACLqD,OAAO,EAAC,WAAW;UACnBU,IAAI,EAAC,OAAO;UACZ/B,SAAS,EAAE1B,IAAK;UAChB0D,EAAE,EAAC,YAAY;UACfzC,EAAE,EAAE;YACF0C,OAAO,EAAE,OAAO;YAChBxC,KAAK,EAAE,cAAc;YACrByC,EAAE,EAAE,CAAC;YACLxC,EAAE,EAAE,GAAG;YACP6B,QAAQ,EAAE,QAAQ;YAClBD,UAAU,EAAE;UACd,CAAE;UAAAhC,QAAA,EACH;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAENnC,OAAA,CAACb,SAAS;MAACiD,QAAQ,EAAC,IAAI;MAAA5B,QAAA,gBAEtBR,OAAA,CAAChB,GAAG;QAACyB,EAAE,EAAE;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACjBR,OAAA,CAACf,UAAU;UAACsD,OAAO,EAAC,IAAI;UAACxB,SAAS,EAAC,QAAQ;UAACuC,YAAY;UAAA9C,QAAA,EAAC;QAEzD;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACZ,IAAI;UAACmE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC/C,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,gBACxCR,OAAA,CAACZ,IAAI;YAACsE,IAAI;YAAC7C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAN,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACoB,EAAE,EAAE;gBAAEiB,MAAM,EAAE,MAAM;gBAAEX,SAAS,EAAE;cAAS,CAAE;cAAAP,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,IAAI;kBAACe,YAAY;kBAAC3C,KAAK,EAAC,SAAS;kBAAAH,QAAA,EAAC;gBAEtD;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,OAAO;kBAAA/B,QAAA,EAAC;gBAE5B;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPnC,OAAA,CAACZ,IAAI;YAACsE,IAAI;YAAC7C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAN,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACoB,EAAE,EAAE;gBAAEiB,MAAM,EAAE,MAAM;gBAAEX,SAAS,EAAE;cAAS,CAAE;cAAAP,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,IAAI;kBAACe,YAAY;kBAAC3C,KAAK,EAAC,SAAS;kBAAAH,QAAA,EAAC;gBAEtD;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,OAAO;kBAAA/B,QAAA,EAAC;gBAE5B;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPnC,OAAA,CAACZ,IAAI;YAACsE,IAAI;YAAC7C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAN,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACoB,EAAE,EAAE;gBAAEiB,MAAM,EAAE,MAAM;gBAAEX,SAAS,EAAE;cAAS,CAAE;cAAAP,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,IAAI;kBAACe,YAAY;kBAAC3C,KAAK,EAAC,SAAS;kBAAAH,QAAA,EAAC;gBAEtD;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,OAAO;kBAAA/B,QAAA,EAAC;gBAE5B;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGL9B,QAAQ,IAAIA,QAAQ,CAACsD,MAAM,GAAG,CAAC,iBAC9B3D,OAAA,CAAChB,GAAG;QAACyB,EAAE,EAAE;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACjBR,OAAA,CAACf,UAAU;UAACsD,OAAO,EAAC,IAAI;UAACxB,SAAS,EAAC,QAAQ;UAACuC,YAAY;UAAA9C,QAAA,EAAC;QAEzD;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACZ,IAAI;UAACmE,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC/C,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,EACvCH,QAAQ,CAACuD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,OAAO,iBAChC9D,OAAA,CAACZ,IAAI;YAACsE,IAAI;YAAC7C,EAAE,EAAE,EAAG;YAACkC,EAAE,EAAE,CAAE;YAACjC,EAAE,EAAE,CAAE;YAAAN,QAAA,eAC9BR,OAAA,CAACN,WAAW;cAACoE,OAAO,EAAEA;YAAQ;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADG2B,OAAO,CAACC,EAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE1C,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPnC,OAAA,CAAChB,GAAG;UAACyB,EAAE,EAAE;YAAEM,SAAS,EAAE,QAAQ;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,eACtCR,OAAA,CAACd,MAAM;YACLqD,OAAO,EAAC,UAAU;YAClBU,IAAI,EAAC,OAAO;YACZ/B,SAAS,EAAE1B,IAAK;YAChB0D,EAAE,EAAC,WAAW;YAAA1C,QAAA,EACf;UAED;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDnC,OAAA,CAAChB,GAAG;QACFyB,EAAE,EAAE;UACF0C,OAAO,EAAE,UAAU;UACnBa,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,CAAC;UACflD,SAAS,EAAE,QAAQ;UACnBsB,EAAE,EAAE;QACN,CAAE;QAAA7B,QAAA,gBAEFR,OAAA,CAACf,UAAU;UAACsD,OAAO,EAAC,IAAI;UAACe,YAAY;UAAA9C,QAAA,EAAC;QAEtC;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;UAACsD,OAAO,EAAC,OAAO;UAAC9B,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,EAAC;QAE3C;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ,CAAChC,eAAe,iBACfH,OAAA,CAACd,MAAM;UACLqD,OAAO,EAAC,WAAW;UACnBU,IAAI,EAAC,OAAO;UACZ/B,SAAS,EAAE1B,IAAK;UAChB0D,EAAE,EAAC,WAAW;UAAA1C,QAAA,EACf;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACjC,EAAA,CArPID,QAAQ;EAAA,QACgBR,OAAO,EACCE,MAAM;AAAA;AAAAuE,EAAA,GAFtCjE,QAAQ;AAuPd,eAAeA,QAAQ;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}