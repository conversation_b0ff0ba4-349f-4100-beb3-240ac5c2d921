import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  TextField,
  MenuItem,
  Pagination
} from '@mui/material';
import {
  People,
  ContactMail,
  AccountBalanceWallet,
  TrendingUp,
  Refresh,
  Search,
  FilterList
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { useApi } from '../hooks/useApi';
import { adminAPI } from '../services/adminAPI';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [usersPage, setUsersPage] = useState(1);
  const [leadsPage, setLeadsPage] = useState(1);
  const [transactionsPage, setTransactionsPage] = useState(1);
  const [usersFilter, setUsersFilter] = useState('all');
  const [leadsFilter, setLeadsFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // API calls
  const { data: overview, loading: overviewLoading, refetch: refetchOverview } = useApi(() => adminAPI.getOverview());
  const { data: charts, loading: chartsLoading } = useApi(() => adminAPI.getCharts(30));
  const { data: users, loading: usersLoading, refetch: refetchUsers } = useApi(() => 
    adminAPI.getUsers(usersPage, 20, searchQuery, usersFilter), [usersPage, usersFilter, searchQuery]
  );
  const { data: leads, loading: leadsLoading, refetch: refetchLeads } = useApi(() => 
    adminAPI.getLeads(leadsPage, 20, leadsFilter), [leadsPage, leadsFilter]
  );
  const { data: transactions, loading: transactionsLoading } = useApi(() => 
    adminAPI.getTransactions(transactionsPage, 20), [transactionsPage]
  );

  // Check if user is admin
  if (!user?.is_staff && !user?.is_superuser) {
    return (
      <Alert severity="error">
        ليس لديك صلاحية للوصول إلى لوحة الإدارة
      </Alert>
    );
  }

  const formatCurrency = (amount) => `${amount} ريال`;
  const formatDate = (dateString) => new Date(dateString).toLocaleDateString('ar-SA');

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleRefresh = () => {
    refetchOverview();
    refetchUsers();
    refetchLeads();
  };

  // Overview Cards Component
  const OverviewCards = () => (
    <Grid container spacing={3} sx={{ mb: 4 }}>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <People sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
            <Typography variant="h4">
              {overview?.users?.total || 0}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي المستخدمين
            </Typography>
            <Typography variant="caption" color="success.main">
              +{overview?.users?.new_today || 0} اليوم
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <ContactMail sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
            <Typography variant="h4">
              {overview?.leads?.total || 0}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي الطلبات
            </Typography>
            <Typography variant="caption" color="warning.main">
              {overview?.leads?.pending || 0} معلق
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <TrendingUp sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
            <Typography variant="h4">
              {formatCurrency(overview?.revenue?.total || 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي الإيرادات
            </Typography>
            <Typography variant="caption" color="success.main">
              +{formatCurrency(overview?.revenue?.today || 0)} اليوم
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <AccountBalanceWallet sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
            <Typography variant="h4">
              {formatCurrency(overview?.wallets?.total_balance || 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              رصيد المحافظ
            </Typography>
            <Typography variant="caption" color="error.main">
              {overview?.wallets?.low_balance_users || 0} رصيد منخفض
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // Users Table Component
  const UsersTable = () => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">المستخدمين</Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              size="small"
              placeholder="البحث..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: <Search />
              }}
            />
            <TextField
              select
              size="small"
              value={usersFilter}
              onChange={(e) => setUsersFilter(e.target.value)}
              sx={{ minWidth: 120 }}
            >
              <MenuItem value="all">الكل</MenuItem>
              <MenuItem value="active">نشط</MenuItem>
              <MenuItem value="low_balance">رصيد منخفض</MenuItem>
            </TextField>
          </Box>
        </Box>

        {usersLoading ? (
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>المستخدم</TableCell>
                    <TableCell>البريد الإلكتروني</TableCell>
                    <TableCell>نوع النشاط</TableCell>
                    <TableCell>رصيد المحفظة</TableCell>
                    <TableCell>عدد الطلبات</TableCell>
                    <TableCell>تاريخ التسجيل</TableCell>
                    <TableCell>الحالة</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users?.users?.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {user.username}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user.first_name} {user.last_name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.business_type || 'غير محدد'}</TableCell>
                      <TableCell>
                        <Typography 
                          color={user.wallet_balance < 50 ? 'error' : 'text.primary'}
                        >
                          {formatCurrency(user.wallet_balance)}
                        </Typography>
                      </TableCell>
                      <TableCell>{user.leads_count}</TableCell>
                      <TableCell>{formatDate(user.date_joined)}</TableCell>
                      <TableCell>
                        <Chip
                          label={user.is_active ? 'نشط' : 'غير نشط'}
                          color={user.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            {users?.pagination && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Pagination
                  count={users.pagination.total_pages}
                  page={usersPage}
                  onChange={(e, page) => setUsersPage(page)}
                />
              </Box>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );

  if (overviewLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4">
          لوحة إدارة النظام
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={handleRefresh}
        >
          تحديث البيانات
        </Button>
      </Box>

      <OverviewCards />

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="المستخدمين" />
          <Tab label="الطلبات" />
          <Tab label="المعاملات" />
        </Tabs>
      </Box>

      {activeTab === 0 && <UsersTable />}
      {activeTab === 1 && (
        <Typography variant="h6">جدول الطلبات - قيد التطوير</Typography>
      )}
      {activeTab === 2 && (
        <Typography variant="h6">جدول المعاملات - قيد التطوير</Typography>
      )}
    </Box>
  );
};

export default AdminDashboard;
