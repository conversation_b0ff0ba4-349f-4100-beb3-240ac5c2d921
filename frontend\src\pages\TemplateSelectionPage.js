import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Typography,
  Button,
  Chip,
  TextField,
  MenuItem,
  InputAdornment,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Search,
  Visibility,
  Add,
  Star,
  WorkspacePremium
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useApi, useAsyncOperation } from '../hooks/useApi';

// Create a mock templates API for now
const templatesAPI = {
  getTemplates: (params = {}) => {
    // This will be replaced with actual API call
    return Promise.resolve({
      data: [
        {
          id: 1,
          name: 'عرض منتج احترافي',
          description: 'قالب لعرض منتج واحد بشكل احترافي مع نموذج طلب',
          template_type: 'product_showcase',
          category: 'ecommerce',
          preview_image: '/template-previews/product-showcase.jpg',
          is_premium: false,
          usage_count: 150
        },
        {
          id: 2,
          name: 'صفحة خدمة احترافية',
          description: 'قالب لعرض خدمة مع تفاصيل شاملة ونموذج تواصل',
          template_type: 'service_landing',
          category: 'services',
          preview_image: '/template-previews/service-landing.jpg',
          is_premium: false,
          usage_count: 89
        },
        {
          id: 3,
          name: 'معرض أعمال إبداعي',
          description: 'قالب لعرض الأعمال والمشاريع بشكل جذاب',
          template_type: 'portfolio',
          category: 'services',
          preview_image: '/template-previews/portfolio.jpg',
          is_premium: true,
          usage_count: 45
        }
      ]
    });
  },
  getCategories: () => {
    return Promise.resolve({
      data: [
        { value: 'ecommerce', label: 'تجارة إلكترونية' },
        { value: 'services', label: 'خدمات' },
        { value: 'food', label: 'طعام ومشروبات' },
        { value: 'fashion', label: 'أزياء' },
        { value: 'technology', label: 'تكنولوجيا' },
        { value: 'health', label: 'صحة وجمال' }
      ]
    });
  }
};

const TemplateSelectionPage = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  const { data: templates, loading } = useApi(() => templatesAPI.getTemplates({
    search: searchQuery,
    category: selectedCategory
  }), [searchQuery, selectedCategory]);

  const { data: categories } = useApi(() => templatesAPI.getCategories());
  const { loading: creating } = useAsyncOperation();

  const handleSelectTemplate = (template) => {
    setSelectedTemplate(template);
    setPreviewOpen(true);
  };

  const handleUseTemplate = async () => {
    if (!selectedTemplate) return;

    // Navigate to template customization page
    navigate(`/templates/${selectedTemplate.id}/customize`);
  };

  const filteredTemplates = templates?.filter(template => {
    const matchesSearch = !searchQuery || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = !selectedCategory || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  }) || [];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        اختر قالب صفحتك التسويقية
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        اختر من مجموعة متنوعة من القوالب الاحترافية لإنشاء صفحتك التسويقية
      </Typography>

      {/* Filters */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            placeholder="البحث في القوالب..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              )
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            select
            label="التصنيف"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <MenuItem value="">جميع التصنيفات</MenuItem>
            {categories?.map((category) => (
              <MenuItem key={category.value} value={category.value}>
                {category.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>

      {/* Templates Grid */}
      {loading ? (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
        </Box>
      ) : filteredTemplates.length === 0 ? (
        <Alert severity="info">
          لا توجد قوالب تطابق معايير البحث
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {filteredTemplates.map((template) => (
            <Grid item xs={12} sm={6} md={4} key={template.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardMedia
                  component="img"
                  height="200"
                  image={template.preview_image || '/placeholder-template.jpg'}
                  alt={template.name}
                  sx={{ objectFit: 'cover' }}
                />
                
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Typography variant="h6" component="h3">
                      {template.name}
                    </Typography>
                    {template.is_premium && (
                      <Chip
                        icon={<WorkspacePremium />}
                        label="مميز"
                        color="warning"
                        size="small"
                      />
                    )}
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {template.description}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Chip
                      label={categories?.find(c => c.value === template.category)?.label || template.category}
                      size="small"
                      variant="outlined"
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Star fontSize="small" color="action" />
                      <Typography variant="caption" color="text.secondary">
                        {template.usage_count} استخدام
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
                
                <CardActions>
                  <Button
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => handleSelectTemplate(template)}
                  >
                    معاينة
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    startIcon={<Add />}
                    onClick={() => handleSelectTemplate(template)}
                  >
                    استخدام القالب
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          معاينة القالب: {selectedTemplate?.name}
        </DialogTitle>
        <DialogContent>
          {selectedTemplate && (
            <Box>
              <img
                src={selectedTemplate.preview_image || '/placeholder-template.jpg'}
                alt={selectedTemplate.name}
                style={{ width: '100%', height: 'auto', marginBottom: 16 }}
              />
              <Typography variant="body1" paragraph>
                {selectedTemplate.description}
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Chip
                  label={categories?.find(c => c.value === selectedTemplate.category)?.label}
                  size="small"
                />
                {selectedTemplate.is_premium && (
                  <Chip
                    icon={<WorkspacePremium />}
                    label="مميز"
                    color="warning"
                    size="small"
                  />
                )}
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                تم استخدام هذا القالب {selectedTemplate.usage_count} مرة
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>
            إغلاق
          </Button>
          <Button
            variant="contained"
            onClick={handleUseTemplate}
            disabled={creating}
            startIcon={creating ? <CircularProgress size={20} /> : <Add />}
          >
            {creating ? 'جاري الإنشاء...' : 'استخدام هذا القالب'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TemplateSelectionPage;
