from django.db import models
from django.core.mail import send_mail
from django.conf import settings
from accounts.models import User
from decimal import Decimal

class Wallet(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wallet')
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    low_balance_threshold = models.DecimalField(max_digits=10, decimal_places=2, default=50.00)
    last_low_balance_alert = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} Wallet - {self.balance} SAR"

    def is_low_balance(self):
        return self.balance < self.low_balance_threshold

    def can_deduct(self, amount):
        return self.balance >= amount

    def deduct(self, amount, description=""):
        if not self.can_deduct(amount):
            raise ValueError("Insufficient balance")

        self.balance -= amount
        self.save()

        # Create transaction record
        Transaction.objects.create(
            wallet=self,
            amount=-amount,
            description=description or f"Deduction of {amount} SAR"
        )

        # Check for low balance and send alert if needed
        self.check_and_send_low_balance_alert()

        return True

    def add_funds(self, amount, description=""):
        self.balance += amount
        self.save()

        # Create transaction record
        Transaction.objects.create(
            wallet=self,
            amount=amount,
            description=description or f"Added {amount} SAR"
        )

        return True

    def check_and_send_low_balance_alert(self):
        from django.utils import timezone
        from datetime import timedelta

        if self.is_low_balance():
            # Only send alert once per day
            if (not self.last_low_balance_alert or
                timezone.now() - self.last_low_balance_alert > timedelta(days=1)):

                self.send_low_balance_notification()
                self.last_low_balance_alert = timezone.now()
                self.save()

    def send_low_balance_notification(self):
        # TODO: Implement email/SMS notification
        # For now, just print to console
        print(f"Low balance alert for {self.user.username}: {self.balance} SAR")

class Transaction(models.Model):
    TRANSACTION_TYPES = [
        ('recharge', 'Recharge'),
        ('deduction', 'Deduction'),
        ('refund', 'Refund'),
        ('bonus', 'Bonus'),
    ]

    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE, related_name='transactions')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES, default='recharge')
    timestamp = models.DateTimeField(auto_now_add=True)
    description = models.CharField(max_length=200, blank=True)
    reference_id = models.CharField(max_length=100, blank=True)  # For payment gateway reference

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.wallet.user.username} - {self.amount} SAR - {self.transaction_type}"

    def save(self, *args, **kwargs):
        # Auto-determine transaction type based on amount
        if not self.transaction_type:
            if self.amount > 0:
                self.transaction_type = 'recharge'
            else:
                self.transaction_type = 'deduction'
        super().save(*args, **kwargs)