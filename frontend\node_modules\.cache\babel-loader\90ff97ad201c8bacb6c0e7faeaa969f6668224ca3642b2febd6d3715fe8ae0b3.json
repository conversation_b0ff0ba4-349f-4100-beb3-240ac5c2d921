{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from \"../Typography/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDialogTitleUtilityClass } from \"./dialogTitleClasses.js\";\nimport DialogContext from \"../Dialog/DialogContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDialogTitleUtilityClass, classes);\n};\nconst DialogTitleRoot = styled(Typography, {\n  name: 'MuiDialogTitle',\n  slot: 'Root'\n})({\n  padding: '16px 24px',\n  flex: '0 0 auto'\n});\nconst DialogTitle = /*#__PURE__*/React.forwardRef(function DialogTitle(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogTitle'\n  });\n  const {\n    className,\n    id: idProp,\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const {\n    titleId = idProp\n  } = React.useContext(DialogContext);\n  return /*#__PURE__*/_jsx(DialogTitleRoot, {\n    component: \"h2\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    variant: \"h6\",\n    id: idProp ?? titleId,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogTitle.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogTitle;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "Typography", "styled", "useDefaultProps", "getDialogTitleUtilityClass", "DialogContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "DialogTitleRoot", "name", "slot", "padding", "flex", "DialogTitle", "forwardRef", "inProps", "ref", "props", "className", "id", "idProp", "other", "titleId", "useContext", "component", "variant", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["D:/apps/lnk2store/frontend/node_modules/@mui/material/esm/DialogTitle/DialogTitle.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from \"../Typography/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDialogTitleUtilityClass } from \"./dialogTitleClasses.js\";\nimport DialogContext from \"../Dialog/DialogContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDialogTitleUtilityClass, classes);\n};\nconst DialogTitleRoot = styled(Typography, {\n  name: 'MuiDialogTitle',\n  slot: 'Root'\n})({\n  padding: '16px 24px',\n  flex: '0 0 auto'\n});\nconst DialogTitle = /*#__PURE__*/React.forwardRef(function DialogTitle(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogTitle'\n  });\n  const {\n    className,\n    id: idProp,\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const {\n    titleId = idProp\n  } = React.useContext(DialogContext);\n  return /*#__PURE__*/_jsx(DialogTitleRoot, {\n    component: \"h2\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    variant: \"h6\",\n    id: idProp ?? titleId,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogTitle.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogTitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,0BAA0B,QAAQ,yBAAyB;AACpE,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOZ,cAAc,CAACW,KAAK,EAAEP,0BAA0B,EAAEM,OAAO,CAAC;AACnE,CAAC;AACD,MAAMG,eAAe,GAAGX,MAAM,CAACD,UAAU,EAAE;EACzCa,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE,WAAW;EACpBC,IAAI,EAAE;AACR,CAAC,CAAC;AACF,MAAMC,WAAW,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMC,KAAK,GAAGnB,eAAe,CAAC;IAC5BmB,KAAK,EAAEF,OAAO;IACdN,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJS,SAAS;IACTC,EAAE,EAAEC,MAAM;IACV,GAAGC;EACL,CAAC,GAAGJ,KAAK;EACT,MAAMb,UAAU,GAAGa,KAAK;EACxB,MAAMZ,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM;IACJkB,OAAO,GAAGF;EACZ,CAAC,GAAG5B,KAAK,CAAC+B,UAAU,CAACvB,aAAa,CAAC;EACnC,OAAO,aAAaE,IAAI,CAACM,eAAe,EAAE;IACxCgB,SAAS,EAAE,IAAI;IACfN,SAAS,EAAExB,IAAI,CAACW,OAAO,CAACE,IAAI,EAAEW,SAAS,CAAC;IACxCd,UAAU,EAAEA,UAAU;IACtBY,GAAG,EAAEA,GAAG;IACRS,OAAO,EAAE,IAAI;IACbN,EAAE,EAAEC,MAAM,IAAIE,OAAO;IACrB,GAAGD;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,WAAW,CAACgB,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAErC,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;EACE1B,OAAO,EAAEZ,SAAS,CAACuC,MAAM;EACzB;AACF;AACA;EACEd,SAAS,EAAEzB,SAAS,CAACwC,MAAM;EAC3B;AACF;AACA;EACEd,EAAE,EAAE1B,SAAS,CAACwC,MAAM;EACpB;AACF;AACA;EACEC,EAAE,EAAEzC,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,OAAO,CAAC3C,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC4C,IAAI,EAAE5C,SAAS,CAACuC,MAAM,EAAEvC,SAAS,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE7C,SAAS,CAAC4C,IAAI,EAAE5C,SAAS,CAACuC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}