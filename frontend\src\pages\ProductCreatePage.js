import React, { useState } from 'react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Grid,
  Chip,

  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { Add, Delete, CloudUpload } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAsyncOperation } from '../hooks/useApi';
import { productsAPI } from '../services/api';

const ProductCreatePage = () => {
  const navigate = useNavigate();
  const { loading, error, execute } = useAsyncOperation();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    old_price: '',
    category: '',
    meta_title: '',
    meta_description: '',
    colors: [],
    sizes: []
  });
  
  const [newColor, setNewColor] = useState('');
  const [newSize, setNewSize] = useState('');
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  const categories = [
    { value: 'electronics', label: 'إلكترونيات' },
    { value: 'fashion', label: 'أزياء' },
    { value: 'home', label: 'منزل وحديقة' },
    { value: 'sports', label: 'رياضة' },
    { value: 'books', label: 'كتب' },
    { value: 'beauty', label: 'جمال وعناية' },
    { value: 'food', label: 'طعام ومشروبات' },
    { value: 'other', label: 'أخرى' }
  ];

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const addColor = () => {
    if (newColor.trim() && !formData.colors.includes(newColor.trim())) {
      setFormData({
        ...formData,
        colors: [...formData.colors, newColor.trim()]
      });
      setNewColor('');
    }
  };

  const removeColor = (colorToRemove) => {
    setFormData({
      ...formData,
      colors: formData.colors.filter(color => color !== colorToRemove)
    });
  };

  const addSize = () => {
    if (newSize.trim() && !formData.sizes.includes(newSize.trim())) {
      setFormData({
        ...formData,
        sizes: [...formData.sizes, newSize.trim()]
      });
      setNewSize('');
    }
  };

  const removeSize = (sizeToRemove) => {
    setFormData({
      ...formData,
      sizes: formData.sizes.filter(size => size !== sizeToRemove)
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Create FormData for file upload
    const submitData = new FormData();
    Object.keys(formData).forEach(key => {
      if (key === 'colors' || key === 'sizes') {
        submitData.append(key, JSON.stringify(formData[key]));
      } else {
        submitData.append(key, formData[key]);
      }
    });
    
    if (imageFile) {
      submitData.append('image', imageFile);
    }

    const result = await execute(() => productsAPI.createProduct(submitData));
    
    if (result.success) {
      navigate('/products');
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        إضافة منتج جديد
      </Typography>

      <Paper elevation={3} sx={{ p: 4, maxWidth: 800, mx: 'auto' }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                المعلومات الأساسية
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم المنتج *"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>التصنيف</InputLabel>
                <Select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  label="التصنيف"
                >
                  {categories.map((category) => (
                    <MenuItem key={category.value} value={category.value}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="وصف المنتج *"
                name="description"
                value={formData.description}
                onChange={handleChange}
                multiline
                rows={4}
                required
              />
            </Grid>

            {/* Pricing */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                التسعير
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="السعر الحالي (ريال) *"
                name="price"
                type="number"
                value={formData.price}
                onChange={handleChange}
                required
                inputProps={{ min: 0, step: 0.01 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="السعر القديم (ريال)"
                name="old_price"
                type="number"
                value={formData.old_price}
                onChange={handleChange}
                inputProps={{ min: 0, step: 0.01 }}
                helperText="اختياري - لإظهار الخصم"
              />
            </Grid>

            {/* Image Upload */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                صورة المنتج
              </Typography>
              <Button
                variant="outlined"
                component="label"
                startIcon={<CloudUpload />}
                sx={{ mb: 2 }}
              >
                رفع صورة
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handleImageChange}
                />
              </Button>
              {imagePreview && (
                <Box sx={{ mt: 2 }}>
                  <img
                    src={imagePreview}
                    alt="معاينة"
                    style={{ maxWidth: 200, maxHeight: 200, objectFit: 'cover' }}
                  />
                </Box>
              )}
            </Grid>

            {/* Colors */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                الألوان المتاحة
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <TextField
                  size="small"
                  label="إضافة لون"
                  value={newColor}
                  onChange={(e) => setNewColor(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addColor()}
                />
                <Button onClick={addColor} startIcon={<Add />}>
                  إضافة
                </Button>
              </Box>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {formData.colors.map((color, index) => (
                  <Chip
                    key={index}
                    label={color}
                    onDelete={() => removeColor(color)}
                    deleteIcon={<Delete />}
                  />
                ))}
              </Box>
            </Grid>

            {/* Sizes */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                المقاسات المتاحة
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <TextField
                  size="small"
                  label="إضافة مقاس"
                  value={newSize}
                  onChange={(e) => setNewSize(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addSize()}
                />
                <Button onClick={addSize} startIcon={<Add />}>
                  إضافة
                </Button>
              </Box>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {formData.sizes.map((size, index) => (
                  <Chip
                    key={index}
                    label={size}
                    onDelete={() => removeSize(size)}
                    deleteIcon={<Delete />}
                  />
                ))}
              </Box>
            </Grid>

            {/* SEO */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                تحسين محركات البحث (SEO)
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="عنوان الصفحة"
                name="meta_title"
                value={formData.meta_title}
                onChange={handleChange}
                helperText="60 حرف كحد أقصى"
                inputProps={{ maxLength: 60 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="وصف الصفحة"
                name="meta_description"
                value={formData.meta_description}
                onChange={handleChange}
                helperText="160 حرف كحد أقصى"
                inputProps={{ maxLength: 160 }}
              />
            </Grid>

            {/* Submit Buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/products')}
                  disabled={loading}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                  {loading ? 'جاري الحفظ...' : 'حفظ المنتج'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Box>
  );
};

export default ProductCreatePage;
