{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, Button, Box, Container } from '@mui/material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport Logo from './Logo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    await logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"static\",\n      sx: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          py: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Logo, {\n            height: \"45px\",\n            forHeader: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/dashboard\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/products\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/my-pages\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"\\u0635\\u0641\\u062D\\u0627\\u062A\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/templates\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"\\u0627\\u0644\\u0642\\u0648\\u0627\\u0644\\u0628\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/leads\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"\\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/wallet\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"\\u0627\\u0644\\u0645\\u062D\\u0641\\u0638\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this), ((user === null || user === void 0 ? void 0 : user.is_staff) || (user === null || user === void 0 ? void 0 : user.is_superuser)) && /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/admin\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            sx: {\n              fontWeight: 500,\n              bgcolor: 'rgba(255,255,255,0.1)',\n              '&:hover': {\n                bgcolor: 'rgba(255,255,255,0.2)'\n              }\n            },\n            children: [\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C (\", user === null || user === void 0 ? void 0 : user.username, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/login\",\n            sx: {\n              fontWeight: 500\n            },\n            children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            component: Link,\n            to: \"/register\",\n            sx: {\n              bgcolor: 'white',\n              color: 'primary.main',\n              fontWeight: 600,\n              '&:hover': {\n                bgcolor: 'grey.100'\n              }\n            },\n            children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: 'calc(100vh - 80px)'\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"B5aHiu91piX0w1CsOFDPXF/GbhU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "Container", "Link", "useNavigate", "useAuth", "Logo", "jsxDEV", "_jsxDEV", "Layout", "children", "_s", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "sx", "flexGrow", "position", "background", "boxShadow", "py", "height", "for<PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gap", "alignItems", "color", "component", "to", "fontWeight", "is_staff", "is_superuser", "onClick", "bgcolor", "username", "variant", "minHeight", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/Layout.js"], "sourcesContent": ["import React from 'react';\nimport { AppB<PERSON>, <PERSON><PERSON><PERSON>, Typography, Button, Box, Container } from '@mui/material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport Logo from './Logo';\n\nconst Layout = ({ children }) => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/');\n  };\n\n  return (\n    <Box sx={{ flexGrow: 1 }}>\n      <AppBar\n        position=\"static\"\n        sx={{\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n        }}\n      >\n        <Toolbar sx={{ py: 1 }}>\n          <Box sx={{ flexGrow: 1 }}>\n            <Logo height=\"45px\" forHeader={true} />\n          </Box>\n          \n          {isAuthenticated ? (\n            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>\n              <Button\n                color=\"inherit\"\n                component={Link}\n                to=\"/dashboard\"\n                sx={{ fontWeight: 500 }}\n              >\n                لوحة التحكم\n              </Button>\n              <Button\n                color=\"inherit\"\n                component={Link}\n                to=\"/products\"\n                sx={{ fontWeight: 500 }}\n              >\n                المنتجات\n              </Button>\n              <Button\n                color=\"inherit\"\n                component={Link}\n                to=\"/my-pages\"\n                sx={{ fontWeight: 500 }}\n              >\n                صفحاتي\n              </Button>\n              <Button\n                color=\"inherit\"\n                component={Link}\n                to=\"/templates\"\n                sx={{ fontWeight: 500 }}\n              >\n                القوالب\n              </Button>\n              <Button\n                color=\"inherit\"\n                component={Link}\n                to=\"/leads\"\n                sx={{ fontWeight: 500 }}\n              >\n                الطلبات\n              </Button>\n              <Button\n                color=\"inherit\"\n                component={Link}\n                to=\"/wallet\"\n                sx={{ fontWeight: 500 }}\n              >\n                المحفظة\n              </Button>\n              {(user?.is_staff || user?.is_superuser) && (\n                <Button\n                  color=\"inherit\"\n                  component={Link}\n                  to=\"/admin\"\n                  sx={{ fontWeight: 500 }}\n                >\n                  إدارة النظام\n                </Button>\n              )}\n              <Button\n                color=\"inherit\"\n                onClick={handleLogout}\n                sx={{\n                  fontWeight: 500,\n                  bgcolor: 'rgba(255,255,255,0.1)',\n                  '&:hover': {\n                    bgcolor: 'rgba(255,255,255,0.2)'\n                  }\n                }}\n              >\n                تسجيل الخروج ({user?.username})\n              </Button>\n            </Box>\n          ) : (\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button\n                color=\"inherit\"\n                component={Link}\n                to=\"/login\"\n                sx={{ fontWeight: 500 }}\n              >\n                تسجيل الدخول\n              </Button>\n              <Button\n                variant=\"contained\"\n                component={Link}\n                to=\"/register\"\n                sx={{\n                  bgcolor: 'white',\n                  color: 'primary.main',\n                  fontWeight: 600,\n                  '&:hover': {\n                    bgcolor: 'grey.100'\n                  }\n                }}\n              >\n                إنشاء حساب\n              </Button>\n            </Box>\n          )}\n        </Toolbar>\n      </AppBar>\n\n      <Box sx={{ minHeight: 'calc(100vh - 80px)' }}>\n        {children}\n      </Box>\n    </Box>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,eAAe;AACnF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,IAAI,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnD,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMF,MAAM,CAAC,CAAC;IACdC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEP,OAAA,CAACP,GAAG;IAACgB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE;IAAAR,QAAA,gBACvBF,OAAA,CAACX,MAAM;MACLsB,QAAQ,EAAC,QAAQ;MACjBF,EAAE,EAAE;QACFG,UAAU,EAAE,mDAAmD;QAC/DC,SAAS,EAAE;MACb,CAAE;MAAAX,QAAA,eAEFF,OAAA,CAACV,OAAO;QAACmB,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,gBACrBF,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAR,QAAA,eACvBF,OAAA,CAACF,IAAI;YAACiB,MAAM,EAAC,MAAM;YAACC,SAAS,EAAE;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EAELf,eAAe,gBACdL,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAArB,QAAA,gBACzDF,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfC,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,YAAY;YACfjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAzB,QAAA,EACzB;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpB,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfC,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,WAAW;YACdjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAzB,QAAA,EACzB;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpB,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfC,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,WAAW;YACdjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAzB,QAAA,EACzB;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpB,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfC,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,YAAY;YACfjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAzB,QAAA,EACzB;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpB,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfC,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,QAAQ;YACXjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAzB,QAAA,EACzB;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpB,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfC,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,SAAS;YACZjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAzB,QAAA,EACzB;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACR,CAAC,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,QAAQ,MAAIxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,YAAY,mBACpC7B,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfC,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,QAAQ;YACXjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAzB,QAAA,EACzB;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDpB,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfM,OAAO,EAAEtB,YAAa;YACtBC,EAAE,EAAE;cACFkB,UAAU,EAAE,GAAG;cACfI,OAAO,EAAE,uBAAuB;cAChC,SAAS,EAAE;gBACTA,OAAO,EAAE;cACX;YACF,CAAE;YAAA7B,QAAA,GACH,uEACe,EAACE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,QAAQ,EAAC,GAChC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENpB,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAApB,QAAA,gBACnCF,OAAA,CAACR,MAAM;YACLgC,KAAK,EAAC,SAAS;YACfC,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,QAAQ;YACXjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAAzB,QAAA,EACzB;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpB,OAAA,CAACR,MAAM;YACLyC,OAAO,EAAC,WAAW;YACnBR,SAAS,EAAE9B,IAAK;YAChB+B,EAAE,EAAC,WAAW;YACdjB,EAAE,EAAE;cACFsB,OAAO,EAAE,OAAO;cAChBP,KAAK,EAAE,cAAc;cACrBG,UAAU,EAAE,GAAG;cACf,SAAS,EAAE;gBACTI,OAAO,EAAE;cACX;YACF,CAAE;YAAA7B,QAAA,EACH;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAETpB,OAAA,CAACP,GAAG;MAACgB,EAAE,EAAE;QAAEyB,SAAS,EAAE;MAAqB,CAAE;MAAAhC,QAAA,EAC1CA;IAAQ;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CApIIF,MAAM;EAAA,QACgCJ,OAAO,EAChCD,WAAW;AAAA;AAAAuC,EAAA,GAFxBlC,MAAM;AAsIZ,eAAeA,MAAM;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}