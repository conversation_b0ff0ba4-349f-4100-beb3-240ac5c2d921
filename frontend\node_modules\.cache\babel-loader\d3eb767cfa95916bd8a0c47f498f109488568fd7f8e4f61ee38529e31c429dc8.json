{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport { createContainer } from '@mui/system';\nimport capitalize from \"../utils/capitalize.js\";\nimport styled from \"../styles/styled.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n    }\n  }),\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: 'MuiContainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;", "map": {"version": 3, "names": ["PropTypes", "createContainer", "capitalize", "styled", "useDefaultProps", "Container", "createStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "ownerState", "root", "String", "max<PERSON><PERSON><PERSON>", "fixed", "disableGutters", "useThemeProps", "inProps", "process", "env", "NODE_ENV", "propTypes", "children", "node", "classes", "object", "component", "elementType", "bool", "oneOfType", "oneOf", "string", "sx", "arrayOf", "func"], "sources": ["D:/apps/lnk2store/frontend/node_modules/@mui/material/esm/Container/Container.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createContainer } from '@mui/system';\nimport capitalize from \"../utils/capitalize.js\";\nimport styled from \"../styles/styled.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n    }\n  }),\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: 'MuiContainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,eAAe,QAAQ,aAAa;AAC7C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,eAAe,QAAQ,kCAAkC;AAClE,MAAMC,SAAS,GAAGJ,eAAe,CAAC;EAChCK,qBAAqB,EAAEH,MAAM,CAAC,KAAK,EAAE;IACnCI,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACpC,MAAM;QACJC;MACF,CAAC,GAAGF,KAAK;MACT,OAAO,CAACC,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC,WAAWT,UAAU,CAACY,MAAM,CAACF,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEH,UAAU,CAACI,KAAK,IAAIL,MAAM,CAACK,KAAK,EAAEJ,UAAU,CAACK,cAAc,IAAIN,MAAM,CAACM,cAAc,CAAC;IAC1K;EACF,CAAC,CAAC;EACFC,aAAa,EAAEC,OAAO,IAAIf,eAAe,CAAC;IACxCM,KAAK,EAAES,OAAO;IACdZ,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC;AACFa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjB,SAAS,CAACkB,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAExB,SAAS,CAACyB,IAAI;EACxB;AACF;AACA;EACEC,OAAO,EAAE1B,SAAS,CAAC2B,MAAM;EACzB;AACF;AACA;AACA;EACEC,SAAS,EAAE5B,SAAS,CAAC6B,WAAW;EAChC;AACF;AACA;AACA;EACEZ,cAAc,EAAEjB,SAAS,CAAC8B,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;EACEd,KAAK,EAAEhB,SAAS,CAAC8B,IAAI;EACrB;AACF;AACA;AACA;AACA;AACA;EACEf,QAAQ,EAAEf,SAAS,CAAC,sCAAsC+B,SAAS,CAAC,CAAC/B,SAAS,CAACgC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,EAAEhC,SAAS,CAACiC,MAAM,CAAC,CAAC;EAC/I;AACF;AACA;EACEC,EAAE,EAAElC,SAAS,CAAC+B,SAAS,CAAC,CAAC/B,SAAS,CAACmC,OAAO,CAACnC,SAAS,CAAC+B,SAAS,CAAC,CAAC/B,SAAS,CAACoC,IAAI,EAAEpC,SAAS,CAAC2B,MAAM,EAAE3B,SAAS,CAAC8B,IAAI,CAAC,CAAC,CAAC,EAAE9B,SAAS,CAACoC,IAAI,EAAEpC,SAAS,CAAC2B,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAetB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}