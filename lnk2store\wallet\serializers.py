from rest_framework import serializers
from .models import Wallet, Transaction
from decimal import Decimal


class TransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = ('id', 'amount', 'timestamp', 'description')


class WalletSerializer(serializers.ModelSerializer):
    recent_transactions = TransactionSerializer(many=True, read_only=True, source='transactions')
    
    class Meta:
        model = Wallet
        fields = ('id', 'balance', 'recent_transactions')
        read_only_fields = ('id', 'balance')


class RechargeSerializer(serializers.Serializer):
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=Decimal('10.00'))
    payment_method = serializers.ChoiceField(choices=[
        ('credit_card', 'بطاقة ائتمان'),
        ('bank_transfer', 'تحويل بنكي'),
        ('paypal', 'PayPal'),
    ], default='credit_card')

    def validate_amount(self, value):
        if value < Decimal('10.00'):
            raise serializers.ValidationError('الحد الأدنى للشحن هو 10 ريال')
        if value > Decimal('10000.00'):
            raise serializers.ValidationError('الحد الأقصى للشحن هو 10000 ريال')
        return value


class WalletStatsSerializer(serializers.Serializer):
    current_balance = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_recharged = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_spent = serializers.DecimalField(max_digits=10, decimal_places=2)
    this_month_spent = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_lead_cost = serializers.DecimalField(max_digits=10, decimal_places=2)
    transactions_count = serializers.IntegerField()
