{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\Logo.js\";\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Logo = ({\n  height = '40px',\n  width = 'auto',\n  variant = 'light',\n  // 'light' or 'dark'\n  linkTo = '/',\n  forHeader = false,\n  // Special handling for header\n  sx = {},\n  ...props\n}) => {\n  // Handle responsive height\n  const logoSx = {\n    height: typeof height === 'object' ? height : height,\n    width,\n    objectFit: 'contain',\n    // Apply filter only for header on dark background\n    filter: forHeader ? 'brightness(0) invert(1)' : 'none',\n    ...sx\n  };\n  const LogoImage = () => /*#__PURE__*/_jsxDEV(Box, {\n    component: \"img\",\n    src: \"/logo-transparent.svg\",\n    alt: \"Lnk2Store\",\n    sx: logoSx,\n    onError: e => {\n      // Fallback to PNG logo, then to text if both fail\n      if (e.target.src.includes('transparent.svg')) {\n        e.target.src = '/logo.png';\n      } else {\n        e.target.style.display = 'none';\n        e.target.nextSibling.style.display = 'block';\n      }\n    },\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n  const FallbackText = () => /*#__PURE__*/_jsxDEV(Box, {\n    component: \"span\",\n    sx: {\n      display: 'none',\n      fontSize: typeof height === 'object' ? {\n        xs: '1.2rem',\n        md: '1.8rem'\n      } : '1.5rem',\n      fontWeight: 'bold',\n      color: forHeader ? 'white' : 'primary.main',\n      fontFamily: '\"Cairo\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n    },\n    children: \"Lnk2Store\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n  if (linkTo) {\n    return /*#__PURE__*/_jsxDEV(Link, {\n      to: linkTo,\n      style: {\n        textDecoration: 'none',\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(LogoImage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FallbackText, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(LogoImage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FallbackText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_c = Logo;\nexport default Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");", "map": {"version": 3, "names": ["React", "Box", "Link", "jsxDEV", "_jsxDEV", "Logo", "height", "width", "variant", "linkTo", "for<PERSON><PERSON><PERSON>", "sx", "props", "logoSx", "objectFit", "filter", "LogoImage", "component", "src", "alt", "onError", "e", "target", "includes", "style", "display", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FallbackText", "fontSize", "xs", "md", "fontWeight", "color", "fontFamily", "children", "to", "textDecoration", "alignItems", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/Logo.js"], "sourcesContent": ["import React from 'react';\nimport { Box } from '@mui/material';\nimport { Link } from 'react-router-dom';\n\nconst Logo = ({\n  height = '40px',\n  width = 'auto',\n  variant = 'light', // 'light' or 'dark'\n  linkTo = '/',\n  forHeader = false, // Special handling for header\n  sx = {},\n  ...props\n}) => {\n  // Handle responsive height\n  const logoSx = {\n    height: typeof height === 'object' ? height : height,\n    width,\n    objectFit: 'contain',\n    // Apply filter only for header on dark background\n    filter: forHeader ? 'brightness(0) invert(1)' : 'none',\n    ...sx\n  };\n\n  const LogoImage = () => (\n    <Box\n      component=\"img\"\n      src=\"/logo-transparent.svg\"\n      alt=\"Lnk2Store\"\n      sx={logoSx}\n      onError={(e) => {\n        // Fallback to PNG logo, then to text if both fail\n        if (e.target.src.includes('transparent.svg')) {\n          e.target.src = '/logo.png';\n        } else {\n          e.target.style.display = 'none';\n          e.target.nextSibling.style.display = 'block';\n        }\n      }}\n      {...props}\n    />\n  );\n\n  const FallbackText = () => (\n    <Box\n      component=\"span\"\n      sx={{\n        display: 'none',\n        fontSize: typeof height === 'object' ? { xs: '1.2rem', md: '1.8rem' } : '1.5rem',\n        fontWeight: 'bold',\n        color: forHeader ? 'white' : 'primary.main',\n        fontFamily: '\"Cairo\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n      }}\n    >\n      Lnk2Store\n    </Box>\n  );\n\n  if (linkTo) {\n    return (\n      <Link \n        to={linkTo} \n        style={{ \n          textDecoration: 'none', \n          display: 'flex', \n          alignItems: 'center' \n        }}\n      >\n        <LogoImage />\n        <FallbackText />\n      </Link>\n    );\n  }\n\n  return (\n    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n      <LogoImage />\n      <FallbackText />\n    </Box>\n  );\n};\n\nexport default Logo;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAQ,eAAe;AACnC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,IAAI,GAAGA,CAAC;EACZC,MAAM,GAAG,MAAM;EACfC,KAAK,GAAG,MAAM;EACdC,OAAO,GAAG,OAAO;EAAE;EACnBC,MAAM,GAAG,GAAG;EACZC,SAAS,GAAG,KAAK;EAAE;EACnBC,EAAE,GAAG,CAAC,CAAC;EACP,GAAGC;AACL,CAAC,KAAK;EACJ;EACA,MAAMC,MAAM,GAAG;IACbP,MAAM,EAAE,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGA,MAAM;IACpDC,KAAK;IACLO,SAAS,EAAE,SAAS;IACpB;IACAC,MAAM,EAAEL,SAAS,GAAG,yBAAyB,GAAG,MAAM;IACtD,GAAGC;EACL,CAAC;EAED,MAAMK,SAAS,GAAGA,CAAA,kBAChBZ,OAAA,CAACH,GAAG;IACFgB,SAAS,EAAC,KAAK;IACfC,GAAG,EAAC,uBAAuB;IAC3BC,GAAG,EAAC,WAAW;IACfR,EAAE,EAAEE,MAAO;IACXO,OAAO,EAAGC,CAAC,IAAK;MACd;MACA,IAAIA,CAAC,CAACC,MAAM,CAACJ,GAAG,CAACK,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QAC5CF,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,WAAW;MAC5B,CAAC,MAAM;QACLG,CAAC,CAACC,MAAM,CAACE,KAAK,CAACC,OAAO,GAAG,MAAM;QAC/BJ,CAAC,CAACC,MAAM,CAACI,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,OAAO;MAC9C;IACF,CAAE;IAAA,GACEb;EAAK;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACF;EAED,MAAMC,YAAY,GAAGA,CAAA,kBACnB3B,OAAA,CAACH,GAAG;IACFgB,SAAS,EAAC,MAAM;IAChBN,EAAE,EAAE;MACFc,OAAO,EAAE,MAAM;MACfO,QAAQ,EAAE,OAAO1B,MAAM,KAAK,QAAQ,GAAG;QAAE2B,EAAE,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAS,CAAC,GAAG,QAAQ;MAChFC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE1B,SAAS,GAAG,OAAO,GAAG,cAAc;MAC3C2B,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,EACH;EAED;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CACN;EAED,IAAIrB,MAAM,EAAE;IACV,oBACEL,OAAA,CAACF,IAAI;MACHqC,EAAE,EAAE9B,MAAO;MACXe,KAAK,EAAE;QACLgB,cAAc,EAAE,MAAM;QACtBf,OAAO,EAAE,MAAM;QACfgB,UAAU,EAAE;MACd,CAAE;MAAAH,QAAA,gBAEFlC,OAAA,CAACY,SAAS;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACb1B,OAAA,CAAC2B,YAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEX;EAEA,oBACE1B,OAAA,CAACH,GAAG;IAACU,EAAE,EAAE;MAAEc,OAAO,EAAE,MAAM;MAAEgB,UAAU,EAAE;IAAS,CAAE;IAAAH,QAAA,gBACjDlC,OAAA,CAACY,SAAS;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACb1B,OAAA,CAAC2B,YAAY;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAACY,EAAA,GA3EIrC,IAAI;AA6EV,eAAeA,IAAI;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}