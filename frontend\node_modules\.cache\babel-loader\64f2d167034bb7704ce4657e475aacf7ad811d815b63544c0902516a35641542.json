{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport CancelIcon from \"../internal/svg-icons/Cancel.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport unsupportedProp from \"../utils/unsupportedProp.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport chipClasses, { getChipUtilityClass } from \"./chipClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    size,\n    color,\n    iconColor,\n    onDelete,\n    clickable,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, disabled && 'disabled', `size${capitalize(size)}`, `color${capitalize(color)}`, clickable && 'clickable', clickable && `clickableColor${capitalize(color)}`, onDelete && 'deletable', onDelete && `deletableColor${capitalize(color)}`, `${variant}${capitalize(color)}`],\n    label: ['label', `label${capitalize(size)}`],\n    avatar: ['avatar', `avatar${capitalize(size)}`, `avatarColor${capitalize(color)}`],\n    icon: ['icon', `icon${capitalize(size)}`, `iconColor${capitalize(iconColor)}`],\n    deleteIcon: ['deleteIcon', `deleteIcon${capitalize(size)}`, `deleteIconColor${capitalize(color)}`, `deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getChipUtilityClass, classes);\n};\nconst ChipRoot = styled('div', {\n  name: 'MuiChip',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      color,\n      iconColor,\n      clickable,\n      onDelete,\n      size,\n      variant\n    } = ownerState;\n    return [{\n      [`& .${chipClasses.avatar}`]: styles.avatar\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatar${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatarColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles.icon\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`icon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`iconColor${capitalize(iconColor)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles.deleteIcon\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIconColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n    }, styles.root, styles[`size${capitalize(size)}`], styles[`color${capitalize(color)}`], clickable && styles.clickable, clickable && color !== 'default' && styles[`clickableColor${capitalize(color)})`], onDelete && styles.deletable, onDelete && color !== 'default' && styles[`deletableColor${capitalize(color)}`], styles[variant], styles[`${variant}${capitalize(color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => {\n  const textColor = theme.palette.mode === 'light' ? theme.palette.grey[700] : theme.palette.grey[300];\n  return {\n    maxWidth: '100%',\n    fontFamily: theme.typography.fontFamily,\n    fontSize: theme.typography.pxToRem(13),\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: 32,\n    lineHeight: 1.5,\n    color: (theme.vars || theme).palette.text.primary,\n    backgroundColor: (theme.vars || theme).palette.action.selected,\n    borderRadius: 32 / 2,\n    whiteSpace: 'nowrap',\n    transition: theme.transitions.create(['background-color', 'box-shadow']),\n    // reset cursor explicitly in case ButtonBase is used\n    cursor: 'unset',\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    textDecoration: 'none',\n    border: 0,\n    // Remove `button` border\n    padding: 0,\n    // Remove `button` padding\n    verticalAlign: 'middle',\n    boxSizing: 'border-box',\n    [`&.${chipClasses.disabled}`]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`& .${chipClasses.avatar}`]: {\n      marginLeft: 5,\n      marginRight: -6,\n      width: 24,\n      height: 24,\n      color: theme.vars ? theme.vars.palette.Chip.defaultAvatarColor : textColor,\n      fontSize: theme.typography.pxToRem(12)\n    },\n    [`& .${chipClasses.avatarColorPrimary}`]: {\n      color: (theme.vars || theme).palette.primary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.primary.dark\n    },\n    [`& .${chipClasses.avatarColorSecondary}`]: {\n      color: (theme.vars || theme).palette.secondary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.secondary.dark\n    },\n    [`& .${chipClasses.avatarSmall}`]: {\n      marginLeft: 4,\n      marginRight: -4,\n      width: 18,\n      height: 18,\n      fontSize: theme.typography.pxToRem(10)\n    },\n    [`& .${chipClasses.icon}`]: {\n      marginLeft: 5,\n      marginRight: -6\n    },\n    [`& .${chipClasses.deleteIcon}`]: {\n      WebkitTapHighlightColor: 'transparent',\n      color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.26)` : alpha(theme.palette.text.primary, 0.26),\n      fontSize: 22,\n      cursor: 'pointer',\n      margin: '0 5px 0 -6px',\n      '&:hover': {\n        color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.4)` : alpha(theme.palette.text.primary, 0.4)\n      }\n    },\n    variants: [{\n      props: {\n        size: 'small'\n      },\n      style: {\n        height: 24,\n        [`& .${chipClasses.icon}`]: {\n          fontSize: 18,\n          marginLeft: 4,\n          marginRight: -4\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          fontSize: 16,\n          marginRight: 4,\n          marginLeft: -4\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['contrastText'])).map(([color]) => {\n      return {\n        props: {\n          color\n        },\n        style: {\n          backgroundColor: (theme.vars || theme).palette[color].main,\n          color: (theme.vars || theme).palette[color].contrastText,\n          [`& .${chipClasses.deleteIcon}`]: {\n            color: theme.vars ? `rgba(${theme.vars.palette[color].contrastTextChannel} / 0.7)` : alpha(theme.palette[color].contrastText, 0.7),\n            '&:hover, &:active': {\n              color: (theme.vars || theme).palette[color].contrastText\n            }\n          }\n        }\n      };\n    }), {\n      props: props => props.iconColor === props.color,\n      style: {\n        [`& .${chipClasses.icon}`]: {\n          color: theme.vars ? theme.vars.palette.Chip.defaultIconColor : textColor\n        }\n      }\n    }, {\n      props: props => props.iconColor === props.color && props.color !== 'default',\n      style: {\n        [`& .${chipClasses.icon}`]: {\n          color: 'inherit'\n        }\n      }\n    }, {\n      props: {\n        onDelete: true\n      },\n      style: {\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark'])).map(([color]) => {\n      return {\n        props: {\n          color,\n          onDelete: true\n        },\n        style: {\n          [`&.${chipClasses.focusVisible}`]: {\n            background: (theme.vars || theme).palette[color].dark\n          }\n        }\n      };\n    }), {\n      props: {\n        clickable: true\n      },\n      style: {\n        userSelect: 'none',\n        WebkitTapHighlightColor: 'transparent',\n        cursor: 'pointer',\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity)\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        },\n        '&:active': {\n          boxShadow: (theme.vars || theme).shadows[1]\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark'])).map(([color]) => ({\n      props: {\n        color,\n        clickable: true\n      },\n      style: {\n        [`&:hover, &.${chipClasses.focusVisible}`]: {\n          backgroundColor: (theme.vars || theme).palette[color].dark\n        }\n      }\n    })), {\n      props: {\n        variant: 'outlined'\n      },\n      style: {\n        backgroundColor: 'transparent',\n        border: theme.vars ? `1px solid ${theme.vars.palette.Chip.defaultBorder}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[700]}`,\n        [`&.${chipClasses.clickable}:hover`]: {\n          backgroundColor: (theme.vars || theme).palette.action.hover\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: (theme.vars || theme).palette.action.focus\n        },\n        [`& .${chipClasses.avatar}`]: {\n          marginLeft: 4\n        },\n        [`& .${chipClasses.avatarSmall}`]: {\n          marginLeft: 2\n        },\n        [`& .${chipClasses.icon}`]: {\n          marginLeft: 4\n        },\n        [`& .${chipClasses.iconSmall}`]: {\n          marginLeft: 2\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          marginRight: 5\n        },\n        [`& .${chipClasses.deleteIconSmall}`]: {\n          marginRight: 3\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()) // no need to check for mainChannel as it's calculated from main\n    .map(([color]) => ({\n      props: {\n        variant: 'outlined',\n        color\n      },\n      style: {\n        color: (theme.vars || theme).palette[color].main,\n        border: `1px solid ${theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.7)` : alpha(theme.palette[color].main, 0.7)}`,\n        [`&.${chipClasses.clickable}:hover`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity)\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette[color].main, theme.palette.action.focusOpacity)\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          color: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.7)` : alpha(theme.palette[color].main, 0.7),\n          '&:hover, &:active': {\n            color: (theme.vars || theme).palette[color].main\n          }\n        }\n      }\n    }))]\n  };\n}));\nconst ChipLabel = styled('span', {\n  name: 'MuiChip',\n  slot: 'Label',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      size\n    } = ownerState;\n    return [styles.label, styles[`label${capitalize(size)}`]];\n  }\n})({\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  paddingLeft: 12,\n  paddingRight: 12,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      variant: 'outlined'\n    },\n    style: {\n      paddingLeft: 11,\n      paddingRight: 11\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      paddingLeft: 8,\n      paddingRight: 8\n    }\n  }, {\n    props: {\n      size: 'small',\n      variant: 'outlined'\n    },\n    style: {\n      paddingLeft: 7,\n      paddingRight: 7\n    }\n  }]\n});\nfunction isDeleteKeyboardEvent(keyboardEvent) {\n  return keyboardEvent.key === 'Backspace' || keyboardEvent.key === 'Delete';\n}\n\n/**\n * Chips represent complex entities in small blocks, such as a contact.\n */\nconst Chip = /*#__PURE__*/React.forwardRef(function Chip(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiChip'\n  });\n  const {\n    avatar: avatarProp,\n    className,\n    clickable: clickableProp,\n    color = 'default',\n    component: ComponentProp,\n    deleteIcon: deleteIconProp,\n    disabled = false,\n    icon: iconProp,\n    label,\n    onClick,\n    onDelete,\n    onKeyDown,\n    onKeyUp,\n    size = 'medium',\n    variant = 'filled',\n    tabIndex,\n    skipFocusWhenDisabled = false,\n    // TODO v6: Rename to `focusableWhenDisabled`.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const chipRef = React.useRef(null);\n  const handleRef = useForkRef(chipRef, ref);\n  const handleDeleteIconClick = event => {\n    // Stop the event from bubbling up to the `Chip`\n    event.stopPropagation();\n    if (onDelete) {\n      onDelete(event);\n    }\n  };\n  const handleKeyDown = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target && isDeleteKeyboardEvent(event)) {\n      // Will be handled in keyUp, otherwise some browsers\n      // might init navigation\n      event.preventDefault();\n    }\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n  };\n  const handleKeyUp = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target) {\n      if (onDelete && isDeleteKeyboardEvent(event)) {\n        onDelete(event);\n      }\n    }\n    if (onKeyUp) {\n      onKeyUp(event);\n    }\n  };\n  const clickable = clickableProp !== false && onClick ? true : clickableProp;\n  const component = clickable || onDelete ? ButtonBase : ComponentProp || 'div';\n  const ownerState = {\n    ...props,\n    component,\n    disabled,\n    size,\n    color,\n    iconColor: /*#__PURE__*/React.isValidElement(iconProp) ? iconProp.props.color || color : color,\n    onDelete: !!onDelete,\n    clickable,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const moreProps = component === ButtonBase ? {\n    component: ComponentProp || 'div',\n    focusVisibleClassName: classes.focusVisible,\n    ...(onDelete && {\n      disableRipple: true\n    })\n  } : {};\n  let deleteIcon = null;\n  if (onDelete) {\n    deleteIcon = deleteIconProp && /*#__PURE__*/React.isValidElement(deleteIconProp) ? (/*#__PURE__*/React.cloneElement(deleteIconProp, {\n      className: clsx(deleteIconProp.props.className, classes.deleteIcon),\n      onClick: handleDeleteIconClick\n    })) : /*#__PURE__*/_jsx(CancelIcon, {\n      className: classes.deleteIcon,\n      onClick: handleDeleteIconClick\n    });\n  }\n  let avatar = null;\n  if (avatarProp && /*#__PURE__*/React.isValidElement(avatarProp)) {\n    avatar = /*#__PURE__*/React.cloneElement(avatarProp, {\n      className: clsx(classes.avatar, avatarProp.props.className)\n    });\n  }\n  let icon = null;\n  if (iconProp && /*#__PURE__*/React.isValidElement(iconProp)) {\n    icon = /*#__PURE__*/React.cloneElement(iconProp, {\n      className: clsx(classes.icon, iconProp.props.className)\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (avatar && icon) {\n      console.error('MUI: The Chip component can not handle the avatar ' + 'and the icon prop at the same time. Pick one.');\n    }\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: ChipRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    // The `component` prop is preserved because `Chip` relies on it for internal logic. If `shouldForwardComponentProp` were `false`, `useSlot` would remove the `component` prop, potentially breaking the component's behavior.\n    shouldForwardComponentProp: true,\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    additionalProps: {\n      disabled: clickable && disabled ? true : undefined,\n      tabIndex: skipFocusWhenDisabled && disabled ? -1 : tabIndex,\n      ...moreProps\n    },\n    getSlotProps: handlers => ({\n      ...handlers,\n      onClick: event => {\n        handlers.onClick?.(event);\n        onClick?.(event);\n      },\n      onKeyDown: event => {\n        handlers.onKeyDown?.(event);\n        handleKeyDown?.(event);\n      },\n      onKeyUp: event => {\n        handlers.onKeyUp?.(event);\n        handleKeyUp?.(event);\n      }\n    })\n  });\n  const [LabelSlot, labelProps] = useSlot('label', {\n    elementType: ChipLabel,\n    externalForwardedProps,\n    ownerState,\n    className: classes.label\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    as: component,\n    ...rootProps,\n    children: [avatar || icon, /*#__PURE__*/_jsx(LabelSlot, {\n      ...labelProps,\n      children: label\n    }), deleteIcon]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Chip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The Avatar element to display.\n   */\n  avatar: PropTypes.element,\n  /**\n   * This prop isn't supported.\n   * Use the `component` prop if you need to change the children structure.\n   */\n  children: unsupportedProp,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the chip will appear clickable, and will raise when pressed,\n   * even if the onClick prop is not defined.\n   * If `false`, the chip will not appear clickable, even if onClick prop is defined.\n   * This can be used, for example,\n   * along with the component prop to indicate an anchor Chip is clickable.\n   * Note: this controls the UI and does not affect the onClick event.\n   */\n  clickable: PropTypes.bool,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Override the default delete icon element. Shown only if `onDelete` is set.\n   */\n  deleteIcon: PropTypes.element,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Icon element.\n   */\n  icon: PropTypes.element,\n  /**\n   * The content of the component.\n   */\n  label: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * Callback fired when the delete icon is clicked.\n   * If set, the delete icon will be shown.\n   */\n  onDelete: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * If `true`, allows the disabled chip to escape focus.\n   * If `false`, allows the disabled chip to receive focus.\n   * @default false\n   */\n  skipFocusWhenDisabled: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    label: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'filled'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Chip;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "alpha", "CancelIcon", "useForkRef", "unsupportedProp", "capitalize", "ButtonBase", "styled", "memoTheme", "createSimplePaletteValueFilter", "useDefaultProps", "chipClasses", "getChipUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "disabled", "size", "color", "iconColor", "onDelete", "clickable", "variant", "slots", "root", "label", "avatar", "icon", "deleteIcon", "ChipRoot", "name", "slot", "overridesResolver", "props", "styles", "deletable", "theme", "textColor", "palette", "mode", "grey", "max<PERSON><PERSON><PERSON>", "fontFamily", "typography", "fontSize", "pxToRem", "display", "alignItems", "justifyContent", "height", "lineHeight", "vars", "text", "primary", "backgroundColor", "action", "selected", "borderRadius", "whiteSpace", "transition", "transitions", "create", "cursor", "outline", "textDecoration", "border", "padding", "verticalAlign", "boxSizing", "opacity", "disabledOpacity", "pointerEvents", "marginLeft", "marginRight", "width", "Chip", "defaultAvatarColor", "avatarColorPrimary", "contrastText", "dark", "avatarColorSecondary", "secondary", "avatar<PERSON><PERSON><PERSON>", "WebkitTapHighlightColor", "primaryChannel", "margin", "variants", "style", "Object", "entries", "filter", "map", "main", "contrastTextChannel", "defaultIconColor", "focusVisible", "selectedChannel", "selectedOpacity", "focusOpacity", "background", "userSelect", "hoverOpacity", "boxShadow", "shadows", "defaultBorder", "hover", "focus", "iconSmall", "deleteIconSmall", "mainChannel", "ChipLabel", "overflow", "textOverflow", "paddingLeft", "paddingRight", "isDeleteKeyboardEvent", "keyboardEvent", "key", "forwardRef", "inProps", "ref", "avatarProp", "className", "clickableProp", "component", "ComponentProp", "deleteIconProp", "iconProp", "onClick", "onKeyDown", "onKeyUp", "tabIndex", "skipFocusWhenDisabled", "slotProps", "other", "chipRef", "useRef", "handleRef", "handleDeleteIconClick", "event", "stopPropagation", "handleKeyDown", "currentTarget", "target", "preventDefault", "handleKeyUp", "isValidElement", "moreProps", "focusVisibleClassName", "disable<PERSON><PERSON><PERSON>", "cloneElement", "process", "env", "NODE_ENV", "console", "error", "externalForwardedProps", "RootSlot", "rootProps", "elementType", "shouldForwardComponentProp", "additionalProps", "undefined", "getSlotProps", "handlers", "LabelSlot", "labelProps", "as", "children", "propTypes", "element", "object", "string", "bool", "oneOfType", "oneOf", "node", "func", "shape", "sx", "arrayOf", "number"], "sources": ["D:/apps/lnk2store/frontend/node_modules/@mui/material/esm/Chip/Chip.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport CancelIcon from \"../internal/svg-icons/Cancel.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport unsupportedProp from \"../utils/unsupportedProp.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport chipClasses, { getChipUtilityClass } from \"./chipClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    size,\n    color,\n    iconColor,\n    onDelete,\n    clickable,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, disabled && 'disabled', `size${capitalize(size)}`, `color${capitalize(color)}`, clickable && 'clickable', clickable && `clickableColor${capitalize(color)}`, onDelete && 'deletable', onDelete && `deletableColor${capitalize(color)}`, `${variant}${capitalize(color)}`],\n    label: ['label', `label${capitalize(size)}`],\n    avatar: ['avatar', `avatar${capitalize(size)}`, `avatarColor${capitalize(color)}`],\n    icon: ['icon', `icon${capitalize(size)}`, `iconColor${capitalize(iconColor)}`],\n    deleteIcon: ['deleteIcon', `deleteIcon${capitalize(size)}`, `deleteIconColor${capitalize(color)}`, `deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getChipUtilityClass, classes);\n};\nconst ChipRoot = styled('div', {\n  name: 'MuiChip',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      color,\n      iconColor,\n      clickable,\n      onDelete,\n      size,\n      variant\n    } = ownerState;\n    return [{\n      [`& .${chipClasses.avatar}`]: styles.avatar\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatar${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatarColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles.icon\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`icon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`iconColor${capitalize(iconColor)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles.deleteIcon\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIconColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n    }, styles.root, styles[`size${capitalize(size)}`], styles[`color${capitalize(color)}`], clickable && styles.clickable, clickable && color !== 'default' && styles[`clickableColor${capitalize(color)})`], onDelete && styles.deletable, onDelete && color !== 'default' && styles[`deletableColor${capitalize(color)}`], styles[variant], styles[`${variant}${capitalize(color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => {\n  const textColor = theme.palette.mode === 'light' ? theme.palette.grey[700] : theme.palette.grey[300];\n  return {\n    maxWidth: '100%',\n    fontFamily: theme.typography.fontFamily,\n    fontSize: theme.typography.pxToRem(13),\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: 32,\n    lineHeight: 1.5,\n    color: (theme.vars || theme).palette.text.primary,\n    backgroundColor: (theme.vars || theme).palette.action.selected,\n    borderRadius: 32 / 2,\n    whiteSpace: 'nowrap',\n    transition: theme.transitions.create(['background-color', 'box-shadow']),\n    // reset cursor explicitly in case ButtonBase is used\n    cursor: 'unset',\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    textDecoration: 'none',\n    border: 0,\n    // Remove `button` border\n    padding: 0,\n    // Remove `button` padding\n    verticalAlign: 'middle',\n    boxSizing: 'border-box',\n    [`&.${chipClasses.disabled}`]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`& .${chipClasses.avatar}`]: {\n      marginLeft: 5,\n      marginRight: -6,\n      width: 24,\n      height: 24,\n      color: theme.vars ? theme.vars.palette.Chip.defaultAvatarColor : textColor,\n      fontSize: theme.typography.pxToRem(12)\n    },\n    [`& .${chipClasses.avatarColorPrimary}`]: {\n      color: (theme.vars || theme).palette.primary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.primary.dark\n    },\n    [`& .${chipClasses.avatarColorSecondary}`]: {\n      color: (theme.vars || theme).palette.secondary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.secondary.dark\n    },\n    [`& .${chipClasses.avatarSmall}`]: {\n      marginLeft: 4,\n      marginRight: -4,\n      width: 18,\n      height: 18,\n      fontSize: theme.typography.pxToRem(10)\n    },\n    [`& .${chipClasses.icon}`]: {\n      marginLeft: 5,\n      marginRight: -6\n    },\n    [`& .${chipClasses.deleteIcon}`]: {\n      WebkitTapHighlightColor: 'transparent',\n      color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.26)` : alpha(theme.palette.text.primary, 0.26),\n      fontSize: 22,\n      cursor: 'pointer',\n      margin: '0 5px 0 -6px',\n      '&:hover': {\n        color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.4)` : alpha(theme.palette.text.primary, 0.4)\n      }\n    },\n    variants: [{\n      props: {\n        size: 'small'\n      },\n      style: {\n        height: 24,\n        [`& .${chipClasses.icon}`]: {\n          fontSize: 18,\n          marginLeft: 4,\n          marginRight: -4\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          fontSize: 16,\n          marginRight: 4,\n          marginLeft: -4\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['contrastText'])).map(([color]) => {\n      return {\n        props: {\n          color\n        },\n        style: {\n          backgroundColor: (theme.vars || theme).palette[color].main,\n          color: (theme.vars || theme).palette[color].contrastText,\n          [`& .${chipClasses.deleteIcon}`]: {\n            color: theme.vars ? `rgba(${theme.vars.palette[color].contrastTextChannel} / 0.7)` : alpha(theme.palette[color].contrastText, 0.7),\n            '&:hover, &:active': {\n              color: (theme.vars || theme).palette[color].contrastText\n            }\n          }\n        }\n      };\n    }), {\n      props: props => props.iconColor === props.color,\n      style: {\n        [`& .${chipClasses.icon}`]: {\n          color: theme.vars ? theme.vars.palette.Chip.defaultIconColor : textColor\n        }\n      }\n    }, {\n      props: props => props.iconColor === props.color && props.color !== 'default',\n      style: {\n        [`& .${chipClasses.icon}`]: {\n          color: 'inherit'\n        }\n      }\n    }, {\n      props: {\n        onDelete: true\n      },\n      style: {\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark'])).map(([color]) => {\n      return {\n        props: {\n          color,\n          onDelete: true\n        },\n        style: {\n          [`&.${chipClasses.focusVisible}`]: {\n            background: (theme.vars || theme).palette[color].dark\n          }\n        }\n      };\n    }), {\n      props: {\n        clickable: true\n      },\n      style: {\n        userSelect: 'none',\n        WebkitTapHighlightColor: 'transparent',\n        cursor: 'pointer',\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity)\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        },\n        '&:active': {\n          boxShadow: (theme.vars || theme).shadows[1]\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['dark'])).map(([color]) => ({\n      props: {\n        color,\n        clickable: true\n      },\n      style: {\n        [`&:hover, &.${chipClasses.focusVisible}`]: {\n          backgroundColor: (theme.vars || theme).palette[color].dark\n        }\n      }\n    })), {\n      props: {\n        variant: 'outlined'\n      },\n      style: {\n        backgroundColor: 'transparent',\n        border: theme.vars ? `1px solid ${theme.vars.palette.Chip.defaultBorder}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[700]}`,\n        [`&.${chipClasses.clickable}:hover`]: {\n          backgroundColor: (theme.vars || theme).palette.action.hover\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: (theme.vars || theme).palette.action.focus\n        },\n        [`& .${chipClasses.avatar}`]: {\n          marginLeft: 4\n        },\n        [`& .${chipClasses.avatarSmall}`]: {\n          marginLeft: 2\n        },\n        [`& .${chipClasses.icon}`]: {\n          marginLeft: 4\n        },\n        [`& .${chipClasses.iconSmall}`]: {\n          marginLeft: 2\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          marginRight: 5\n        },\n        [`& .${chipClasses.deleteIconSmall}`]: {\n          marginRight: 3\n        }\n      }\n    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()) // no need to check for mainChannel as it's calculated from main\n    .map(([color]) => ({\n      props: {\n        variant: 'outlined',\n        color\n      },\n      style: {\n        color: (theme.vars || theme).palette[color].main,\n        border: `1px solid ${theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.7)` : alpha(theme.palette[color].main, 0.7)}`,\n        [`&.${chipClasses.clickable}:hover`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity)\n        },\n        [`&.${chipClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette[color].main, theme.palette.action.focusOpacity)\n        },\n        [`& .${chipClasses.deleteIcon}`]: {\n          color: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.7)` : alpha(theme.palette[color].main, 0.7),\n          '&:hover, &:active': {\n            color: (theme.vars || theme).palette[color].main\n          }\n        }\n      }\n    }))]\n  };\n}));\nconst ChipLabel = styled('span', {\n  name: 'MuiChip',\n  slot: 'Label',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      size\n    } = ownerState;\n    return [styles.label, styles[`label${capitalize(size)}`]];\n  }\n})({\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  paddingLeft: 12,\n  paddingRight: 12,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      variant: 'outlined'\n    },\n    style: {\n      paddingLeft: 11,\n      paddingRight: 11\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      paddingLeft: 8,\n      paddingRight: 8\n    }\n  }, {\n    props: {\n      size: 'small',\n      variant: 'outlined'\n    },\n    style: {\n      paddingLeft: 7,\n      paddingRight: 7\n    }\n  }]\n});\nfunction isDeleteKeyboardEvent(keyboardEvent) {\n  return keyboardEvent.key === 'Backspace' || keyboardEvent.key === 'Delete';\n}\n\n/**\n * Chips represent complex entities in small blocks, such as a contact.\n */\nconst Chip = /*#__PURE__*/React.forwardRef(function Chip(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiChip'\n  });\n  const {\n    avatar: avatarProp,\n    className,\n    clickable: clickableProp,\n    color = 'default',\n    component: ComponentProp,\n    deleteIcon: deleteIconProp,\n    disabled = false,\n    icon: iconProp,\n    label,\n    onClick,\n    onDelete,\n    onKeyDown,\n    onKeyUp,\n    size = 'medium',\n    variant = 'filled',\n    tabIndex,\n    skipFocusWhenDisabled = false,\n    // TODO v6: Rename to `focusableWhenDisabled`.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const chipRef = React.useRef(null);\n  const handleRef = useForkRef(chipRef, ref);\n  const handleDeleteIconClick = event => {\n    // Stop the event from bubbling up to the `Chip`\n    event.stopPropagation();\n    if (onDelete) {\n      onDelete(event);\n    }\n  };\n  const handleKeyDown = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target && isDeleteKeyboardEvent(event)) {\n      // Will be handled in keyUp, otherwise some browsers\n      // might init navigation\n      event.preventDefault();\n    }\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n  };\n  const handleKeyUp = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target) {\n      if (onDelete && isDeleteKeyboardEvent(event)) {\n        onDelete(event);\n      }\n    }\n    if (onKeyUp) {\n      onKeyUp(event);\n    }\n  };\n  const clickable = clickableProp !== false && onClick ? true : clickableProp;\n  const component = clickable || onDelete ? ButtonBase : ComponentProp || 'div';\n  const ownerState = {\n    ...props,\n    component,\n    disabled,\n    size,\n    color,\n    iconColor: /*#__PURE__*/React.isValidElement(iconProp) ? iconProp.props.color || color : color,\n    onDelete: !!onDelete,\n    clickable,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const moreProps = component === ButtonBase ? {\n    component: ComponentProp || 'div',\n    focusVisibleClassName: classes.focusVisible,\n    ...(onDelete && {\n      disableRipple: true\n    })\n  } : {};\n  let deleteIcon = null;\n  if (onDelete) {\n    deleteIcon = deleteIconProp && /*#__PURE__*/React.isValidElement(deleteIconProp) ? (/*#__PURE__*/React.cloneElement(deleteIconProp, {\n      className: clsx(deleteIconProp.props.className, classes.deleteIcon),\n      onClick: handleDeleteIconClick\n    })) : /*#__PURE__*/_jsx(CancelIcon, {\n      className: classes.deleteIcon,\n      onClick: handleDeleteIconClick\n    });\n  }\n  let avatar = null;\n  if (avatarProp && /*#__PURE__*/React.isValidElement(avatarProp)) {\n    avatar = /*#__PURE__*/React.cloneElement(avatarProp, {\n      className: clsx(classes.avatar, avatarProp.props.className)\n    });\n  }\n  let icon = null;\n  if (iconProp && /*#__PURE__*/React.isValidElement(iconProp)) {\n    icon = /*#__PURE__*/React.cloneElement(iconProp, {\n      className: clsx(classes.icon, iconProp.props.className)\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (avatar && icon) {\n      console.error('MUI: The Chip component can not handle the avatar ' + 'and the icon prop at the same time. Pick one.');\n    }\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: ChipRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    // The `component` prop is preserved because `Chip` relies on it for internal logic. If `shouldForwardComponentProp` were `false`, `useSlot` would remove the `component` prop, potentially breaking the component's behavior.\n    shouldForwardComponentProp: true,\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    additionalProps: {\n      disabled: clickable && disabled ? true : undefined,\n      tabIndex: skipFocusWhenDisabled && disabled ? -1 : tabIndex,\n      ...moreProps\n    },\n    getSlotProps: handlers => ({\n      ...handlers,\n      onClick: event => {\n        handlers.onClick?.(event);\n        onClick?.(event);\n      },\n      onKeyDown: event => {\n        handlers.onKeyDown?.(event);\n        handleKeyDown?.(event);\n      },\n      onKeyUp: event => {\n        handlers.onKeyUp?.(event);\n        handleKeyUp?.(event);\n      }\n    })\n  });\n  const [LabelSlot, labelProps] = useSlot('label', {\n    elementType: ChipLabel,\n    externalForwardedProps,\n    ownerState,\n    className: classes.label\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    as: component,\n    ...rootProps,\n    children: [avatar || icon, /*#__PURE__*/_jsx(LabelSlot, {\n      ...labelProps,\n      children: label\n    }), deleteIcon]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Chip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The Avatar element to display.\n   */\n  avatar: PropTypes.element,\n  /**\n   * This prop isn't supported.\n   * Use the `component` prop if you need to change the children structure.\n   */\n  children: unsupportedProp,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the chip will appear clickable, and will raise when pressed,\n   * even if the onClick prop is not defined.\n   * If `false`, the chip will not appear clickable, even if onClick prop is defined.\n   * This can be used, for example,\n   * along with the component prop to indicate an anchor Chip is clickable.\n   * Note: this controls the UI and does not affect the onClick event.\n   */\n  clickable: PropTypes.bool,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Override the default delete icon element. Shown only if `onDelete` is set.\n   */\n  deleteIcon: PropTypes.element,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Icon element.\n   */\n  icon: PropTypes.element,\n  /**\n   * The content of the component.\n   */\n  label: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * Callback fired when the delete icon is clicked.\n   * If set, the delete icon will be shown.\n   */\n  onDelete: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * If `true`, allows the disabled chip to escape focus.\n   * If `false`, allows the disabled chip to receive focus.\n   * @default false\n   */\n  skipFocusWhenDisabled: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    label: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'filled'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Chip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,WAAW,IAAIC,mBAAmB,QAAQ,kBAAkB;AACnE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,OAAO,EAAEN,QAAQ,IAAI,UAAU,EAAE,OAAOhB,UAAU,CAACiB,IAAI,CAAC,EAAE,EAAE,QAAQjB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAEG,SAAS,IAAI,WAAW,EAAEA,SAAS,IAAI,iBAAiBrB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAEE,QAAQ,IAAI,WAAW,EAAEA,QAAQ,IAAI,iBAAiBpB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAE,GAAGI,OAAO,GAAGtB,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC;IACjSO,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQzB,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC;IAC5CS,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS1B,UAAU,CAACiB,IAAI,CAAC,EAAE,EAAE,cAAcjB,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC;IAClFS,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO3B,UAAU,CAACiB,IAAI,CAAC,EAAE,EAAE,YAAYjB,UAAU,CAACmB,SAAS,CAAC,EAAE,CAAC;IAC9ES,UAAU,EAAE,CAAC,YAAY,EAAE,aAAa5B,UAAU,CAACiB,IAAI,CAAC,EAAE,EAAE,kBAAkBjB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAE,aAAalB,UAAU,CAACsB,OAAO,CAAC,QAAQtB,UAAU,CAACkB,KAAK,CAAC,EAAE;EAChK,CAAC;EACD,OAAOvB,cAAc,CAAC4B,KAAK,EAAEhB,mBAAmB,EAAEQ,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMc,QAAQ,GAAG3B,MAAM,CAAC,KAAK,EAAE;EAC7B4B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,MAAM;MACJf,KAAK;MACLC,SAAS;MACTE,SAAS;MACTD,QAAQ;MACRH,IAAI;MACJK;IACF,CAAC,GAAGR,UAAU;IACd,OAAO,CAAC;MACN,CAAC,MAAMR,WAAW,CAACoB,MAAM,EAAE,GAAGQ,MAAM,CAACR;IACvC,CAAC,EAAE;MACD,CAAC,MAAMpB,WAAW,CAACoB,MAAM,EAAE,GAAGQ,MAAM,CAAC,SAASlC,UAAU,CAACiB,IAAI,CAAC,EAAE;IAClE,CAAC,EAAE;MACD,CAAC,MAAMX,WAAW,CAACoB,MAAM,EAAE,GAAGQ,MAAM,CAAC,cAAclC,UAAU,CAACkB,KAAK,CAAC,EAAE;IACxE,CAAC,EAAE;MACD,CAAC,MAAMZ,WAAW,CAACqB,IAAI,EAAE,GAAGO,MAAM,CAACP;IACrC,CAAC,EAAE;MACD,CAAC,MAAMrB,WAAW,CAACqB,IAAI,EAAE,GAAGO,MAAM,CAAC,OAAOlC,UAAU,CAACiB,IAAI,CAAC,EAAE;IAC9D,CAAC,EAAE;MACD,CAAC,MAAMX,WAAW,CAACqB,IAAI,EAAE,GAAGO,MAAM,CAAC,YAAYlC,UAAU,CAACmB,SAAS,CAAC,EAAE;IACxE,CAAC,EAAE;MACD,CAAC,MAAMb,WAAW,CAACsB,UAAU,EAAE,GAAGM,MAAM,CAACN;IAC3C,CAAC,EAAE;MACD,CAAC,MAAMtB,WAAW,CAACsB,UAAU,EAAE,GAAGM,MAAM,CAAC,aAAalC,UAAU,CAACiB,IAAI,CAAC,EAAE;IAC1E,CAAC,EAAE;MACD,CAAC,MAAMX,WAAW,CAACsB,UAAU,EAAE,GAAGM,MAAM,CAAC,kBAAkBlC,UAAU,CAACkB,KAAK,CAAC,EAAE;IAChF,CAAC,EAAE;MACD,CAAC,MAAMZ,WAAW,CAACsB,UAAU,EAAE,GAAGM,MAAM,CAAC,aAAalC,UAAU,CAACsB,OAAO,CAAC,QAAQtB,UAAU,CAACkB,KAAK,CAAC,EAAE;IACtG,CAAC,EAAEgB,MAAM,CAACV,IAAI,EAAEU,MAAM,CAAC,OAAOlC,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC,EAAEiB,MAAM,CAAC,QAAQlC,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC,EAAEG,SAAS,IAAIa,MAAM,CAACb,SAAS,EAAEA,SAAS,IAAIH,KAAK,KAAK,SAAS,IAAIgB,MAAM,CAAC,iBAAiBlC,UAAU,CAACkB,KAAK,CAAC,GAAG,CAAC,EAAEE,QAAQ,IAAIc,MAAM,CAACC,SAAS,EAAEf,QAAQ,IAAIF,KAAK,KAAK,SAAS,IAAIgB,MAAM,CAAC,iBAAiBlC,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC,EAAEgB,MAAM,CAACZ,OAAO,CAAC,EAAEY,MAAM,CAAC,GAAGZ,OAAO,GAAGtB,UAAU,CAACkB,KAAK,CAAC,EAAE,CAAC,CAAC;EACrX;AACF,CAAC,CAAC,CAACf,SAAS,CAAC,CAAC;EACZiC;AACF,CAAC,KAAK;EACJ,MAAMC,SAAS,GAAGD,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGH,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGJ,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EACpG,OAAO;IACLC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAEN,KAAK,CAACO,UAAU,CAACD,UAAU;IACvCE,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE,CAAC;IACtCC,OAAO,EAAE,aAAa;IACtBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,GAAG;IACfhC,KAAK,EAAE,CAACkB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACc,IAAI,CAACC,OAAO;IACjDC,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACiB,MAAM,CAACC,QAAQ;IAC9DC,YAAY,EAAE,EAAE,GAAG,CAAC;IACpBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAEvB,KAAK,CAACwB,WAAW,CAACC,MAAM,CAAC,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;IACxE;IACAC,MAAM,EAAE,OAAO;IACf;IACAC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,MAAM;IACtBC,MAAM,EAAE,CAAC;IACT;IACAC,OAAO,EAAE,CAAC;IACV;IACAC,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAE,YAAY;IACvB,CAAC,KAAK9D,WAAW,CAACU,QAAQ,EAAE,GAAG;MAC7BqD,OAAO,EAAE,CAACjC,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACiB,MAAM,CAACe,eAAe;MAC7DC,aAAa,EAAE;IACjB,CAAC;IACD,CAAC,MAAMjE,WAAW,CAACoB,MAAM,EAAE,GAAG;MAC5B8C,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,CAAC;MACfC,KAAK,EAAE,EAAE;MACTzB,MAAM,EAAE,EAAE;MACV/B,KAAK,EAAEkB,KAAK,CAACe,IAAI,GAAGf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACqC,IAAI,CAACC,kBAAkB,GAAGvC,SAAS;MAC1EO,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE;IACvC,CAAC;IACD,CAAC,MAAMvC,WAAW,CAACuE,kBAAkB,EAAE,GAAG;MACxC3D,KAAK,EAAE,CAACkB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACe,OAAO,CAACyB,YAAY;MACzDxB,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACe,OAAO,CAAC0B;IACzD,CAAC;IACD,CAAC,MAAMzE,WAAW,CAAC0E,oBAAoB,EAAE,GAAG;MAC1C9D,KAAK,EAAE,CAACkB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAAC2C,SAAS,CAACH,YAAY;MAC3DxB,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAAC2C,SAAS,CAACF;IAC3D,CAAC;IACD,CAAC,MAAMzE,WAAW,CAAC4E,WAAW,EAAE,GAAG;MACjCV,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,CAAC;MACfC,KAAK,EAAE,EAAE;MACTzB,MAAM,EAAE,EAAE;MACVL,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE;IACvC,CAAC;IACD,CAAC,MAAMvC,WAAW,CAACqB,IAAI,EAAE,GAAG;MAC1B6C,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;IAChB,CAAC;IACD,CAAC,MAAMnE,WAAW,CAACsB,UAAU,EAAE,GAAG;MAChCuD,uBAAuB,EAAE,aAAa;MACtCjE,KAAK,EAAEkB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACc,IAAI,CAACgC,cAAc,UAAU,GAAGxF,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACc,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC;MACtHT,QAAQ,EAAE,EAAE;MACZkB,MAAM,EAAE,SAAS;MACjBuB,MAAM,EAAE,cAAc;MACtB,SAAS,EAAE;QACTnE,KAAK,EAAEkB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACc,IAAI,CAACgC,cAAc,SAAS,GAAGxF,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACc,IAAI,CAACC,OAAO,EAAE,GAAG;MACrH;IACF,CAAC;IACDiC,QAAQ,EAAE,CAAC;MACTrD,KAAK,EAAE;QACLhB,IAAI,EAAE;MACR,CAAC;MACDsE,KAAK,EAAE;QACLtC,MAAM,EAAE,EAAE;QACV,CAAC,MAAM3C,WAAW,CAACqB,IAAI,EAAE,GAAG;UAC1BiB,QAAQ,EAAE,EAAE;UACZ4B,UAAU,EAAE,CAAC;UACbC,WAAW,EAAE,CAAC;QAChB,CAAC;QACD,CAAC,MAAMnE,WAAW,CAACsB,UAAU,EAAE,GAAG;UAChCgB,QAAQ,EAAE,EAAE;UACZ6B,WAAW,EAAE,CAAC;UACdD,UAAU,EAAE,CAAC;QACf;MACF;IACF,CAAC,EAAE,GAAGgB,MAAM,CAACC,OAAO,CAACrD,KAAK,CAACE,OAAO,CAAC,CAACoD,MAAM,CAACtF,8BAA8B,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAACuF,GAAG,CAAC,CAAC,CAACzE,KAAK,CAAC,KAAK;MAC5G,OAAO;QACLe,KAAK,EAAE;UACLf;QACF,CAAC;QACDqE,KAAK,EAAE;UACLjC,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACpB,KAAK,CAAC,CAAC0E,IAAI;UAC1D1E,KAAK,EAAE,CAACkB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACpB,KAAK,CAAC,CAAC4D,YAAY;UACxD,CAAC,MAAMxE,WAAW,CAACsB,UAAU,EAAE,GAAG;YAChCV,KAAK,EAAEkB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACpB,KAAK,CAAC,CAAC2E,mBAAmB,SAAS,GAAGjG,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACpB,KAAK,CAAC,CAAC4D,YAAY,EAAE,GAAG,CAAC;YAClI,mBAAmB,EAAE;cACnB5D,KAAK,EAAE,CAACkB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACpB,KAAK,CAAC,CAAC4D;YAC9C;UACF;QACF;MACF,CAAC;IACH,CAAC,CAAC,EAAE;MACF7C,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACd,SAAS,KAAKc,KAAK,CAACf,KAAK;MAC/CqE,KAAK,EAAE;QACL,CAAC,MAAMjF,WAAW,CAACqB,IAAI,EAAE,GAAG;UAC1BT,KAAK,EAAEkB,KAAK,CAACe,IAAI,GAAGf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACqC,IAAI,CAACmB,gBAAgB,GAAGzD;QACjE;MACF;IACF,CAAC,EAAE;MACDJ,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACd,SAAS,KAAKc,KAAK,CAACf,KAAK,IAAIe,KAAK,CAACf,KAAK,KAAK,SAAS;MAC5EqE,KAAK,EAAE;QACL,CAAC,MAAMjF,WAAW,CAACqB,IAAI,EAAE,GAAG;UAC1BT,KAAK,EAAE;QACT;MACF;IACF,CAAC,EAAE;MACDe,KAAK,EAAE;QACLb,QAAQ,EAAE;MACZ,CAAC;MACDmE,KAAK,EAAE;QACL,CAAC,KAAKjF,WAAW,CAACyF,YAAY,EAAE,GAAG;UACjCzC,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAACyC,eAAe,WAAW5D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC0C,eAAe,MAAM7D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC2C,YAAY,IAAI,GAAGtG,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACiB,MAAM,CAACC,QAAQ,EAAEpB,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC0C,eAAe,GAAG7D,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC2C,YAAY;QACrS;MACF;IACF,CAAC,EAAE,GAAGV,MAAM,CAACC,OAAO,CAACrD,KAAK,CAACE,OAAO,CAAC,CAACoD,MAAM,CAACtF,8BAA8B,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAACuF,GAAG,CAAC,CAAC,CAACzE,KAAK,CAAC,KAAK;MACpG,OAAO;QACLe,KAAK,EAAE;UACLf,KAAK;UACLE,QAAQ,EAAE;QACZ,CAAC;QACDmE,KAAK,EAAE;UACL,CAAC,KAAKjF,WAAW,CAACyF,YAAY,EAAE,GAAG;YACjCI,UAAU,EAAE,CAAC/D,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACpB,KAAK,CAAC,CAAC6D;UACnD;QACF;MACF,CAAC;IACH,CAAC,CAAC,EAAE;MACF9C,KAAK,EAAE;QACLZ,SAAS,EAAE;MACb,CAAC;MACDkE,KAAK,EAAE;QACLa,UAAU,EAAE,MAAM;QAClBjB,uBAAuB,EAAE,aAAa;QACtCrB,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE;UACTR,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAACyC,eAAe,WAAW5D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC0C,eAAe,MAAM7D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC8C,YAAY,IAAI,GAAGzG,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACiB,MAAM,CAACC,QAAQ,EAAEpB,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC0C,eAAe,GAAG7D,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC8C,YAAY;QACrS,CAAC;QACD,CAAC,KAAK/F,WAAW,CAACyF,YAAY,EAAE,GAAG;UACjCzC,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAACyC,eAAe,WAAW5D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC0C,eAAe,MAAM7D,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC2C,YAAY,IAAI,GAAGtG,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACiB,MAAM,CAACC,QAAQ,EAAEpB,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC0C,eAAe,GAAG7D,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC2C,YAAY;QACrS,CAAC;QACD,UAAU,EAAE;UACVI,SAAS,EAAE,CAAClE,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEmE,OAAO,CAAC,CAAC;QAC5C;MACF;IACF,CAAC,EAAE,GAAGf,MAAM,CAACC,OAAO,CAACrD,KAAK,CAACE,OAAO,CAAC,CAACoD,MAAM,CAACtF,8BAA8B,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAACuF,GAAG,CAAC,CAAC,CAACzE,KAAK,CAAC,MAAM;MACrGe,KAAK,EAAE;QACLf,KAAK;QACLG,SAAS,EAAE;MACb,CAAC;MACDkE,KAAK,EAAE;QACL,CAAC,cAAcjF,WAAW,CAACyF,YAAY,EAAE,GAAG;UAC1CzC,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACpB,KAAK,CAAC,CAAC6D;QACxD;MACF;IACF,CAAC,CAAC,CAAC,EAAE;MACH9C,KAAK,EAAE;QACLX,OAAO,EAAE;MACX,CAAC;MACDiE,KAAK,EAAE;QACLjC,eAAe,EAAE,aAAa;QAC9BW,MAAM,EAAE7B,KAAK,CAACe,IAAI,GAAG,aAAaf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACqC,IAAI,CAAC6B,aAAa,EAAE,GAAG,aAAapE,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGH,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGJ,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,EAAE;QAC7K,CAAC,KAAKlC,WAAW,CAACe,SAAS,QAAQ,GAAG;UACpCiC,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACiB,MAAM,CAACkD;QACxD,CAAC;QACD,CAAC,KAAKnG,WAAW,CAACyF,YAAY,EAAE,GAAG;UACjCzC,eAAe,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACiB,MAAM,CAACmD;QACxD,CAAC;QACD,CAAC,MAAMpG,WAAW,CAACoB,MAAM,EAAE,GAAG;UAC5B8C,UAAU,EAAE;QACd,CAAC;QACD,CAAC,MAAMlE,WAAW,CAAC4E,WAAW,EAAE,GAAG;UACjCV,UAAU,EAAE;QACd,CAAC;QACD,CAAC,MAAMlE,WAAW,CAACqB,IAAI,EAAE,GAAG;UAC1B6C,UAAU,EAAE;QACd,CAAC;QACD,CAAC,MAAMlE,WAAW,CAACqG,SAAS,EAAE,GAAG;UAC/BnC,UAAU,EAAE;QACd,CAAC;QACD,CAAC,MAAMlE,WAAW,CAACsB,UAAU,EAAE,GAAG;UAChC6C,WAAW,EAAE;QACf,CAAC;QACD,CAAC,MAAMnE,WAAW,CAACsG,eAAe,EAAE,GAAG;UACrCnC,WAAW,EAAE;QACf;MACF;IACF,CAAC,EAAE,GAAGe,MAAM,CAACC,OAAO,CAACrD,KAAK,CAACE,OAAO,CAAC,CAACoD,MAAM,CAACtF,8BAA8B,CAAC,CAAC,CAAC,CAAC;IAAA,CAC5EuF,GAAG,CAAC,CAAC,CAACzE,KAAK,CAAC,MAAM;MACjBe,KAAK,EAAE;QACLX,OAAO,EAAE,UAAU;QACnBJ;MACF,CAAC;MACDqE,KAAK,EAAE;QACLrE,KAAK,EAAE,CAACkB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACpB,KAAK,CAAC,CAAC0E,IAAI;QAChD3B,MAAM,EAAE,aAAa7B,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACpB,KAAK,CAAC,CAAC2F,WAAW,SAAS,GAAGjH,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACpB,KAAK,CAAC,CAAC0E,IAAI,EAAE,GAAG,CAAC,EAAE;QAClI,CAAC,KAAKtF,WAAW,CAACe,SAAS,QAAQ,GAAG;UACpCiC,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACpB,KAAK,CAAC,CAAC2F,WAAW,MAAMzE,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC8C,YAAY,GAAG,GAAGzG,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACpB,KAAK,CAAC,CAAC0E,IAAI,EAAExD,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC8C,YAAY;QACjM,CAAC;QACD,CAAC,KAAK/F,WAAW,CAACyF,YAAY,EAAE,GAAG;UACjCzC,eAAe,EAAElB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACpB,KAAK,CAAC,CAAC2F,WAAW,MAAMzE,KAAK,CAACe,IAAI,CAACb,OAAO,CAACiB,MAAM,CAAC2C,YAAY,GAAG,GAAGtG,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACpB,KAAK,CAAC,CAAC0E,IAAI,EAAExD,KAAK,CAACE,OAAO,CAACiB,MAAM,CAAC2C,YAAY;QACjM,CAAC;QACD,CAAC,MAAM5F,WAAW,CAACsB,UAAU,EAAE,GAAG;UAChCV,KAAK,EAAEkB,KAAK,CAACe,IAAI,GAAG,QAAQf,KAAK,CAACe,IAAI,CAACb,OAAO,CAACpB,KAAK,CAAC,CAAC2F,WAAW,SAAS,GAAGjH,KAAK,CAACwC,KAAK,CAACE,OAAO,CAACpB,KAAK,CAAC,CAAC0E,IAAI,EAAE,GAAG,CAAC;UAClH,mBAAmB,EAAE;YACnB1E,KAAK,EAAE,CAACkB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEE,OAAO,CAACpB,KAAK,CAAC,CAAC0E;UAC9C;QACF;MACF;IACF,CAAC,CAAC,CAAC;EACL,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMkB,SAAS,GAAG5G,MAAM,CAAC,MAAM,EAAE;EAC/B4B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,MAAM;MACJhB;IACF,CAAC,GAAGH,UAAU;IACd,OAAO,CAACoB,MAAM,CAACT,KAAK,EAAES,MAAM,CAAC,QAAQlC,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC,CAAC;EAC3D;AACF,CAAC,CAAC,CAAC;EACD8F,QAAQ,EAAE,QAAQ;EAClBC,YAAY,EAAE,UAAU;EACxBC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChBxD,UAAU,EAAE,QAAQ;EACpB4B,QAAQ,EAAE,CAAC;IACTrD,KAAK,EAAE;MACLX,OAAO,EAAE;IACX,CAAC;IACDiE,KAAK,EAAE;MACL0B,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE;IAChB;EACF,CAAC,EAAE;IACDjF,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACDsE,KAAK,EAAE;MACL0B,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB;EACF,CAAC,EAAE;IACDjF,KAAK,EAAE;MACLhB,IAAI,EAAE,OAAO;MACbK,OAAO,EAAE;IACX,CAAC;IACDiE,KAAK,EAAE;MACL0B,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAChB;EACF,CAAC;AACH,CAAC,CAAC;AACF,SAASC,qBAAqBA,CAACC,aAAa,EAAE;EAC5C,OAAOA,aAAa,CAACC,GAAG,KAAK,WAAW,IAAID,aAAa,CAACC,GAAG,KAAK,QAAQ;AAC5E;;AAEA;AACA;AACA;AACA,MAAM1C,IAAI,GAAG,aAAanF,KAAK,CAAC8H,UAAU,CAAC,SAAS3C,IAAIA,CAAC4C,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAMvF,KAAK,GAAG5B,eAAe,CAAC;IAC5B4B,KAAK,EAAEsF,OAAO;IACdzF,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJJ,MAAM,EAAE+F,UAAU;IAClBC,SAAS;IACTrG,SAAS,EAAEsG,aAAa;IACxBzG,KAAK,GAAG,SAAS;IACjB0G,SAAS,EAAEC,aAAa;IACxBjG,UAAU,EAAEkG,cAAc;IAC1B9G,QAAQ,GAAG,KAAK;IAChBW,IAAI,EAAEoG,QAAQ;IACdtG,KAAK;IACLuG,OAAO;IACP5G,QAAQ;IACR6G,SAAS;IACTC,OAAO;IACPjH,IAAI,GAAG,QAAQ;IACfK,OAAO,GAAG,QAAQ;IAClB6G,QAAQ;IACRC,qBAAqB,GAAG,KAAK;IAC7B;IACA7G,KAAK,GAAG,CAAC,CAAC;IACV8G,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAGrG,KAAK;EACT,MAAMsG,OAAO,GAAG/I,KAAK,CAACgJ,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,SAAS,GAAG3I,UAAU,CAACyI,OAAO,EAAEf,GAAG,CAAC;EAC1C,MAAMkB,qBAAqB,GAAGC,KAAK,IAAI;IACrC;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAIxH,QAAQ,EAAE;MACZA,QAAQ,CAACuH,KAAK,CAAC;IACjB;EACF,CAAC;EACD,MAAME,aAAa,GAAGF,KAAK,IAAI;IAC7B;IACA,IAAIA,KAAK,CAACG,aAAa,KAAKH,KAAK,CAACI,MAAM,IAAI5B,qBAAqB,CAACwB,KAAK,CAAC,EAAE;MACxE;MACA;MACAA,KAAK,CAACK,cAAc,CAAC,CAAC;IACxB;IACA,IAAIf,SAAS,EAAE;MACbA,SAAS,CAACU,KAAK,CAAC;IAClB;EACF,CAAC;EACD,MAAMM,WAAW,GAAGN,KAAK,IAAI;IAC3B;IACA,IAAIA,KAAK,CAACG,aAAa,KAAKH,KAAK,CAACI,MAAM,EAAE;MACxC,IAAI3H,QAAQ,IAAI+F,qBAAqB,CAACwB,KAAK,CAAC,EAAE;QAC5CvH,QAAQ,CAACuH,KAAK,CAAC;MACjB;IACF;IACA,IAAIT,OAAO,EAAE;MACXA,OAAO,CAACS,KAAK,CAAC;IAChB;EACF,CAAC;EACD,MAAMtH,SAAS,GAAGsG,aAAa,KAAK,KAAK,IAAIK,OAAO,GAAG,IAAI,GAAGL,aAAa;EAC3E,MAAMC,SAAS,GAAGvG,SAAS,IAAID,QAAQ,GAAGnB,UAAU,GAAG4H,aAAa,IAAI,KAAK;EAC7E,MAAM/G,UAAU,GAAG;IACjB,GAAGmB,KAAK;IACR2F,SAAS;IACT5G,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,SAAS,EAAE,aAAa3B,KAAK,CAAC0J,cAAc,CAACnB,QAAQ,CAAC,GAAGA,QAAQ,CAAC9F,KAAK,CAACf,KAAK,IAAIA,KAAK,GAAGA,KAAK;IAC9FE,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBC,SAAS;IACTC;EACF,CAAC;EACD,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMqI,SAAS,GAAGvB,SAAS,KAAK3H,UAAU,GAAG;IAC3C2H,SAAS,EAAEC,aAAa,IAAI,KAAK;IACjCuB,qBAAqB,EAAErI,OAAO,CAACgF,YAAY;IAC3C,IAAI3E,QAAQ,IAAI;MACdiI,aAAa,EAAE;IACjB,CAAC;EACH,CAAC,GAAG,CAAC,CAAC;EACN,IAAIzH,UAAU,GAAG,IAAI;EACrB,IAAIR,QAAQ,EAAE;IACZQ,UAAU,GAAGkG,cAAc,IAAI,aAAatI,KAAK,CAAC0J,cAAc,CAACpB,cAAc,CAAC,IAAI,aAAatI,KAAK,CAAC8J,YAAY,CAACxB,cAAc,EAAE;MAClIJ,SAAS,EAAEhI,IAAI,CAACoI,cAAc,CAAC7F,KAAK,CAACyF,SAAS,EAAE3G,OAAO,CAACa,UAAU,CAAC;MACnEoG,OAAO,EAAEU;IACX,CAAC,CAAC,IAAI,aAAahI,IAAI,CAACb,UAAU,EAAE;MAClC6H,SAAS,EAAE3G,OAAO,CAACa,UAAU;MAC7BoG,OAAO,EAAEU;IACX,CAAC,CAAC;EACJ;EACA,IAAIhH,MAAM,GAAG,IAAI;EACjB,IAAI+F,UAAU,IAAI,aAAajI,KAAK,CAAC0J,cAAc,CAACzB,UAAU,CAAC,EAAE;IAC/D/F,MAAM,GAAG,aAAalC,KAAK,CAAC8J,YAAY,CAAC7B,UAAU,EAAE;MACnDC,SAAS,EAAEhI,IAAI,CAACqB,OAAO,CAACW,MAAM,EAAE+F,UAAU,CAACxF,KAAK,CAACyF,SAAS;IAC5D,CAAC,CAAC;EACJ;EACA,IAAI/F,IAAI,GAAG,IAAI;EACf,IAAIoG,QAAQ,IAAI,aAAavI,KAAK,CAAC0J,cAAc,CAACnB,QAAQ,CAAC,EAAE;IAC3DpG,IAAI,GAAG,aAAanC,KAAK,CAAC8J,YAAY,CAACvB,QAAQ,EAAE;MAC/CL,SAAS,EAAEhI,IAAI,CAACqB,OAAO,CAACY,IAAI,EAAEoG,QAAQ,CAAC9F,KAAK,CAACyF,SAAS;IACxD,CAAC,CAAC;EACJ;EACA,IAAI6B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI/H,MAAM,IAAIC,IAAI,EAAE;MAClB+H,OAAO,CAACC,KAAK,CAAC,oDAAoD,GAAG,+CAA+C,CAAC;IACvH;EACF;EACA,MAAMC,sBAAsB,GAAG;IAC7BrI,KAAK;IACL8G;EACF,CAAC;EACD,MAAM,CAACwB,QAAQ,EAAEC,SAAS,CAAC,GAAGtJ,OAAO,CAAC,MAAM,EAAE;IAC5CuJ,WAAW,EAAElI,QAAQ;IACrB+H,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGtB;IACL,CAAC;IACDxH,UAAU;IACV;IACAkJ,0BAA0B,EAAE,IAAI;IAChCxC,GAAG,EAAEiB,SAAS;IACdf,SAAS,EAAEhI,IAAI,CAACqB,OAAO,CAACS,IAAI,EAAEkG,SAAS,CAAC;IACxCuC,eAAe,EAAE;MACfjJ,QAAQ,EAAEK,SAAS,IAAIL,QAAQ,GAAG,IAAI,GAAGkJ,SAAS;MAClD/B,QAAQ,EAAEC,qBAAqB,IAAIpH,QAAQ,GAAG,CAAC,CAAC,GAAGmH,QAAQ;MAC3D,GAAGgB;IACL,CAAC;IACDgB,YAAY,EAAEC,QAAQ,KAAK;MACzB,GAAGA,QAAQ;MACXpC,OAAO,EAAEW,KAAK,IAAI;QAChByB,QAAQ,CAACpC,OAAO,GAAGW,KAAK,CAAC;QACzBX,OAAO,GAAGW,KAAK,CAAC;MAClB,CAAC;MACDV,SAAS,EAAEU,KAAK,IAAI;QAClByB,QAAQ,CAACnC,SAAS,GAAGU,KAAK,CAAC;QAC3BE,aAAa,GAAGF,KAAK,CAAC;MACxB,CAAC;MACDT,OAAO,EAAES,KAAK,IAAI;QAChByB,QAAQ,CAAClC,OAAO,GAAGS,KAAK,CAAC;QACzBM,WAAW,GAAGN,KAAK,CAAC;MACtB;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAM,CAAC0B,SAAS,EAAEC,UAAU,CAAC,GAAG9J,OAAO,CAAC,OAAO,EAAE;IAC/CuJ,WAAW,EAAEjD,SAAS;IACtB8C,sBAAsB;IACtB9I,UAAU;IACV4G,SAAS,EAAE3G,OAAO,CAACU;EACrB,CAAC,CAAC;EACF,OAAO,aAAab,KAAK,CAACiJ,QAAQ,EAAE;IAClCU,EAAE,EAAE3C,SAAS;IACb,GAAGkC,SAAS;IACZU,QAAQ,EAAE,CAAC9I,MAAM,IAAIC,IAAI,EAAE,aAAajB,IAAI,CAAC2J,SAAS,EAAE;MACtD,GAAGC,UAAU;MACbE,QAAQ,EAAE/I;IACZ,CAAC,CAAC,EAAEG,UAAU;EAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AACF2H,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9E,IAAI,CAAC8F,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACE/I,MAAM,EAAEjC,SAAS,CAACiL,OAAO;EACzB;AACF;AACA;AACA;EACEF,QAAQ,EAAEzK,eAAe;EACzB;AACF;AACA;EACEgB,OAAO,EAAEtB,SAAS,CAACkL,MAAM;EACzB;AACF;AACA;EACEjD,SAAS,EAAEjI,SAAS,CAACmL,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEvJ,SAAS,EAAE5B,SAAS,CAACoL,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;EACE3J,KAAK,EAAEzB,SAAS,CAAC,sCAAsCqL,SAAS,CAAC,CAACrL,SAAS,CAACsL,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEtL,SAAS,CAACmL,MAAM,CAAC,CAAC;EACjL;AACF;AACA;AACA;EACEhD,SAAS,EAAEnI,SAAS,CAACsK,WAAW;EAChC;AACF;AACA;EACEnI,UAAU,EAAEnC,SAAS,CAACiL,OAAO;EAC7B;AACF;AACA;AACA;EACE1J,QAAQ,EAAEvB,SAAS,CAACoL,IAAI;EACxB;AACF;AACA;EACElJ,IAAI,EAAElC,SAAS,CAACiL,OAAO;EACvB;AACF;AACA;EACEjJ,KAAK,EAAEhC,SAAS,CAACuL,IAAI;EACrB;AACF;AACA;EACEhD,OAAO,EAAEvI,SAAS,CAACwL,IAAI;EACvB;AACF;AACA;AACA;EACE7J,QAAQ,EAAE3B,SAAS,CAACwL,IAAI;EACxB;AACF;AACA;EACEhD,SAAS,EAAExI,SAAS,CAACwL,IAAI;EACzB;AACF;AACA;EACE/C,OAAO,EAAEzI,SAAS,CAACwL,IAAI;EACvB;AACF;AACA;AACA;EACEhK,IAAI,EAAExB,SAAS,CAAC,sCAAsCqL,SAAS,CAAC,CAACrL,SAAS,CAACsL,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAEtL,SAAS,CAACmL,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;AACA;EACExC,qBAAqB,EAAE3I,SAAS,CAACoL,IAAI;EACrC;AACF;AACA;AACA;EACExC,SAAS,EAAE5I,SAAS,CAACyL,KAAK,CAAC;IACzBzJ,KAAK,EAAEhC,SAAS,CAACqL,SAAS,CAAC,CAACrL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAACkL,MAAM,CAAC,CAAC;IAC9DnJ,IAAI,EAAE/B,SAAS,CAACqL,SAAS,CAAC,CAACrL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAACkL,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEpJ,KAAK,EAAE9B,SAAS,CAACyL,KAAK,CAAC;IACrBzJ,KAAK,EAAEhC,SAAS,CAACsK,WAAW;IAC5BvI,IAAI,EAAE/B,SAAS,CAACsK;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEoB,EAAE,EAAE1L,SAAS,CAACqL,SAAS,CAAC,CAACrL,SAAS,CAAC2L,OAAO,CAAC3L,SAAS,CAACqL,SAAS,CAAC,CAACrL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAACkL,MAAM,EAAElL,SAAS,CAACoL,IAAI,CAAC,CAAC,CAAC,EAAEpL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAACkL,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACExC,QAAQ,EAAE1I,SAAS,CAAC4L,MAAM;EAC1B;AACF;AACA;AACA;EACE/J,OAAO,EAAE7B,SAAS,CAAC,sCAAsCqL,SAAS,CAAC,CAACrL,SAAS,CAACsL,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAEtL,SAAS,CAACmL,MAAM,CAAC;AAChI,CAAC,GAAG,KAAK,CAAC;AACV,eAAejG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}