"""
API views for core functionality
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.urls import reverse
from django.conf import settings
from django.shortcuts import render


@api_view(['GET'])
@permission_classes([AllowAny])
def api_root(request):
    """
    API Root - عرض جميع endpoints المتاحة
    """
    base_url = request.build_absolute_uri('/api/v1/')
    
    api_endpoints = {
        'message': 'مرحباً بك في API منصة lnk2store',
        'version': '1.0',
        'documentation': f"{base_url}docs/",
        'endpoints': {
            'authentication': {
                'login': f"{base_url}accounts/login/",
                'register': f"{base_url}accounts/register/",
                'profile': f"{base_url}accounts/profile/",
                'stats': f"{base_url}accounts/stats/",
            },
            'products': {
                'list': f"{base_url}products/",
                'create': f"{base_url}products/create/",
                'detail': f"{base_url}products/{{id}}/",
                'search': f"{base_url}products/search/?q={{query}}",
                'categories': f"{base_url}products/categories/",
            },
            'leads': {
                'my_leads': f"{base_url}leads/my-leads/",
                'create': f"{base_url}leads/create/",
                'detail': f"{base_url}leads/{{id}}/",
                'stats': f"{base_url}leads/stats/",
            },
            'wallet': {
                'balance': f"{base_url}wallet/",
                'recharge': f"{base_url}wallet/recharge/",
                'transactions': f"{base_url}wallet/transactions/",
                'stats': f"{base_url}wallet/stats/",
            },
            'templates': {
                'list': f"{base_url}templates/templates/",
                'categories': f"{base_url}templates/templates/categories/",
                'my_pages': f"{base_url}templates/my-pages/",
            },
            'pages': {
                'list': f"{base_url}pages/",
                'create': f"{base_url}pages/create/",
                'by_slug': f"{base_url}pages/{{slug}}/",
            }
        },
        'authentication_info': {
            'type': 'JWT Bearer Token',
            'header': 'Authorization: Bearer <token>',
            'note': 'احصل على التوكن من endpoint تسجيل الدخول'
        },
        'features': [
            'نظام الدفع لكل طلب (Pay-per-lead)',
            'تكامل WhatsApp',
            'قوالب تسويقية متعددة',
            'نظام محفظة إلكترونية',
            'إحصائيات شاملة',
            'بحث متقدم',
            'واجهة إدارية'
        ],
        'status': 'active',
        'server_time': request.META.get('HTTP_DATE', 'غير متوفر')
    }
    
    return Response(api_endpoints)


@api_view(['GET'])
@permission_classes([AllowAny])
def api_health(request):
    """
    Health check endpoint
    """
    return Response({
        'status': 'healthy',
        'message': 'API يعمل بشكل طبيعي',
        'version': '1.0',
        'debug': settings.DEBUG
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def api_docs(request):
    """
    API Documentation
    """
    docs = {
        'title': 'lnk2store API Documentation',
        'description': 'منصة SaaS لإنشاء صفحات تسويقية مع نظام جمع الطلبات',
        'version': '1.0',
        'base_url': request.build_absolute_uri('/api/v1/'),
        
        'authentication': {
            'method': 'JWT Bearer Token',
            'login_endpoint': '/api/v1/accounts/login/',
            'example': {
                'request': {
                    'method': 'POST',
                    'url': '/api/v1/accounts/login/',
                    'body': {
                        'username': 'your_username',
                        'password': 'your_password'
                    }
                },
                'response': {
                    'user': {'username': 'admin', 'email': '<EMAIL>'},
                    'tokens': {
                        'access': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
                        'refresh': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
                    }
                }
            }
        },
        
        'common_responses': {
            '200': 'نجح الطلب',
            '201': 'تم إنشاء المورد بنجاح',
            '400': 'خطأ في البيانات المرسلة',
            '401': 'غير مصرح - تحتاج تسجيل دخول',
            '403': 'ممنوع - ليس لديك صلاحية',
            '404': 'المورد غير موجود',
            '500': 'خطأ في الخادم'
        },
        
        'examples': {
            'get_products': {
                'method': 'GET',
                'url': '/api/v1/products/',
                'description': 'جلب قائمة المنتجات'
            },
            'create_lead': {
                'method': 'POST',
                'url': '/api/v1/leads/create/',
                'headers': {'Authorization': 'Bearer <token>'},
                'body': {
                    'name': 'اسم العميل',
                    'phone': '+966501234567',
                    'product_id': 1,
                    'notes': 'ملاحظات إضافية'
                }
            }
        }
    }
    
    return Response(docs)


def api_docs_html(request):
    """
    API Documentation HTML page
    """
    return render(request, 'api_docs.html')
