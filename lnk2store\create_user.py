import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'lnk2store.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

# Create test user
email = '<EMAIL>'
password = 'testpassword123'
username = 'ahmed.hassan'

user, created = User.objects.get_or_create(
    email=email,
    defaults={
        'username': username,
        'first_name': '<PERSON>',
        'last_name': '<PERSON>'
    }
)

if created:
    user.set_password(password)
    user.save()
    print(f"✅ Created new user: {email}")
else:
    # Update password for existing user
    user.set_password(password)
    user.save()
    print(f"✅ Updated existing user: {email}")

print(f"Username: {username}")
print(f"Email: {email}")
print(f"Password: {password}")
print("You can now use these credentials to login!")
