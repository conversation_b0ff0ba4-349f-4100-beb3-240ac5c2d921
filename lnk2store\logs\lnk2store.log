API Exception: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedTable: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\apps\lnk2store\lnk2store\products\api_views.py", line 132, in product_categories
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 797, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 715, in to_representation
    return [
           ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.ProgrammingError: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^

API Exception: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedTable: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\apps\lnk2store\lnk2store\products\api_views.py", line 132, in product_categories
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 797, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 715, in to_representation
    return [
           ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.ProgrammingError: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^

API Exception: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedTable: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\apps\lnk2store\lnk2store\products\api_views.py", line 132, in product_categories
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 797, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 715, in to_representation
    return [
           ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.ProgrammingError: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^

API Exception: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedTable: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\apps\lnk2store\lnk2store\products\api_views.py", line 132, in product_categories
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 797, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\rest_framework\serializers.py", line 715, in to_representation
    return [
           ^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.ProgrammingError: relation "products_productcategory" does not exist
LINE 1: ...ve", "products_productcategory"."created_at" FROM "products_...
                                                             ^

INFO 2025-07-11 12:15:41,111 autoreload 14800 1712 Watching for file changes with StatReloader
INFO 2025-07-11 12:16:15,054 autoreload 14800 1712 D:\apps\lnk2store\lnk2store\lnk2store\settings\base.py changed, reloading.
INFO 2025-07-11 12:16:15,666 autoreload 14344 13300 Watching for file changes with StatReloader
INFO 2025-07-11 12:21:27,431 autoreload 20112 18040 Watching for file changes with StatReloader
INFO 2025-07-11 12:23:06,212 autoreload 7380 9144 Watching for file changes with StatReloader
WARNING 2025-07-11 12:23:27,668 log 7380 15532 Not Found: /
WARNING 2025-07-11 12:23:27,669 basehttp 7380 15532 "GET / HTTP/1.1" 404 4338
WARNING 2025-07-11 12:23:28,182 log 7380 15532 Not Found: /favicon.ico
WARNING 2025-07-11 12:23:28,183 basehttp 7380 15532 "GET /favicon.ico HTTP/1.1" 404 4389
INFO 2025-07-11 12:24:46,309 basehttp 7380 16216 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:24:46,445 basehttp 7380 16216 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-11 12:24:49,749 basehttp 7380 15180 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:24:51,774 basehttp 7380 11280 "GET /api/v1/products/ HTTP/1.1" 200 52
INFO 2025-07-11 12:24:53,823 basehttp 7380 19860 "GET /api/v1/leads/my-leads/ HTTP/1.1" 200 52
WARNING 2025-07-11 12:24:55,879 log 7380 19664 Not Found: /api/v1/wallet/balance/
WARNING 2025-07-11 12:24:55,879 basehttp 7380 19664 "GET /api/v1/wallet/balance/ HTTP/1.1" 404 5672
WARNING 2025-07-11 12:24:57,923 log 7380 10076 Not Found: /api/v1/templates/
WARNING 2025-07-11 12:24:57,923 basehttp 7380 10076 "GET /api/v1/templates/ HTTP/1.1" 404 8495
INFO 2025-07-11 12:26:53,523 basehttp 7380 13656 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:26:53,525 basehttp 7380 13656 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-11 12:26:56,225 basehttp 7380 13156 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:26:58,258 basehttp 7380 8276 "GET /api/v1/products/ HTTP/1.1" 200 52
INFO 2025-07-11 12:27:00,301 basehttp 7380 17724 "GET /api/v1/leads/my-leads/ HTTP/1.1" 200 52
INFO 2025-07-11 12:27:02,337 basehttp 7380 14524 "GET /api/v1/wallet/ HTTP/1.1" 200 50
INFO 2025-07-11 12:27:04,373 basehttp 7380 18040 "GET /api/v1/templates/templates/ HTTP/1.1" 200 52
INFO 2025-07-11 12:28:30,126 basehttp 7380 20260 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:28:30,131 basehttp 7380 20260 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-11 12:28:34,897 basehttp 7380 15780 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:28:36,945 basehttp 7380 8644 "GET /api/v1/accounts/profile/ HTTP/1.1" 200 195
INFO 2025-07-11 12:28:39,000 basehttp 7380 14808 "GET /api/v1/accounts/stats/ HTTP/1.1" 200 93
INFO 2025-07-11 12:28:41,033 basehttp 7380 13096 "GET /api/v1/products/ HTTP/1.1" 200 52
INFO 2025-07-11 12:28:43,080 basehttp 7380 15820 "GET /api/v1/leads/my-leads/ HTTP/1.1" 200 52
INFO 2025-07-11 12:28:45,111 basehttp 7380 20424 "GET /api/v1/leads/stats/ HTTP/1.1" 200 112
INFO 2025-07-11 12:28:47,136 basehttp 7380 5228 "GET /api/v1/wallet/ HTTP/1.1" 200 50
INFO 2025-07-11 12:28:49,195 basehttp 7380 13632 "GET /api/v1/wallet/transactions/ HTTP/1.1" 200 59
INFO 2025-07-11 12:28:51,238 basehttp 7380 2312 "GET /api/v1/wallet/stats/ HTTP/1.1" 200 148
INFO 2025-07-11 12:28:53,268 basehttp 7380 10784 "GET /api/v1/templates/templates/ HTTP/1.1" 200 52
INFO 2025-07-11 12:28:55,323 basehttp 7380 2676 "GET /api/v1/templates/templates/categories/ HTTP/1.1" 200 388
INFO 2025-07-11 12:28:57,344 basehttp 7380 17476 "GET /api/v1/templates/my-pages/ HTTP/1.1" 200 52
INFO 2025-07-11 12:35:54,101 basehttp 7380 19144 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:35:54,106 basehttp 7380 19144 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-11 12:35:58,849 basehttp 7380 8028 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:36:00,890 basehttp 7380 19660 "GET /api/v1/accounts/profile/ HTTP/1.1" 200 195
INFO 2025-07-11 12:36:02,929 basehttp 7380 2528 "GET /api/v1/accounts/stats/ HTTP/1.1" 200 93
INFO 2025-07-11 12:36:04,981 basehttp 7380 19624 "GET /api/v1/products/ HTTP/1.1" 200 2208
INFO 2025-07-11 12:36:07,020 basehttp 7380 14164 "GET /api/v1/leads/my-leads/ HTTP/1.1" 200 52
INFO 2025-07-11 12:36:09,063 basehttp 7380 11640 "GET /api/v1/leads/stats/ HTTP/1.1" 200 112
INFO 2025-07-11 12:36:11,121 basehttp 7380 18672 "GET /api/v1/wallet/ HTTP/1.1" 200 50
INFO 2025-07-11 12:36:13,193 basehttp 7380 10912 "GET /api/v1/wallet/transactions/ HTTP/1.1" 200 59
INFO 2025-07-11 12:36:15,224 basehttp 7380 15572 "GET /api/v1/wallet/stats/ HTTP/1.1" 200 148
INFO 2025-07-11 12:36:17,286 basehttp 7380 16448 "GET /api/v1/templates/templates/ HTTP/1.1" 200 981
INFO 2025-07-11 12:36:19,323 basehttp 7380 12712 "GET /api/v1/templates/templates/categories/ HTTP/1.1" 200 388
INFO 2025-07-11 12:36:21,368 basehttp 7380 10352 "GET /api/v1/templates/my-pages/ HTTP/1.1" 200 52
INFO 2025-07-11 12:36:33,478 basehttp 7380 17484 "GET /api/v1/products/1/ HTTP/1.1" 200 716
INFO 2025-07-11 12:36:33,654 basehttp 7380 17484 - Broken pipe from ('127.0.0.1', 56858)
INFO 2025-07-11 12:37:22,570 basehttp 7380 16652 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:37:24,610 basehttp 7380 11760 "GET /api/v1/products/ HTTP/1.1" 200 2208
INFO 2025-07-11 12:37:52,056 basehttp 7380 13652 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:37:54,114 basehttp 7380 19100 "GET /api/v1/products/ HTTP/1.1" 200 2208
INFO 2025-07-11 12:38:25,882 basehttp 7380 3884 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:38:27,927 basehttp 7380 1572 "GET /api/v1/products/ HTTP/1.1" 200 2208
INFO 2025-07-11 12:39:18,539 basehttp 7380 1340 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:39:20,580 basehttp 7380 11636 "GET /api/v1/products/ HTTP/1.1" 200 2208
INFO 2025-07-11 12:40:03,901 basehttp 7380 19112 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:40:03,906 basehttp 7380 19112 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-11 12:40:08,641 basehttp 7380 11372 "POST /api/v1/accounts/login/ HTTP/1.1" 200 749
INFO 2025-07-11 12:40:10,664 basehttp 7380 15804 "GET /api/v1/accounts/profile/ HTTP/1.1" 200 195
INFO 2025-07-11 12:40:12,702 basehttp 7380 18636 "GET /api/v1/accounts/stats/ HTTP/1.1" 200 93
INFO 2025-07-11 12:40:14,749 basehttp 7380 10664 "GET /api/v1/products/ HTTP/1.1" 200 2208
INFO 2025-07-11 12:40:16,778 basehttp 7380 13792 "GET /api/v1/products/5/ HTTP/1.1" 200 658
INFO 2025-07-11 12:40:18,811 basehttp 7380 7148 "GET /api/v1/products/search/?q=test HTTP/1.1" 200 39
INFO 2025-07-11 12:40:20,840 basehttp 7380 16004 "GET /api/v1/products/categories/ HTTP/1.1" 200 518
INFO 2025-07-11 12:40:22,875 basehttp 7380 4984 "GET /api/v1/leads/my-leads/ HTTP/1.1" 200 52
INFO 2025-07-11 12:40:24,921 basehttp 7380 9436 "GET /api/v1/leads/stats/ HTTP/1.1" 200 112
INFO 2025-07-11 12:40:26,976 basehttp 7380 16608 "GET /api/v1/wallet/ HTTP/1.1" 200 50
INFO 2025-07-11 12:40:28,998 basehttp 7380 18536 "GET /api/v1/wallet/transactions/ HTTP/1.1" 200 59
INFO 2025-07-11 12:40:31,057 basehttp 7380 17348 "GET /api/v1/wallet/stats/ HTTP/1.1" 200 148
INFO 2025-07-11 12:40:33,092 basehttp 7380 14576 "GET /api/v1/templates/templates/ HTTP/1.1" 200 981
INFO 2025-07-11 12:40:35,117 basehttp 7380 7512 "GET /api/v1/templates/templates/categories/ HTTP/1.1" 200 388
INFO 2025-07-11 12:40:37,172 basehttp 7380 10184 "GET /api/v1/templates/my-pages/ HTTP/1.1" 200 52
INFO 2025-07-11 12:43:17,628 basehttp 7380 17096 "GET /admin HTTP/1.1" 301 0
INFO 2025-07-11 12:43:17,633 basehttp 7380 17708 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:43:17,633 basehttp 7380 17708 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-11 12:43:17,739 basehttp 7380 17708 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
INFO 2025-07-11 12:43:17,748 basehttp 7380 17708 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
INFO 2025-07-11 12:43:17,753 basehttp 7380 17708 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-07-11 12:43:17,762 basehttp 7380 17708 "GET /static/admin/css/login.css HTTP/1.1" 200 951
INFO 2025-07-11 12:43:17,774 basehttp 7380 17708 "GET /static/admin/css/rtl.css HTTP/1.1" 200 4772
INFO 2025-07-11 12:43:17,783 basehttp 7380 17708 "GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
INFO 2025-07-11 12:43:17,788 basehttp 7380 17708 "GET /static/admin/css/responsive_rtl.css HTTP/1.1" 200 1991
INFO 2025-07-11 12:43:17,796 basehttp 7380 17708 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
INFO 2025-07-11 12:43:17,811 basehttp 7380 17708 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
WARNING 2025-07-11 12:43:18,159 log 7380 17708 Not Found: /favicon.ico
WARNING 2025-07-11 12:43:18,159 basehttp 7380 17708 "GET /favicon.ico HTTP/1.1" 404 4389
INFO 2025-07-11 12:43:49,169 basehttp 7380 17708 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:43:49,312 basehttp 7380 17708 "GET /admin/ HTTP/1.1" 200 5767
INFO 2025-07-11 12:43:49,355 basehttp 7380 17708 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-11 12:43:49,376 basehttp 7380 2764 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-07-11 12:43:49,376 basehttp 7380 17708 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-07-11 12:44:10,119 basehttp 7380 17708 "GET /admin/password_change/ HTTP/1.1" 200 7627
INFO 2025-07-11 12:44:10,146 basehttp 7380 17708 "GET /static/admin/css/forms.css HTTP/1.1" 200 8525
INFO 2025-07-11 12:44:10,159 basehttp 7380 17708 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11991
INFO 2025-07-11 12:44:34,126 basehttp 7380 17708 "POST /admin/password_change/ HTTP/1.1" 302 0
INFO 2025-07-11 12:44:34,300 basehttp 7380 17708 "GET /admin/password_change/done/ HTTP/1.1" 200 5530
INFO 2025-07-11 12:44:53,382 basehttp 7380 17708 "POST /admin/logout/ HTTP/1.1" 200 3756
INFO 2025-07-11 12:44:58,990 basehttp 7380 17708 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:44:58,994 basehttp 7380 17708 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4342
INFO 2025-07-11 12:45:06,173 basehttp 7380 17708 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:45:06,299 basehttp 7380 17708 "GET /admin/ HTTP/1.1" 200 5767
INFO 2025-07-11 12:45:08,507 basehttp 7380 17708 "GET /admin/auth/group/add/ HTTP/1.1" 200 16398
INFO 2025-07-11 12:45:08,588 basehttp 7380 2764 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
INFO 2025-07-11 12:45:08,600 basehttp 7380 2764 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-07-11 12:45:08,617 basehttp 7380 2764 "GET /static/admin/js/core.js HTTP/1.1" 200 6208
INFO 2025-07-11 12:45:08,634 basehttp 7380 2764 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9777
INFO 2025-07-11 12:45:08,650 basehttp 7380 2764 "GET /static/admin/js/SelectBox.js HTTP/1.1" 200 4530
INFO 2025-07-11 12:45:08,666 basehttp 7380 17708 "GET /admin/jsi18n/ HTTP/1.1" 200 14010
INFO 2025-07-11 12:45:08,667 basehttp 7380 2764 "GET /static/admin/js/actions.js HTTP/1.1" 200 8076
INFO 2025-07-11 12:45:08,682 basehttp 7380 2764 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
INFO 2025-07-11 12:45:08,683 basehttp 7380 17708 "GET /static/admin/js/SelectFilter2.js HTTP/1.1" 200 15845
INFO 2025-07-11 12:45:08,692 basehttp 7380 2764 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-07-11 12:45:08,707 basehttp 7380 2764 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-07-11 12:45:08,720 basehttp 7380 2764 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-07-11 12:45:08,767 basehttp 7380 17708 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
INFO 2025-07-11 12:45:08,859 basehttp 7380 17708 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-07-11 12:45:08,859 basehttp 7380 2764 "GET /static/admin/img/selector-icons.svg HTTP/1.1" 200 3291
INFO 2025-07-11 12:45:34,013 basehttp 7380 2764 "GET /admin/password_change/ HTTP/1.1" 200 7627
INFO 2025-07-11 12:45:41,772 basehttp 7380 2764 "GET /admin/auth/group/add/ HTTP/1.1" 200 16398
INFO 2025-07-11 12:45:41,917 basehttp 7380 2764 "GET /admin/jsi18n/ HTTP/1.1" 200 14010
INFO 2025-07-11 12:45:49,586 basehttp 7380 2764 "GET /admin/ HTTP/1.1" 200 5767
WARNING 2025-07-11 12:46:13,186 log 7380 2764 Not Found: /api/v1/
WARNING 2025-07-11 12:46:13,186 basehttp 7380 2764 "GET /api/v1/ HTTP/1.1" 404 4377
WARNING 2025-07-11 12:47:23,347 log 7380 2764 Not Found: /api/v1/
WARNING 2025-07-11 12:47:23,348 basehttp 7380 2764 "GET /api/v1/ HTTP/1.1" 404 4377
INFO 2025-07-11 12:48:10,344 autoreload 7380 9144 D:\apps\lnk2store\lnk2store\lnk2store\urls.py changed, reloading.
INFO 2025-07-11 12:48:11,023 autoreload 13532 876 Watching for file changes with StatReloader
INFO 2025-07-11 12:48:26,639 basehttp 13532 14384 "GET /api/v1/ HTTP/1.1" 200 1999
INFO 2025-07-11 12:48:26,822 basehttp 13532 14384 - Broken pipe from ('127.0.0.1', 57178)
INFO 2025-07-11 12:48:38,776 basehttp 13532 15352 "GET /api/v1/docs/ HTTP/1.1" 200 1281
INFO 2025-07-11 12:48:38,951 basehttp 13532 15352 - Broken pipe from ('127.0.0.1', 57183)
INFO 2025-07-11 12:49:55,823 autoreload 13532 876 D:\apps\lnk2store\lnk2store\core\api_views.py changed, reloading.
INFO 2025-07-11 12:49:56,458 autoreload 13132 7376 Watching for file changes with StatReloader
INFO 2025-07-11 12:50:09,831 autoreload 13132 7376 D:\apps\lnk2store\lnk2store\core\api_views.py changed, reloading.
INFO 2025-07-11 12:50:10,474 autoreload 14568 19228 Watching for file changes with StatReloader
INFO 2025-07-11 12:50:27,031 autoreload 14568 19228 D:\apps\lnk2store\lnk2store\core\api_urls.py changed, reloading.
INFO 2025-07-11 12:50:27,666 autoreload 17164 1804 Watching for file changes with StatReloader
INFO 2025-07-11 12:50:38,887 basehttp 17164 5900 "GET /api/v1/ HTTP/1.1" 200 1999
INFO 2025-07-11 12:50:47,301 basehttp 17164 5900 "GET /api/v1/docs/html/ HTTP/1.1" 200 8887
INFO 2025-07-11 12:51:00,124 basehttp 17164 15004 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-11 12:51:00,142 basehttp 17164 15004 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4342
WARNING 2025-07-11 12:51:04,858 log 17164 1812 Bad Request: /api/v1/accounts/login/
WARNING 2025-07-11 12:51:04,858 basehttp 17164 1812 "POST /api/v1/accounts/login/ HTTP/1.1" 400 68
INFO 2025-07-11 12:51:06,897 basehttp 17164 3156 "GET /api/v1/products/ HTTP/1.1" 200 2208
INFO 2025-07-11 12:51:08,924 basehttp 17164 11612 "GET /api/v1/products/5/ HTTP/1.1" 200 658
INFO 2025-07-11 12:51:10,969 basehttp 17164 2236 "GET /api/v1/products/search/?q=test HTTP/1.1" 200 39
INFO 2025-07-11 12:51:13,018 basehttp 17164 12044 "GET /api/v1/products/categories/ HTTP/1.1" 200 518
WARNING 2025-07-11 12:51:15,137 log 17164 18596 Unauthorized: /api/v1/leads/my-leads/
WARNING 2025-07-11 12:51:15,137 basehttp 17164 18596 "GET /api/v1/leads/my-leads/ HTTP/1.1" 401 159
WARNING 2025-07-11 12:51:17,212 log 17164 11320 Unauthorized: /api/v1/wallet/
WARNING 2025-07-11 12:51:17,212 basehttp 17164 11320 "GET /api/v1/wallet/ HTTP/1.1" 401 159
INFO 2025-07-11 12:51:19,238 basehttp 17164 20024 "GET /api/v1/templates/templates/ HTTP/1.1" 200 981
INFO 2025-07-11 12:51:21,274 basehttp 17164 16624 "GET /api/v1/templates/templates/categories/ HTTP/1.1" 200 388
INFO 2025-07-11 12:52:19,479 basehttp 17164 5900 "GET /api/v1/ HTTP/1.1" 200 1999
