import re
import json
from django.template import Template as DjangoTemplate, Context
from django.utils.safestring import mark_safe
from django.conf import settings

class TemplateRenderer:
    """Renders marketing templates with user customizations"""
    
    def __init__(self, template, custom_config):
        self.template = template
        self.custom_config = custom_config
        self.config_schema = template.config_schema
    
    def render(self):
        """Render the complete HTML page"""
        try:
            # Process the HTML structure
            html_content = self._process_html()
            
            # Process CSS styles
            css_content = self._process_css()
            
            # Process JavaScript
            js_content = self._process_js()
            
            # Combine everything into a complete HTML page
            full_html = self._build_complete_page(html_content, css_content, js_content)
            
            return mark_safe(full_html)
            
        except Exception as e:
            return f"<div class='error'>خطأ في عرض القالب: {str(e)}</div>"
    
    def _process_html(self):
        """Process HTML template with user data"""
        html = self.template.html_structure
        
        # Replace placeholders with user data
        for key, value in self.custom_config.items():
            placeholder = f"{{{{{key}}}}}"
            if isinstance(value, dict):
                # Handle complex objects
                for sub_key, sub_value in value.items():
                    sub_placeholder = f"{{{{{key}.{sub_key}}}}}"
                    html = html.replace(sub_placeholder, str(sub_value))
            else:
                html = html.replace(placeholder, str(value))
        
        # Process conditional blocks
        html = self._process_conditionals(html)
        
        # Process loops
        html = self._process_loops(html)
        
        return html
    
    def _process_css(self):
        """Process CSS with user customizations"""
        css = self.template.css_styles
        
        # Replace CSS variables with user values
        css_vars = self.custom_config.get('styles', {})
        for var_name, var_value in css_vars.items():
            css = css.replace(f"var(--{var_name})", str(var_value))
        
        return css
    
    def _process_js(self):
        """Process JavaScript with user configurations"""
        js = self.template.js_scripts
        
        # Inject user configuration as JavaScript variables
        config_js = f"window.USER_CONFIG = {json.dumps(self.custom_config)};"
        
        return config_js + js
    
    def _process_conditionals(self, html):
        """Process conditional blocks like {% if condition %}"""
        # Simple conditional processing
        pattern = r'\{\% if (\w+) \%\}(.*?)\{\% endif \%\}'
        
        def replace_conditional(match):
            condition = match.group(1)
            content = match.group(2)
            
            # Check if condition is met in user config
            if self.custom_config.get(condition):
                return content
            return ''
        
        return re.sub(pattern, replace_conditional, html, flags=re.DOTALL)
    
    def _process_loops(self, html):
        """Process loop blocks like {% for item in items %}"""
        pattern = r'\{\% for (\w+) in (\w+) \%\}(.*?)\{\% endfor \%\}'
        
        def replace_loop(match):
            item_var = match.group(1)
            items_var = match.group(2)
            content = match.group(3)
            
            items = self.custom_config.get(items_var, [])
            result = ''
            
            for item in items:
                item_content = content
                if isinstance(item, dict):
                    for key, value in item.items():
                        item_content = item_content.replace(f"{{{{{item_var}.{key}}}}}", str(value))
                else:
                    item_content = item_content.replace(f"{{{{{item_var}}}}}", str(item))
                result += item_content
            
            return result
        
        return re.sub(pattern, replace_loop, html, flags=re.DOTALL)
    
    def _build_complete_page(self, html_content, css_content, js_content):
        """Build complete HTML page"""
        page_title = self.custom_config.get('page_title', 'صفحتي التسويقية')
        meta_description = self.custom_config.get('meta_description', '')
        
        return f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{page_title}</title>
    <meta name="description" content="{meta_description}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {{
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
        }}
        {css_content}
    </style>
</head>
<body>
    {html_content}
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        {js_content}
    </script>
</body>
</html>
        """.strip()

class TemplateValidator:
    """Validates template configurations"""
    
    @staticmethod
    def validate_config(template, custom_config):
        """Validate user configuration against template schema"""
        schema = template.config_schema
        errors = []
        
        # Check required fields
        required_fields = schema.get('required', [])
        for field in required_fields:
            if field not in custom_config:
                errors.append(f"الحقل '{field}' مطلوب")
        
        # Validate field types and constraints
        fields = schema.get('fields', {})
        for field_name, field_config in fields.items():
            if field_name in custom_config:
                value = custom_config[field_name]
                field_errors = TemplateValidator._validate_field(field_name, value, field_config)
                errors.extend(field_errors)
        
        return errors
    
    @staticmethod
    def _validate_field(field_name, value, field_config):
        """Validate individual field"""
        errors = []
        field_type = field_config.get('type', 'string')
        
        # Type validation
        if field_type == 'string' and not isinstance(value, str):
            errors.append(f"الحقل '{field_name}' يجب أن يكون نص")
        elif field_type == 'number' and not isinstance(value, (int, float)):
            errors.append(f"الحقل '{field_name}' يجب أن يكون رقم")
        elif field_type == 'boolean' and not isinstance(value, bool):
            errors.append(f"الحقل '{field_name}' يجب أن يكون true أو false")
        elif field_type == 'array' and not isinstance(value, list):
            errors.append(f"الحقل '{field_name}' يجب أن يكون قائمة")
        
        # Length validation for strings
        if field_type == 'string' and isinstance(value, str):
            min_length = field_config.get('min_length')
            max_length = field_config.get('max_length')
            
            if min_length and len(value) < min_length:
                errors.append(f"الحقل '{field_name}' يجب أن يكون على الأقل {min_length} أحرف")
            if max_length and len(value) > max_length:
                errors.append(f"الحقل '{field_name}' يجب أن يكون أقل من {max_length} حرف")
        
        # Range validation for numbers
        if field_type == 'number' and isinstance(value, (int, float)):
            min_value = field_config.get('min_value')
            max_value = field_config.get('max_value')
            
            if min_value is not None and value < min_value:
                errors.append(f"الحقل '{field_name}' يجب أن يكون على الأقل {min_value}")
            if max_value is not None and value > max_value:
                errors.append(f"الحقل '{field_name}' يجب أن يكون أقل من {max_value}")
        
        return errors
