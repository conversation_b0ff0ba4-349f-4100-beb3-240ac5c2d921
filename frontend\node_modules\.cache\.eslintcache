[{"D:\\apps\\lnk2store\\frontend\\src\\index.js": "1", "D:\\apps\\lnk2store\\frontend\\src\\reportWebVitals.js": "2", "D:\\apps\\lnk2store\\frontend\\src\\App.js": "3", "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useAuth.js": "4", "D:\\apps\\lnk2store\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\apps\\lnk2store\\frontend\\src\\components\\ProtectedRoute.js": "6", "D:\\apps\\lnk2store\\frontend\\src\\pages\\HomePage.js": "7", "D:\\apps\\lnk2store\\frontend\\src\\components\\Layout.js": "8", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductsPage.js": "9", "D:\\apps\\lnk2store\\frontend\\src\\pages\\DashboardPage.js": "10", "D:\\apps\\lnk2store\\frontend\\src\\pages\\LeadsPage.js": "11", "D:\\apps\\lnk2store\\frontend\\src\\pages\\RegisterPage.js": "12", "D:\\apps\\lnk2store\\frontend\\src\\pages\\WalletPage.js": "13", "D:\\apps\\lnk2store\\frontend\\src\\components\\ProductCard.js": "14", "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useApi.js": "15", "D:\\apps\\lnk2store\\frontend\\src\\services\\api.js": "16", "D:\\apps\\lnk2store\\frontend\\src\\pages\\AdminDashboard.js": "17", "D:\\apps\\lnk2store\\frontend\\src\\services\\adminAPI.js": "18", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductCreatePage.js": "19", "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateCustomizePage.js": "20", "D:\\apps\\lnk2store\\frontend\\src\\pages\\MyPagesPage.js": "21", "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateSelectionPage.js": "22", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductDetailPage.js": "23", "D:\\apps\\lnk2store\\frontend\\src\\components\\LeadForm.js": "24", "D:\\apps\\lnk2store\\frontend\\src\\components\\ErrorBoundary.js": "25", "D:\\apps\\lnk2store\\frontend\\src\\components\\Logo.js": "26", "D:\\apps\\lnk2store\\frontend\\src\\components\\ShoppingTemplate.js": "27", "D:\\apps\\lnk2store\\frontend\\src\\contexts\\AppContext.js": "28", "D:\\apps\\lnk2store\\frontend\\src\\components\\SettingsMenu.js": "29"}, {"size": 535, "mtime": 1752100207436, "results": "30", "hashOfConfig": "31"}, {"size": 362, "mtime": 1752100207805, "results": "32", "hashOfConfig": "31"}, {"size": 4010, "mtime": 1752238276061, "results": "33", "hashOfConfig": "31"}, {"size": 3138, "mtime": 1752100397968, "results": "34", "hashOfConfig": "31"}, {"size": 3269, "mtime": 1752190267574, "results": "35", "hashOfConfig": "31"}, {"size": 728, "mtime": 1752100878650, "results": "36", "hashOfConfig": "31"}, {"size": 15386, "mtime": 1752192775631, "results": "37", "hashOfConfig": "31"}, {"size": 5814, "mtime": 1752237117227, "results": "38", "hashOfConfig": "31"}, {"size": 9868, "mtime": 1752236675082, "results": "39", "hashOfConfig": "31"}, {"size": 14161, "mtime": 1752238392811, "results": "40", "hashOfConfig": "31"}, {"size": 11171, "mtime": 1752100869108, "results": "41", "hashOfConfig": "31"}, {"size": 5607, "mtime": 1752190242302, "results": "42", "hashOfConfig": "31"}, {"size": 7952, "mtime": 1752100790868, "results": "43", "hashOfConfig": "31"}, {"size": 8228, "mtime": 1752236908895, "results": "44", "hashOfConfig": "31"}, {"size": 1620, "mtime": 1752100410295, "results": "45", "hashOfConfig": "31"}, {"size": 6910, "mtime": 1752174117978, "results": "46", "hashOfConfig": "31"}, {"size": 10254, "mtime": 1752101721001, "results": "47", "hashOfConfig": "31"}, {"size": 891, "mtime": 1752101676962, "results": "48", "hashOfConfig": "31"}, {"size": 11163, "mtime": 1752174330146, "results": "49", "hashOfConfig": "31"}, {"size": 16053, "mtime": 1752177664504, "results": "50", "hashOfConfig": "31"}, {"size": 12424, "mtime": 1752173890183, "results": "51", "hashOfConfig": "31"}, {"size": 10320, "mtime": 1752174400165, "results": "52", "hashOfConfig": "31"}, {"size": 9944, "mtime": 1752173697126, "results": "53", "hashOfConfig": "31"}, {"size": 3812, "mtime": 1752100442092, "results": "54", "hashOfConfig": "31"}, {"size": 3879, "mtime": 1752176372047, "results": "55", "hashOfConfig": "31"}, {"size": 1717, "mtime": 1752191477728, "results": "56", "hashOfConfig": "31"}, {"size": 12146, "mtime": 1752237777347, "results": "57", "hashOfConfig": "31"}, {"size": 5697, "mtime": 1752236427970, "results": "58", "hashOfConfig": "31"}, {"size": 5341, "mtime": 1752238357237, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rv5rso", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\apps\\lnk2store\\frontend\\src\\index.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\reportWebVitals.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\App.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useAuth.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\HomePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\Layout.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductsPage.js", ["147", "148", "149"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\DashboardPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\LeadsPage.js", ["150"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\RegisterPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\WalletPage.js", ["151"], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ProductCard.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useApi.js", ["152", "153"], [], "D:\\apps\\lnk2store\\frontend\\src\\services\\api.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\AdminDashboard.js", ["154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164"], [], "D:\\apps\\lnk2store\\frontend\\src\\services\\adminAPI.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductCreatePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateCustomizePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\MyPagesPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateSelectionPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\LeadForm.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ErrorBoundary.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\Logo.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ShoppingTemplate.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\contexts\\AppContext.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\SettingsMenu.js", [], [], {"ruleId": "165", "severity": 1, "message": "166", "line": 1, "column": 27, "nodeType": "167", "messageId": "168", "endLine": 1, "endColumn": 36}, {"ruleId": "165", "severity": 1, "message": "169", "line": 35, "column": 11, "nodeType": "167", "messageId": "168", "endLine": 35, "endColumn": 25}, {"ruleId": "165", "severity": 1, "message": "170", "line": 35, "column": 34, "nodeType": "167", "messageId": "168", "endLine": 35, "endColumn": 40}, {"ruleId": "165", "severity": 1, "message": "171", "line": 24, "column": 3, "nodeType": "167", "messageId": "168", "endLine": 24, "endColumn": 8}, {"ruleId": "165", "severity": 1, "message": "172", "line": 15, "column": 3, "nodeType": "167", "messageId": "168", "endLine": 15, "endColumn": 8}, {"ruleId": "173", "severity": 1, "message": "174", "line": 25, "column": 6, "nodeType": "167", "endLine": 25, "endColumn": 18}, {"ruleId": "173", "severity": 1, "message": "175", "line": 25, "column": 6, "nodeType": "167", "endLine": 25, "endColumn": 18, "suggestions": "176"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 1, "column": 27, "nodeType": "167", "messageId": "168", "endLine": 1, "endColumn": 36}, {"ruleId": "165", "severity": 1, "message": "177", "line": 31, "column": 3, "nodeType": "167", "messageId": "168", "endLine": 31, "endColumn": 13}, {"ruleId": "165", "severity": 1, "message": "178", "line": 41, "column": 21, "nodeType": "167", "messageId": "168", "endLine": 41, "endColumn": 33}, {"ruleId": "165", "severity": 1, "message": "179", "line": 42, "column": 28, "nodeType": "167", "messageId": "168", "endLine": 42, "endColumn": 47}, {"ruleId": "165", "severity": 1, "message": "180", "line": 44, "column": 23, "nodeType": "167", "messageId": "168", "endLine": 44, "endColumn": 37}, {"ruleId": "165", "severity": 1, "message": "181", "line": 49, "column": 17, "nodeType": "167", "messageId": "168", "endLine": 49, "endColumn": 23}, {"ruleId": "165", "severity": 1, "message": "182", "line": 49, "column": 34, "nodeType": "167", "messageId": "168", "endLine": 49, "endColumn": 47}, {"ruleId": "165", "severity": 1, "message": "183", "line": 53, "column": 17, "nodeType": "167", "messageId": "168", "endLine": 53, "endColumn": 22}, {"ruleId": "165", "severity": 1, "message": "184", "line": 53, "column": 33, "nodeType": "167", "messageId": "168", "endLine": 53, "endColumn": 45}, {"ruleId": "165", "severity": 1, "message": "185", "line": 56, "column": 17, "nodeType": "167", "messageId": "168", "endLine": 56, "endColumn": 29}, {"ruleId": "165", "severity": 1, "message": "186", "line": 56, "column": 40, "nodeType": "167", "messageId": "168", "endLine": 56, "endColumn": 59}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'formatCurrency' is assigned a value but never used.", "'isDark' is assigned a value but never used.", "'Alert' is defined but never used.", "'Paper' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", "React Hook useEffect has a missing dependency: 'apiFunction'. Either include it or remove the dependency array. If 'apiFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["187"], "'FilterList' is defined but never used.", "'setLeadsPage' is assigned a value but never used.", "'setTransactionsPage' is assigned a value but never used.", "'setLeadsFilter' is assigned a value but never used.", "'charts' is assigned a value but never used.", "'chartsLoading' is assigned a value but never used.", "'leads' is assigned a value but never used.", "'leadsLoading' is assigned a value but never used.", "'transactions' is assigned a value but never used.", "'transactionsLoading' is assigned a value but never used.", {"desc": "188", "fix": "189"}, "Update the dependencies array to be: [apiFunction]", {"range": "190", "text": "191"}, [661, 673], "[apiFunction]"]