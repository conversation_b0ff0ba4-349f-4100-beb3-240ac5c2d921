[{"D:\\apps\\lnk2store\\frontend\\src\\index.js": "1", "D:\\apps\\lnk2store\\frontend\\src\\reportWebVitals.js": "2", "D:\\apps\\lnk2store\\frontend\\src\\App.js": "3", "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useAuth.js": "4", "D:\\apps\\lnk2store\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\apps\\lnk2store\\frontend\\src\\components\\ProtectedRoute.js": "6", "D:\\apps\\lnk2store\\frontend\\src\\pages\\HomePage.js": "7", "D:\\apps\\lnk2store\\frontend\\src\\components\\Layout.js": "8", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductsPage.js": "9", "D:\\apps\\lnk2store\\frontend\\src\\pages\\DashboardPage.js": "10", "D:\\apps\\lnk2store\\frontend\\src\\pages\\LeadsPage.js": "11", "D:\\apps\\lnk2store\\frontend\\src\\pages\\RegisterPage.js": "12", "D:\\apps\\lnk2store\\frontend\\src\\pages\\WalletPage.js": "13", "D:\\apps\\lnk2store\\frontend\\src\\components\\ProductCard.js": "14", "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useApi.js": "15", "D:\\apps\\lnk2store\\frontend\\src\\services\\api.js": "16", "D:\\apps\\lnk2store\\frontend\\src\\pages\\AdminDashboard.js": "17", "D:\\apps\\lnk2store\\frontend\\src\\services\\adminAPI.js": "18", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductCreatePage.js": "19", "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateCustomizePage.js": "20", "D:\\apps\\lnk2store\\frontend\\src\\pages\\MyPagesPage.js": "21", "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateSelectionPage.js": "22", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductDetailPage.js": "23", "D:\\apps\\lnk2store\\frontend\\src\\components\\LeadForm.js": "24", "D:\\apps\\lnk2store\\frontend\\src\\components\\ErrorBoundary.js": "25"}, {"size": 535, "mtime": 1752100207436, "results": "26", "hashOfConfig": "27"}, {"size": 362, "mtime": 1752100207805, "results": "28", "hashOfConfig": "27"}, {"size": 3820, "mtime": 1752176464992, "results": "29", "hashOfConfig": "27"}, {"size": 3138, "mtime": 1752100397968, "results": "30", "hashOfConfig": "27"}, {"size": 2866, "mtime": 1752100508222, "results": "31", "hashOfConfig": "27"}, {"size": 728, "mtime": 1752100878650, "results": "32", "hashOfConfig": "27"}, {"size": 6009, "mtime": 1752100491350, "results": "33", "hashOfConfig": "27"}, {"size": 2499, "mtime": 1752173977936, "results": "34", "hashOfConfig": "27"}, {"size": 4705, "mtime": 1752100901600, "results": "35", "hashOfConfig": "27"}, {"size": 9620, "mtime": 1752100829145, "results": "36", "hashOfConfig": "27"}, {"size": 11171, "mtime": 1752100869108, "results": "37", "hashOfConfig": "27"}, {"size": 4747, "mtime": 1752100527205, "results": "38", "hashOfConfig": "27"}, {"size": 7952, "mtime": 1752100790868, "results": "39", "hashOfConfig": "27"}, {"size": 2931, "mtime": 1752100458914, "results": "40", "hashOfConfig": "27"}, {"size": 1620, "mtime": 1752100410295, "results": "41", "hashOfConfig": "27"}, {"size": 6910, "mtime": 1752174117978, "results": "42", "hashOfConfig": "27"}, {"size": 10254, "mtime": 1752101721001, "results": "43", "hashOfConfig": "27"}, {"size": 891, "mtime": 1752101676962, "results": "44", "hashOfConfig": "27"}, {"size": 11163, "mtime": 1752174330146, "results": "45", "hashOfConfig": "27"}, {"size": 15964, "mtime": 1752176298870, "results": "46", "hashOfConfig": "27"}, {"size": 12424, "mtime": 1752173890183, "results": "47", "hashOfConfig": "27"}, {"size": 10320, "mtime": 1752174400165, "results": "48", "hashOfConfig": "27"}, {"size": 9944, "mtime": 1752173697126, "results": "49", "hashOfConfig": "27"}, {"size": 3812, "mtime": 1752100442092, "results": "50", "hashOfConfig": "27"}, {"size": 3879, "mtime": 1752176372047, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rv5rso", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\apps\\lnk2store\\frontend\\src\\index.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\reportWebVitals.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\App.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useAuth.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\HomePage.js", ["127", "128"], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\Layout.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductsPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\DashboardPage.js", ["129"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\LeadsPage.js", ["130"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\RegisterPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\WalletPage.js", ["131"], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ProductCard.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useApi.js", ["132", "133"], [], "D:\\apps\\lnk2store\\frontend\\src\\services\\api.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\AdminDashboard.js", ["134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144"], [], "D:\\apps\\lnk2store\\frontend\\src\\services\\adminAPI.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductCreatePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateCustomizePage.js", ["145"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\MyPagesPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateSelectionPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\LeadForm.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ErrorBoundary.js", [], [], {"ruleId": "146", "severity": 1, "message": "147", "line": 10, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 10, "endColumn": 14}, {"ruleId": "146", "severity": 1, "message": "150", "line": 20, "column": 27, "nodeType": "148", "messageId": "149", "endLine": 20, "endColumn": 34}, {"ruleId": "146", "severity": 1, "message": "151", "line": 15, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 15, "endColumn": 8}, {"ruleId": "146", "severity": 1, "message": "152", "line": 24, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 24, "endColumn": 8}, {"ruleId": "146", "severity": 1, "message": "151", "line": 15, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 15, "endColumn": 8}, {"ruleId": "153", "severity": 1, "message": "154", "line": 25, "column": 6, "nodeType": "148", "endLine": 25, "endColumn": 18}, {"ruleId": "153", "severity": 1, "message": "155", "line": 25, "column": 6, "nodeType": "148", "endLine": 25, "endColumn": 18, "suggestions": "156"}, {"ruleId": "146", "severity": 1, "message": "157", "line": 1, "column": 27, "nodeType": "148", "messageId": "149", "endLine": 1, "endColumn": 36}, {"ruleId": "146", "severity": 1, "message": "158", "line": 31, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 31, "endColumn": 13}, {"ruleId": "146", "severity": 1, "message": "159", "line": 41, "column": 21, "nodeType": "148", "messageId": "149", "endLine": 41, "endColumn": 33}, {"ruleId": "146", "severity": 1, "message": "160", "line": 42, "column": 28, "nodeType": "148", "messageId": "149", "endLine": 42, "endColumn": 47}, {"ruleId": "146", "severity": 1, "message": "161", "line": 44, "column": 23, "nodeType": "148", "messageId": "149", "endLine": 44, "endColumn": 37}, {"ruleId": "146", "severity": 1, "message": "162", "line": 49, "column": 17, "nodeType": "148", "messageId": "149", "endLine": 49, "endColumn": 23}, {"ruleId": "146", "severity": 1, "message": "163", "line": 49, "column": 34, "nodeType": "148", "messageId": "149", "endLine": 49, "endColumn": 47}, {"ruleId": "146", "severity": 1, "message": "164", "line": 53, "column": 17, "nodeType": "148", "messageId": "149", "endLine": 53, "endColumn": 22}, {"ruleId": "146", "severity": 1, "message": "165", "line": 53, "column": 33, "nodeType": "148", "messageId": "149", "endLine": 53, "endColumn": 45}, {"ruleId": "146", "severity": 1, "message": "166", "line": 56, "column": 17, "nodeType": "148", "messageId": "149", "endLine": 56, "endColumn": 29}, {"ruleId": "146", "severity": 1, "message": "167", "line": 56, "column": 40, "nodeType": "148", "messageId": "149", "endLine": 56, "endColumn": 59}, {"ruleId": "146", "severity": 1, "message": "168", "line": 27, "column": 10, "nodeType": "148", "messageId": "149", "endLine": 27, "endColumn": 19}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "'loading' is assigned a value but never used.", "'Paper' is defined but never used.", "'Alert' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", "React Hook useEffect has a missing dependency: 'apiFunction'. Either include it or remove the dependency array. If 'apiFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["169"], "'useEffect' is defined but never used.", "'FilterList' is defined but never used.", "'setLeadsPage' is assigned a value but never used.", "'setTransactionsPage' is assigned a value but never used.", "'setLeadsFilter' is assigned a value but never used.", "'charts' is assigned a value but never used.", "'chartsLoading' is assigned a value but never used.", "'leads' is assigned a value but never used.", "'leadsLoading' is assigned a value but never used.", "'transactions' is assigned a value but never used.", "'transactionsLoading' is assigned a value but never used.", "'useParams' is defined but never used.", {"desc": "170", "fix": "171"}, "Update the dependencies array to be: [apiFunction]", {"range": "172", "text": "173"}, [661, 673], "[apiFunction]"]