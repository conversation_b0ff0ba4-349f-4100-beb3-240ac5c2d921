[{"D:\\apps\\lnk2store\\frontend\\src\\index.js": "1", "D:\\apps\\lnk2store\\frontend\\src\\reportWebVitals.js": "2", "D:\\apps\\lnk2store\\frontend\\src\\App.js": "3", "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useAuth.js": "4", "D:\\apps\\lnk2store\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\apps\\lnk2store\\frontend\\src\\components\\ProtectedRoute.js": "6", "D:\\apps\\lnk2store\\frontend\\src\\pages\\HomePage.js": "7", "D:\\apps\\lnk2store\\frontend\\src\\components\\Layout.js": "8", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductsPage.js": "9", "D:\\apps\\lnk2store\\frontend\\src\\pages\\DashboardPage.js": "10", "D:\\apps\\lnk2store\\frontend\\src\\pages\\LeadsPage.js": "11", "D:\\apps\\lnk2store\\frontend\\src\\pages\\RegisterPage.js": "12", "D:\\apps\\lnk2store\\frontend\\src\\pages\\WalletPage.js": "13", "D:\\apps\\lnk2store\\frontend\\src\\components\\ProductCard.js": "14", "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useApi.js": "15", "D:\\apps\\lnk2store\\frontend\\src\\services\\api.js": "16", "D:\\apps\\lnk2store\\frontend\\src\\pages\\AdminDashboard.js": "17", "D:\\apps\\lnk2store\\frontend\\src\\services\\adminAPI.js": "18", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductCreatePage.js": "19", "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateCustomizePage.js": "20", "D:\\apps\\lnk2store\\frontend\\src\\pages\\MyPagesPage.js": "21", "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateSelectionPage.js": "22", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductDetailPage.js": "23", "D:\\apps\\lnk2store\\frontend\\src\\components\\LeadForm.js": "24", "D:\\apps\\lnk2store\\frontend\\src\\components\\ErrorBoundary.js": "25", "D:\\apps\\lnk2store\\frontend\\src\\components\\Logo.js": "26", "D:\\apps\\lnk2store\\frontend\\src\\components\\ShoppingTemplate.js": "27"}, {"size": 535, "mtime": 1752100207436, "results": "28", "hashOfConfig": "29"}, {"size": 362, "mtime": 1752100207805, "results": "30", "hashOfConfig": "29"}, {"size": 4768, "mtime": 1752189184896, "results": "31", "hashOfConfig": "29"}, {"size": 3138, "mtime": 1752100397968, "results": "32", "hashOfConfig": "29"}, {"size": 3269, "mtime": 1752190267574, "results": "33", "hashOfConfig": "29"}, {"size": 728, "mtime": 1752100878650, "results": "34", "hashOfConfig": "29"}, {"size": 17721, "mtime": 1752189790028, "results": "35", "hashOfConfig": "29"}, {"size": 4035, "mtime": 1752188120100, "results": "36", "hashOfConfig": "29"}, {"size": 4705, "mtime": 1752100901600, "results": "37", "hashOfConfig": "29"}, {"size": 9620, "mtime": 1752100829145, "results": "38", "hashOfConfig": "29"}, {"size": 11171, "mtime": 1752100869108, "results": "39", "hashOfConfig": "29"}, {"size": 5607, "mtime": 1752190242302, "results": "40", "hashOfConfig": "29"}, {"size": 7952, "mtime": 1752100790868, "results": "41", "hashOfConfig": "29"}, {"size": 2931, "mtime": 1752100458914, "results": "42", "hashOfConfig": "29"}, {"size": 1620, "mtime": 1752100410295, "results": "43", "hashOfConfig": "29"}, {"size": 6910, "mtime": 1752174117978, "results": "44", "hashOfConfig": "29"}, {"size": 10254, "mtime": 1752101721001, "results": "45", "hashOfConfig": "29"}, {"size": 891, "mtime": 1752101676962, "results": "46", "hashOfConfig": "29"}, {"size": 11163, "mtime": 1752174330146, "results": "47", "hashOfConfig": "29"}, {"size": 16053, "mtime": 1752177664504, "results": "48", "hashOfConfig": "29"}, {"size": 12424, "mtime": 1752173890183, "results": "49", "hashOfConfig": "29"}, {"size": 10320, "mtime": 1752174400165, "results": "50", "hashOfConfig": "29"}, {"size": 9944, "mtime": 1752173697126, "results": "51", "hashOfConfig": "29"}, {"size": 3812, "mtime": 1752100442092, "results": "52", "hashOfConfig": "29"}, {"size": 3879, "mtime": 1752176372047, "results": "53", "hashOfConfig": "29"}, {"size": 1862, "mtime": 1752189846629, "results": "54", "hashOfConfig": "29"}, {"size": 11513, "mtime": 1752189129768, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rv5rso", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\apps\\lnk2store\\frontend\\src\\index.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\reportWebVitals.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\App.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useAuth.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\HomePage.js", ["137"], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\Layout.js", ["138", "139"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductsPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\DashboardPage.js", ["140"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\LeadsPage.js", ["141"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\RegisterPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\WalletPage.js", ["142"], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ProductCard.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useApi.js", ["143", "144"], [], "D:\\apps\\lnk2store\\frontend\\src\\services\\api.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\AdminDashboard.js", ["145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155"], [], "D:\\apps\\lnk2store\\frontend\\src\\services\\adminAPI.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductCreatePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateCustomizePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\MyPagesPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateSelectionPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\LeadForm.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ErrorBoundary.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\Logo.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ShoppingTemplate.js", ["156"], [], {"ruleId": "157", "severity": 1, "message": "158", "line": 318, "column": 15, "nodeType": "159", "messageId": "160", "endLine": 318, "endColumn": 46}, {"ruleId": "161", "severity": 1, "message": "162", "line": 2, "column": 27, "nodeType": "163", "messageId": "164", "endLine": 2, "endColumn": 37}, {"ruleId": "161", "severity": 1, "message": "165", "line": 2, "column": 52, "nodeType": "163", "messageId": "164", "endLine": 2, "endColumn": 61}, {"ruleId": "161", "severity": 1, "message": "166", "line": 15, "column": 3, "nodeType": "163", "messageId": "164", "endLine": 15, "endColumn": 8}, {"ruleId": "161", "severity": 1, "message": "167", "line": 24, "column": 3, "nodeType": "163", "messageId": "164", "endLine": 24, "endColumn": 8}, {"ruleId": "161", "severity": 1, "message": "166", "line": 15, "column": 3, "nodeType": "163", "messageId": "164", "endLine": 15, "endColumn": 8}, {"ruleId": "168", "severity": 1, "message": "169", "line": 25, "column": 6, "nodeType": "163", "endLine": 25, "endColumn": 18}, {"ruleId": "168", "severity": 1, "message": "170", "line": 25, "column": 6, "nodeType": "163", "endLine": 25, "endColumn": 18, "suggestions": "171"}, {"ruleId": "161", "severity": 1, "message": "172", "line": 1, "column": 27, "nodeType": "163", "messageId": "164", "endLine": 1, "endColumn": 36}, {"ruleId": "161", "severity": 1, "message": "173", "line": 31, "column": 3, "nodeType": "163", "messageId": "164", "endLine": 31, "endColumn": 13}, {"ruleId": "161", "severity": 1, "message": "174", "line": 41, "column": 21, "nodeType": "163", "messageId": "164", "endLine": 41, "endColumn": 33}, {"ruleId": "161", "severity": 1, "message": "175", "line": 42, "column": 28, "nodeType": "163", "messageId": "164", "endLine": 42, "endColumn": 47}, {"ruleId": "161", "severity": 1, "message": "176", "line": 44, "column": 23, "nodeType": "163", "messageId": "164", "endLine": 44, "endColumn": 37}, {"ruleId": "161", "severity": 1, "message": "177", "line": 49, "column": 17, "nodeType": "163", "messageId": "164", "endLine": 49, "endColumn": 23}, {"ruleId": "161", "severity": 1, "message": "178", "line": 49, "column": 34, "nodeType": "163", "messageId": "164", "endLine": 49, "endColumn": 47}, {"ruleId": "161", "severity": 1, "message": "179", "line": 53, "column": 17, "nodeType": "163", "messageId": "164", "endLine": 53, "endColumn": 22}, {"ruleId": "161", "severity": 1, "message": "180", "line": 53, "column": 33, "nodeType": "163", "messageId": "164", "endLine": 53, "endColumn": 45}, {"ruleId": "161", "severity": 1, "message": "181", "line": 56, "column": 17, "nodeType": "163", "messageId": "164", "endLine": 56, "endColumn": 29}, {"ruleId": "161", "severity": 1, "message": "182", "line": 56, "column": 40, "nodeType": "163", "messageId": "164", "endLine": 56, "endColumn": 59}, {"ruleId": "161", "severity": 1, "message": "183", "line": 10, "column": 3, "nodeType": "163", "messageId": "164", "endLine": 10, "endColumn": 14}, "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "no-unused-vars", "'Typography' is defined but never used.", "Identifier", "unusedVar", "'Container' is defined but never used.", "'Paper' is defined but never used.", "'Alert' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", "React Hook useEffect has a missing dependency: 'apiFunction'. Either include it or remove the dependency array. If 'apiFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["184"], "'useEffect' is defined but never used.", "'FilterList' is defined but never used.", "'setLeadsPage' is assigned a value but never used.", "'setTransactionsPage' is assigned a value but never used.", "'setLeadsFilter' is assigned a value but never used.", "'charts' is assigned a value but never used.", "'chartsLoading' is assigned a value but never used.", "'leads' is assigned a value but never used.", "'leadsLoading' is assigned a value but never used.", "'transactions' is assigned a value but never used.", "'transactionsLoading' is assigned a value but never used.", "'CardContent' is defined but never used.", {"desc": "185", "fix": "186"}, "Update the dependencies array to be: [apiFunction]", {"range": "187", "text": "188"}, [661, 673], "[apiFunction]"]