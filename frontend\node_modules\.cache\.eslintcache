[{"D:\\apps\\lnk2store\\frontend\\src\\index.js": "1", "D:\\apps\\lnk2store\\frontend\\src\\reportWebVitals.js": "2", "D:\\apps\\lnk2store\\frontend\\src\\App.js": "3", "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useAuth.js": "4", "D:\\apps\\lnk2store\\frontend\\src\\pages\\LoginPage.js": "5", "D:\\apps\\lnk2store\\frontend\\src\\components\\ProtectedRoute.js": "6", "D:\\apps\\lnk2store\\frontend\\src\\pages\\HomePage.js": "7", "D:\\apps\\lnk2store\\frontend\\src\\components\\Layout.js": "8", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductsPage.js": "9", "D:\\apps\\lnk2store\\frontend\\src\\pages\\DashboardPage.js": "10", "D:\\apps\\lnk2store\\frontend\\src\\pages\\LeadsPage.js": "11", "D:\\apps\\lnk2store\\frontend\\src\\pages\\RegisterPage.js": "12", "D:\\apps\\lnk2store\\frontend\\src\\pages\\WalletPage.js": "13", "D:\\apps\\lnk2store\\frontend\\src\\components\\ProductCard.js": "14", "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useApi.js": "15", "D:\\apps\\lnk2store\\frontend\\src\\services\\api.js": "16", "D:\\apps\\lnk2store\\frontend\\src\\pages\\AdminDashboard.js": "17", "D:\\apps\\lnk2store\\frontend\\src\\services\\adminAPI.js": "18", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductCreatePage.js": "19", "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateCustomizePage.js": "20", "D:\\apps\\lnk2store\\frontend\\src\\pages\\MyPagesPage.js": "21", "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateSelectionPage.js": "22", "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductDetailPage.js": "23", "D:\\apps\\lnk2store\\frontend\\src\\components\\LeadForm.js": "24", "D:\\apps\\lnk2store\\frontend\\src\\components\\ErrorBoundary.js": "25", "D:\\apps\\lnk2store\\frontend\\src\\components\\Logo.js": "26", "D:\\apps\\lnk2store\\frontend\\src\\components\\ShoppingTemplate.js": "27", "D:\\apps\\lnk2store\\frontend\\src\\contexts\\AppContext.js": "28"}, {"size": 535, "mtime": 1752100207436, "results": "29", "hashOfConfig": "30"}, {"size": 362, "mtime": 1752100207805, "results": "31", "hashOfConfig": "30"}, {"size": 4882, "mtime": 1752236927997, "results": "32", "hashOfConfig": "30"}, {"size": 3138, "mtime": 1752100397968, "results": "33", "hashOfConfig": "30"}, {"size": 3269, "mtime": 1752190267574, "results": "34", "hashOfConfig": "30"}, {"size": 728, "mtime": 1752100878650, "results": "35", "hashOfConfig": "30"}, {"size": 15386, "mtime": 1752192775631, "results": "36", "hashOfConfig": "30"}, {"size": 5740, "mtime": 1752192675523, "results": "37", "hashOfConfig": "30"}, {"size": 9868, "mtime": 1752236675082, "results": "38", "hashOfConfig": "30"}, {"size": 9620, "mtime": 1752100829145, "results": "39", "hashOfConfig": "30"}, {"size": 11171, "mtime": 1752100869108, "results": "40", "hashOfConfig": "30"}, {"size": 5607, "mtime": 1752190242302, "results": "41", "hashOfConfig": "30"}, {"size": 7952, "mtime": 1752100790868, "results": "42", "hashOfConfig": "30"}, {"size": 8228, "mtime": 1752236908895, "results": "43", "hashOfConfig": "30"}, {"size": 1620, "mtime": 1752100410295, "results": "44", "hashOfConfig": "30"}, {"size": 6910, "mtime": 1752174117978, "results": "45", "hashOfConfig": "30"}, {"size": 10254, "mtime": 1752101721001, "results": "46", "hashOfConfig": "30"}, {"size": 891, "mtime": 1752101676962, "results": "47", "hashOfConfig": "30"}, {"size": 11163, "mtime": 1752174330146, "results": "48", "hashOfConfig": "30"}, {"size": 16053, "mtime": 1752177664504, "results": "49", "hashOfConfig": "30"}, {"size": 12424, "mtime": 1752173890183, "results": "50", "hashOfConfig": "30"}, {"size": 10320, "mtime": 1752174400165, "results": "51", "hashOfConfig": "30"}, {"size": 9944, "mtime": 1752173697126, "results": "52", "hashOfConfig": "30"}, {"size": 3812, "mtime": 1752100442092, "results": "53", "hashOfConfig": "30"}, {"size": 3879, "mtime": 1752176372047, "results": "54", "hashOfConfig": "30"}, {"size": 1717, "mtime": 1752191477728, "results": "55", "hashOfConfig": "30"}, {"size": 12255, "mtime": 1752191839123, "results": "56", "hashOfConfig": "30"}, {"size": 5697, "mtime": 1752236427970, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rv5rso", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\apps\\lnk2store\\frontend\\src\\index.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\reportWebVitals.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\App.js", ["142", "143", "144", "145"], [], "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useAuth.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\HomePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\Layout.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductsPage.js", ["146", "147", "148"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\DashboardPage.js", ["149"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\LeadsPage.js", ["150"], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\RegisterPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\WalletPage.js", ["151"], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ProductCard.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\hooks\\useApi.js", ["152", "153"], [], "D:\\apps\\lnk2store\\frontend\\src\\services\\api.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\AdminDashboard.js", ["154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164"], [], "D:\\apps\\lnk2store\\frontend\\src\\services\\adminAPI.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductCreatePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateCustomizePage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\MyPagesPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\TemplateSelectionPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\LeadForm.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ErrorBoundary.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\Logo.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\components\\ShoppingTemplate.js", [], [], "D:\\apps\\lnk2store\\frontend\\src\\contexts\\AppContext.js", [], [], {"ruleId": "165", "severity": 1, "message": "166", "line": 4, "column": 10, "nodeType": "167", "messageId": "168", "endLine": 4, "endColumn": 21}, {"ruleId": "169", "severity": 2, "message": "170", "line": 25, "column": 15, "nodeType": "167", "messageId": "171", "endLine": 25, "endColumn": 26}, {"ruleId": "172", "severity": 2, "message": "173", "line": 98, "column": 8, "nodeType": "174", "messageId": "175", "endLine": 98, "endColumn": 21}, {"ruleId": "172", "severity": 2, "message": "176", "line": 99, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 99, "endColumn": 21}, {"ruleId": "165", "severity": 1, "message": "177", "line": 1, "column": 27, "nodeType": "167", "messageId": "168", "endLine": 1, "endColumn": 36}, {"ruleId": "165", "severity": 1, "message": "178", "line": 35, "column": 11, "nodeType": "167", "messageId": "168", "endLine": 35, "endColumn": 25}, {"ruleId": "165", "severity": 1, "message": "179", "line": 35, "column": 34, "nodeType": "167", "messageId": "168", "endLine": 35, "endColumn": 40}, {"ruleId": "165", "severity": 1, "message": "180", "line": 15, "column": 3, "nodeType": "167", "messageId": "168", "endLine": 15, "endColumn": 8}, {"ruleId": "165", "severity": 1, "message": "181", "line": 24, "column": 3, "nodeType": "167", "messageId": "168", "endLine": 24, "endColumn": 8}, {"ruleId": "165", "severity": 1, "message": "180", "line": 15, "column": 3, "nodeType": "167", "messageId": "168", "endLine": 15, "endColumn": 8}, {"ruleId": "182", "severity": 1, "message": "183", "line": 25, "column": 6, "nodeType": "167", "endLine": 25, "endColumn": 18}, {"ruleId": "182", "severity": 1, "message": "184", "line": 25, "column": 6, "nodeType": "167", "endLine": 25, "endColumn": 18, "suggestions": "185"}, {"ruleId": "165", "severity": 1, "message": "177", "line": 1, "column": 27, "nodeType": "167", "messageId": "168", "endLine": 1, "endColumn": 36}, {"ruleId": "165", "severity": 1, "message": "186", "line": 31, "column": 3, "nodeType": "167", "messageId": "168", "endLine": 31, "endColumn": 13}, {"ruleId": "165", "severity": 1, "message": "187", "line": 41, "column": 21, "nodeType": "167", "messageId": "168", "endLine": 41, "endColumn": 33}, {"ruleId": "165", "severity": 1, "message": "188", "line": 42, "column": 28, "nodeType": "167", "messageId": "168", "endLine": 42, "endColumn": 47}, {"ruleId": "165", "severity": 1, "message": "189", "line": 44, "column": 23, "nodeType": "167", "messageId": "168", "endLine": 44, "endColumn": 37}, {"ruleId": "165", "severity": 1, "message": "190", "line": 49, "column": 17, "nodeType": "167", "messageId": "168", "endLine": 49, "endColumn": 23}, {"ruleId": "165", "severity": 1, "message": "191", "line": 49, "column": 34, "nodeType": "167", "messageId": "168", "endLine": 49, "endColumn": 47}, {"ruleId": "165", "severity": 1, "message": "192", "line": 53, "column": 17, "nodeType": "167", "messageId": "168", "endLine": 53, "endColumn": 22}, {"ruleId": "165", "severity": 1, "message": "193", "line": 53, "column": 33, "nodeType": "167", "messageId": "168", "endLine": 53, "endColumn": 45}, {"ruleId": "165", "severity": 1, "message": "194", "line": 56, "column": 17, "nodeType": "167", "messageId": "168", "endLine": 56, "endColumn": 29}, {"ruleId": "165", "severity": 1, "message": "195", "line": 56, "column": 40, "nodeType": "167", "messageId": "168", "endLine": 56, "endColumn": 59}, "no-unused-vars", "'AppProvider' is defined but never used.", "Identifier", "unusedVar", "no-undef", "'createTheme' is not defined.", "undef", "react/jsx-no-undef", "'ThemeProvider' is not defined.", "JSXIdentifier", "undefined", "'CssBaseline' is not defined.", "'useEffect' is defined but never used.", "'formatCurrency' is assigned a value but never used.", "'isDark' is assigned a value but never used.", "'Paper' is defined but never used.", "'Alert' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", "React Hook useEffect has a missing dependency: 'apiFunction'. Either include it or remove the dependency array. If 'apiFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["196"], "'FilterList' is defined but never used.", "'setLeadsPage' is assigned a value but never used.", "'setTransactionsPage' is assigned a value but never used.", "'setLeadsFilter' is assigned a value but never used.", "'charts' is assigned a value but never used.", "'chartsLoading' is assigned a value but never used.", "'leads' is assigned a value but never used.", "'leadsLoading' is assigned a value but never used.", "'transactions' is assigned a value but never used.", "'transactionsLoading' is assigned a value but never used.", {"desc": "197", "fix": "198"}, "Update the dependencies array to be: [apiFunction]", {"range": "199", "text": "200"}, [661, 673], "[apiFunction]"]