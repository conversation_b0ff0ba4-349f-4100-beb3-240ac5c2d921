# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Django Configuration
DJANGO_ENVIRONMENT=development
SECRET_KEY=your-secret-key-here
DEBUG=True

# Database Configuration (Production)
DB_NAME=lnk2store_prod
DB_USER=lnk2store_user
DB_PASSWORD=your-database-password
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://127.0.0.1:6379/1

# Celery Configuration
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Server Configuration
SERVER_IP=your-server-ip

# WhatsApp Configuration
WHATSAPP_API_URL=https://api.whatsapp.com/send
WHATSAPP_BUSINESS_PHONE=+966500000000

# Monitoring (Optional)
SENTRY_DSN=your-sentry-dsn-here

# SSL Configuration
SSL_CERTIFICATE_PATH=/etc/ssl/certs/lnk2store.com.crt
SSL_PRIVATE_KEY_PATH=/etc/ssl/private/lnk2store.com.key
