from django.urls import path
from . import template_api_views

urlpatterns = [
    # Template endpoints
    path('templates/', template_api_views.TemplateListView.as_view(), name='api_template_list'),
    path('templates/<int:pk>/', template_api_views.TemplateDetailView.as_view(), name='api_template_detail'),
    path('templates/categories/', template_api_views.template_categories, name='api_template_categories'),
    path('templates/types/', template_api_views.template_types, name='api_template_types'),
    
    # Template reviews
    path('templates/<int:template_id>/reviews/', template_api_views.TemplateReviewListView.as_view(), name='api_template_reviews'),
    path('templates/reviews/create/', template_api_views.TemplateReviewCreateView.as_view(), name='api_template_review_create'),
    
    # User pages management
    path('my-pages/', template_api_views.UserPageListView.as_view(), name='api_user_pages'),
    path('my-pages/create/', template_api_views.UserPageCreateView.as_view(), name='api_user_page_create'),
    path('my-pages/<int:pk>/', template_api_views.UserPageDetailView.as_view(), name='api_user_page_detail'),
    path('my-pages/<int:pk>/update/', template_api_views.UserPageUpdateView.as_view(), name='api_user_page_update'),
    path('my-pages/<int:pk>/delete/', template_api_views.UserPageDeleteView.as_view(), name='api_user_page_delete'),
    path('my-pages/<int:pk>/publish/', template_api_views.publish_user_page, name='api_user_page_publish'),
    path('my-pages/<int:pk>/unpublish/', template_api_views.unpublish_user_page, name='api_user_page_unpublish'),
    
    # Public page rendering
    path('render/<slug:slug>/', template_api_views.render_user_page, name='api_render_user_page'),
]
