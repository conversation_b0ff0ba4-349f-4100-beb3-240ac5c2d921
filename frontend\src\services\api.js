import axios from 'axios';

// Base API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
const getToken = () => localStorage.getItem('access_token');
const getRefreshToken = () => localStorage.getItem('refresh_token');
const setTokens = (accessToken, refreshToken) => {
  localStorage.setItem('access_token', accessToken);
  localStorage.setItem('refresh_token', refreshToken);
};
const clearTokens = () => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/accounts/token/refresh/`, {
            refresh: refreshToken,
          });
          
          const { access } = response.data;
          setTokens(access, refreshToken);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        clearTokens();
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/accounts/register/', userData),
  login: (credentials) => api.post('/accounts/login/', credentials),
  logout: () => api.post('/accounts/logout/', { refresh: getRefreshToken() }),
  getProfile: () => api.get('/accounts/profile/'),
  updateProfile: (userData) => api.put('/accounts/profile/', userData),
  getUserStats: () => api.get('/accounts/stats/'),
};

// Products API
export const productsAPI = {
  getProducts: () => api.get('/products/'),
  getProduct: (id) => api.get(`/products/${id}/`),
  createProduct: (productData) => api.post('/products/create/', productData),
  updateProduct: (id, productData) => api.put(`/products/${id}/update/`, productData),
  deleteProduct: (id) => api.delete(`/products/${id}/delete/`),
  uploadImages: (productId, images) => {
    const formData = new FormData();
    images.forEach(image => formData.append('images', image));
    return api.post(`/products/${productId}/images/`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  deleteImage: (productId, imageId) => api.delete(`/products/${productId}/images/${imageId}/delete/`),
  searchProducts: (query) => api.get(`/products/search/?q=${query}`),
};

// Leads API
export const leadsAPI = {
  createLead: (leadData) => api.post('/leads/create/', leadData),
  getUserLeads: () => api.get('/leads/my-leads/'),
  getLead: (id) => api.get(`/leads/${id}/`),
  markProcessed: (id) => api.post(`/leads/${id}/mark-processed/`),
  getLeadStats: () => api.get('/leads/stats/'),
  getRecentLeads: (limit = 10) => api.get(`/leads/recent/?limit=${limit}`),
};

// Wallet API
export const walletAPI = {
  getWallet: () => api.get('/wallet/'),
  rechargeWallet: (amount, paymentMethod) => api.post('/wallet/recharge/', { amount, payment_method: paymentMethod }),
  getTransactions: (page = 1, pageSize = 20) => api.get(`/wallet/transactions/?page=${page}&page_size=${pageSize}`),
  getWalletStats: () => api.get('/wallet/stats/'),
};

// Pages API
export const pagesAPI = {
  getPages: () => api.get('/pages/'),
  getPage: (slug) => api.get(`/pages/${slug}/`),
  getPageBySlug: (slug) => api.get(`/pages/by-slug/${slug}/`),
  getUserPage: (username) => api.get(`/pages/user/${username}/`),
  createPage: (pageData) => api.post('/pages/create/', pageData),
  updatePage: (slug, pageData) => api.put(`/pages/${slug}/update/`, pageData),
  deletePage: (slug) => api.delete(`/pages/${slug}/delete/`),
};

// Utility functions
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    return error.response.data.message || error.response.data.error || 'حدث خطأ في الخادم';
  } else if (error.request) {
    // Request was made but no response received
    return 'لا يمكن الاتصال بالخادم';
  } else {
    // Something else happened
    return 'حدث خطأ غير متوقع';
  }
};

export { setTokens, clearTokens, getToken };
export default api;
