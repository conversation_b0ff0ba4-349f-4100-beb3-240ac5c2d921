import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Chip,
  Rating,
  IconButton,
  Badge
} from '@mui/material';
import {
  ShoppingCart,
  Favorite,
  Share,
  LocalShipping,
  Security,
  Support
} from '@mui/icons-material';

const ShoppingTemplate = ({ product }) => {
  const sampleProduct = {
    id: 1,
    name: 'منتج مميز',
    price: 299,
    originalPrice: 399,
    rating: 4.5,
    reviews: 128,
    images: [
      '/api/placeholder/400/400',
      '/api/placeholder/400/400',
      '/api/placeholder/400/400'
    ],
    description: 'وصف تفصيلي للمنتج مع جميع المزايا والخصائص المهمة التي يحتاجها العميل لاتخاذ قرار الشراء.',
    features: [
      'جودة عالية مضمونة',
      'ضمان لمدة سنة كاملة',
      'شحن مجاني لجميع المحافظات',
      'إمكانية الإرجاع خلال 14 يوم'
    ],
    inStock: true,
    discount: 25
  };

  const productData = product || sampleProduct;

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Header */}
      <Box sx={{ bgcolor: 'white', borderBottom: '1px solid', borderColor: 'divider', py: 2 }}>
        <Container maxWidth="lg">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              متجر Lnk2Store
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <IconButton>
                <Badge badgeContent={3} color="primary">
                  <ShoppingCart />
                </Badge>
              </IconButton>
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Product Section */}
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Grid container spacing={4}>
          {/* Product Images */}
          <Grid item xs={12} md={6}>
            <Box sx={{ position: 'sticky', top: 20 }}>
              <Card sx={{ mb: 2 }}>
                <CardMedia
                  component="img"
                  height="400"
                  image={productData.images[0]}
                  alt={productData.name}
                  sx={{ objectFit: 'cover' }}
                />
                {productData.discount > 0 && (
                  <Chip
                    label={`خصم ${productData.discount}%`}
                    color="error"
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      fontWeight: 600
                    }}
                  />
                )}
              </Card>
              
              {/* Thumbnail Images */}
              <Box sx={{ display: 'flex', gap: 1, overflowX: 'auto' }}>
                {productData.images.slice(0, 4).map((image, index) => (
                  <Card key={index} sx={{ minWidth: 80, cursor: 'pointer' }}>
                    <CardMedia
                      component="img"
                      height="80"
                      image={image}
                      alt={`${productData.name} ${index + 1}`}
                    />
                  </Card>
                ))}
              </Box>
            </Box>
          </Grid>

          {/* Product Details */}
          <Grid item xs={12} md={6}>
            <Box sx={{ position: 'sticky', top: 20 }}>
              <Typography variant="h4" gutterBottom sx={{ fontWeight: 700 }}>
                {productData.name}
              </Typography>

              {/* Rating */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Rating value={productData.rating} readOnly precision={0.5} />
                <Typography variant="body2" color="text.secondary">
                  ({productData.reviews} تقييم)
                </Typography>
              </Box>

              {/* Price */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="h4" color="primary" sx={{ fontWeight: 700 }}>
                    {productData.price} ر.س
                  </Typography>
                  {productData.originalPrice > productData.price && (
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        textDecoration: 'line-through',
                        color: 'text.secondary'
                      }}
                    >
                      {productData.originalPrice} ر.س
                    </Typography>
                  )}
                </Box>
                <Typography variant="body2" color="success.main" sx={{ fontWeight: 600 }}>
                  وفر {productData.originalPrice - productData.price} ر.س
                </Typography>
              </Box>

              {/* Stock Status */}
              <Box sx={{ mb: 3 }}>
                <Chip
                  label={productData.inStock ? "متوفر في المخزن" : "غير متوفر"}
                  color={productData.inStock ? "success" : "error"}
                  variant="outlined"
                />
              </Box>

              {/* Description */}
              <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.8 }}>
                {productData.description}
              </Typography>

              {/* Features */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  المزايا الرئيسية:
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {productData.features.map((feature, index) => (
                    <Typography key={index} variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box component="span" sx={{ color: 'success.main', mr: 1 }}>✓</Box>
                      {feature}
                    </Typography>
                  ))}
                </Box>
              </Box>

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2, mb: 4 }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<ShoppingCart />}
                  sx={{ 
                    flex: 1,
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600
                  }}
                  disabled={!productData.inStock}
                >
                  أضف إلى السلة
                </Button>
                <IconButton size="large" color="primary">
                  <Favorite />
                </IconButton>
                <IconButton size="large" color="primary">
                  <Share />
                </IconButton>
              </Box>

              {/* Trust Badges */}
              <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', pt: 3, borderTop: '1px solid', borderColor: 'divider' }}>
                <Box sx={{ textAlign: 'center' }}>
                  <LocalShipping color="primary" sx={{ fontSize: '2rem', mb: 1 }} />
                  <Typography variant="caption" display="block">شحن مجاني</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Security color="primary" sx={{ fontSize: '2rem', mb: 1 }} />
                  <Typography variant="caption" display="block">دفع آمن</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Support color="primary" sx={{ fontSize: '2rem', mb: 1 }} />
                  <Typography variant="caption" display="block">دعم 24/7</Typography>
                </Box>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>

      {/* Order Form Section */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 6 }}>
        <Container maxWidth="md">
          <Typography variant="h4" textAlign="center" gutterBottom sx={{ fontWeight: 700 }}>
            اطلب الآن واحصل على خصم خاص!
          </Typography>
          <Typography variant="h6" textAlign="center" sx={{ mb: 4, opacity: 0.9 }}>
            املأ النموذج أدناه وسنتواصل معك خلال دقائق
          </Typography>
          
          <Card sx={{ p: 4, maxWidth: 500, mx: 'auto' }}>
            <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <Box>
                <Typography variant="body2" gutterBottom sx={{ fontWeight: 600 }}>
                  الاسم الكامل *
                </Typography>
                <Box
                  sx={{
                    border: '2px solid',
                    borderColor: 'grey.300',
                    borderRadius: 1,
                    p: 1.5,
                    bgcolor: 'grey.50'
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    أدخل اسمك الكامل
                  </Typography>
                </Box>
              </Box>
              
              <Box>
                <Typography variant="body2" gutterBottom sx={{ fontWeight: 600 }}>
                  رقم الهاتف *
                </Typography>
                <Box
                  sx={{
                    border: '2px solid',
                    borderColor: 'grey.300',
                    borderRadius: 1,
                    p: 1.5,
                    bgcolor: 'grey.50'
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    أدخل رقم هاتفك
                  </Typography>
                </Box>
              </Box>
              
              <Box>
                <Typography variant="body2" gutterBottom sx={{ fontWeight: 600 }}>
                  العنوان
                </Typography>
                <Box
                  sx={{
                    border: '2px solid',
                    borderColor: 'grey.300',
                    borderRadius: 1,
                    p: 1.5,
                    bgcolor: 'grey.50',
                    minHeight: 80
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    أدخل عنوانك التفصيلي
                  </Typography>
                </Box>
              </Box>
              
              <Button
                variant="contained"
                size="large"
                sx={{
                  bgcolor: 'success.main',
                  py: 2,
                  fontSize: '1.2rem',
                  fontWeight: 700,
                  '&:hover': {
                    bgcolor: 'success.dark'
                  }
                }}
              >
                اطلب الآن - {productData.price} ر.س
              </Button>
              
              <Typography variant="caption" textAlign="center" color="text.secondary">
                * الحقول المطلوبة
              </Typography>
            </Box>
          </Card>
        </Container>
      </Box>
    </Box>
  );
};

export default ShoppingTemplate;
