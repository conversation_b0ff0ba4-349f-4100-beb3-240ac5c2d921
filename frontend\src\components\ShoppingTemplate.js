import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardMedia,
  Chip,
  Rating,
  IconButton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  ShoppingCart,
  Favorite,
  Share,
  LocalShipping,
  Security,
  Support,
  WhatsApp,
  CheckCircle,
  Star,
  Close
} from '@mui/icons-material';

const ShoppingTemplate = ({ product }) => {
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    address: '',
    notes: ''
  });
  const [orderSuccess, setOrderSuccess] = useState(false);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const sampleProduct = {
    id: 1,
    name: 'هاتف ذكي متطور',
    price: 2500,
    originalPrice: 3000,
    rating: 4.8,
    reviews: 256,
    images: ['/api/placeholder/600/600'],
    description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة، مثالي للاستخدام اليومي والتصوير الاحترافي.',
    features: [
      'شاشة AMOLED عالية الدقة 6.7 بوصة',
      'كاميرا ثلاثية 108 ميجابكسل',
      'بطارية 5000 مللي أمبير',
      'ذاكرة تخزين 256 جيجابايت'
    ],
    inStock: true,
    discount: 17,
    warranty: 'ضمان سنتين',
    shipping: 'شحن مجاني'
  };

  const productData = product || sampleProduct;
  const discountAmount = productData.originalPrice - productData.price;

  const handleOrderSubmit = () => {
    if (!customerInfo.name || !customerInfo.phone) return;

    const whatsappMessage = `
🛍️ طلب جديد من lnk2store
📱 المنتج: ${productData.name}
💰 السعر: ${formatCurrency(productData.price)}
👤 العميل: ${customerInfo.name}
📞 الهاتف: ${customerInfo.phone}
📍 العنوان: ${customerInfo.address}
📝 ملاحظات: ${customerInfo.notes}
    `.trim();

    const whatsappUrl = `https://wa.me/201234567890?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');

    setOrderDialogOpen(false);
    setOrderSuccess(true);
    setCustomerInfo({ name: '', phone: '', address: '', notes: '' });
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50' }}>
      <Box sx={{ bgcolor: 'white', py: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Container>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              🛍️ lnk2store
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton color="primary">
                <Share />
              </IconButton>
              <IconButton color="primary">
                <Favorite />
              </IconButton>
            </Box>
          </Box>
        </Container>
      </Box>

      <Container sx={{ py: 4 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Card sx={{ position: 'relative' }}>
              {productData.discount > 0 && (
                <Chip
                  label={`خصم ${productData.discount}%`}
                  color="error"
                  sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1, fontWeight: 'bold' }}
                />
              )}
              <CardMedia
                component="img"
                height="500"
                image={productData.images[0]}
                alt={productData.name}
                sx={{ objectFit: 'cover' }}
              />
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                {productData.name}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Rating value={productData.rating} readOnly precision={0.1} />
                <Typography variant="body2" color="text.secondary">
                  ({productData.reviews} تقييم)
                </Typography>
                <Chip label="الأكثر مبيعاً" color="success" size="small" />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                  <Typography variant="h3" color="primary" sx={{ fontWeight: 'bold' }}>
                    {formatCurrency(productData.price)}
                  </Typography>
                  {productData.originalPrice > productData.price && (
                    <Typography variant="h5" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
                      {formatCurrency(productData.originalPrice)}
                    </Typography>
                  )}
                </Box>
                {discountAmount > 0 && (
                  <Typography variant="body1" color="success.main" sx={{ fontWeight: 'bold' }}>
                    🎉 توفر {formatCurrency(discountAmount)}!
                  </Typography>
                )}
              </Box>

              <Typography variant="body1" paragraph sx={{ lineHeight: 1.8 }}>
                {productData.description}
              </Typography>

              <Card sx={{ mb: 3, p: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Star color="primary" />
                  المزايا الرئيسية
                </Typography>
                <List dense>
                  {productData.features.map((feature, index) => (
                    <ListItem key={index} sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <CheckCircle color="success" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={feature} />
                    </ListItem>
                  ))}
                </List>
              </Card>

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 2 }}>
                    <LocalShipping color="primary" sx={{ mb: 1 }} />
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {productData.shipping}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 2 }}>
                    <Security color="primary" sx={{ mb: 1 }} />
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {productData.warranty}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.paper', borderRadius: 2 }}>
                    <Support color="primary" sx={{ mb: 1 }} />
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      دعم 24/7
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Button
                  variant="contained"
                  size="large"
                  fullWidth
                  startIcon={<ShoppingCart />}
                  onClick={() => setOrderDialogOpen(true)}
                  sx={{ py: 2, fontSize: '1.1rem', fontWeight: 'bold' }}
                >
                  اطلب الآن
                </Button>
                <Button
                  variant="contained"
                  size="large"
                  color="success"
                  startIcon={<WhatsApp />}
                  onClick={() => setOrderDialogOpen(true)}
                  sx={{ py: 2, minWidth: 120 }}
                >
                  واتساب
                </Button>
              </Box>

              <Card sx={{ p: 2, bgcolor: 'primary.main', color: 'white' }}>
                <Typography variant="h6" gutterBottom>
                  📞 للطلب والاستفسار
                </Typography>
                <Typography variant="body1">
                  اتصل بنا: 01234567890
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  متاح 24 ساعة لخدمتك
                </Typography>
              </Card>
            </Box>
          </Grid>
        </Grid>
      </Container>

      <Dialog open={orderDialogOpen} onClose={() => setOrderDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">🛍️ إتمام الطلب</Typography>
          <IconButton onClick={() => setOrderDialogOpen(false)}>
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              المنتج: {productData.name}
            </Typography>
            <Typography variant="h6" color="primary">
              السعر: {formatCurrency(productData.price)}
            </Typography>
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="الاسم الكامل *"
                value={customerInfo.name}
                onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="رقم الهاتف *"
                value={customerInfo.phone}
                onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="العنوان"
                value={customerInfo.address}
                onChange={(e) => setCustomerInfo({...customerInfo, address: e.target.value})}
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="ملاحظات إضافية"
                value={customerInfo.notes}
                onChange={(e) => setCustomerInfo({...customerInfo, notes: e.target.value})}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setOrderDialogOpen(false)}>إلغاء</Button>
          <Button 
            variant="contained" 
            onClick={handleOrderSubmit}
            disabled={!customerInfo.name || !customerInfo.phone}
            startIcon={<WhatsApp />}
          >
            إرسال الطلب عبر واتساب
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar open={orderSuccess} autoHideDuration={6000} onClose={() => setOrderSuccess(false)}>
        <Alert onClose={() => setOrderSuccess(false)} severity="success">
          تم إرسال طلبك بنجاح! سيتم التواصل معك قريباً.
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ShoppingTemplate;
