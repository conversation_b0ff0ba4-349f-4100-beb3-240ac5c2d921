# 🚀 lnk2store Deployment Guide for Hostinger KVM4

This guide will walk you through deploying lnk2store to your Hostinger KVM4 server with the domain lnk2store.com.

## 📋 Prerequisites

- Hostinger KVM4 server with Ubuntu 20.04+ or similar
- Domain lnk2store.com pointing to your server IP
- SSH access to your server
- Basic knowledge of Linux commands

## 🔧 Step 1: Server Preparation

### 1.1 Connect to Your Server
```bash
ssh root@your-server-ip
```

### 1.2 Update System
```bash
apt update && apt upgrade -y
```

### 1.3 Create Deployment User
```bash
adduser deploy
usermod -aG sudo deploy
su - deploy
```

## 📦 Step 2: Upload Project Files

### 2.1 Clone or Upload Project
```bash
# Option 1: If using Git
git clone https://github.com/yourusername/lnk2store.git /var/www/lnk2store

# Option 2: Upload via SCP (from your local machine)
scp -r lnk2store/ deploy@your-server-ip:/var/www/
```

### 2.2 Set Permissions
```bash
sudo chown -R deploy:www-data /var/www/lnk2store
sudo chmod -R 755 /var/www/lnk2store
```

## ⚙️ Step 3: Configure Environment

### 3.1 Copy Environment File
```bash
cd /var/www/lnk2store
cp .env.production .env
```

### 3.2 Edit Environment Variables
```bash
nano .env
```

Update these critical values:
```env
SECRET_KEY=your-super-secret-key-here
DB_PASSWORD=your-secure-database-password
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
SERVER_IP=your-server-ip
```

## 🚀 Step 4: Run Deployment Script

### 4.1 Make Script Executable
```bash
chmod +x deploy.sh
```

### 4.2 Run Deployment
```bash
sudo ./deploy.sh production
```

This script will:
- Install all required system packages
- Set up PostgreSQL database
- Configure Redis
- Install Python dependencies
- Run Django migrations
- Configure Nginx
- Set up Supervisor for process management

## 🔒 Step 5: SSL Certificate Setup

### 5.1 Install SSL Certificate
```bash
sudo certbot --nginx -d lnk2store.com -d www.lnk2store.com
```

### 5.2 Test Auto-renewal
```bash
sudo certbot renew --dry-run
```

## 🌐 Step 6: DNS Configuration

Point your domain to your server:
- A record: lnk2store.com → your-server-ip
- A record: www.lnk2store.com → your-server-ip

## 🔧 Step 7: Frontend Deployment

### 7.1 Build React Frontend
```bash
cd /var/www/lnk2store/frontend
chmod +x build-production.sh
./build-production.sh
```

### 7.2 Configure Nginx for Frontend
The deployment script already configures Nginx to serve the React build files.

## ✅ Step 8: Verification

### 8.1 Check Services
```bash
sudo supervisorctl status
sudo systemctl status nginx
sudo systemctl status postgresql
sudo systemctl status redis-server
```

### 8.2 Test Application
- Visit https://lnk2store.com
- Check admin panel: https://lnk2store.com/admin
- Test API: https://lnk2store.com/api/v1/

## 🔧 Step 9: Post-Deployment Configuration

### 9.1 Create Admin User
```bash
cd /var/www/lnk2store
source venv/bin/activate
python manage.py createsuperuser
```

### 9.2 Load Initial Data (Optional)
```bash
python manage.py loaddata fixtures/initial_data.json
```

## 📊 Monitoring and Maintenance

### Log Files Locations
- Django: `/var/log/lnk2store/django.log`
- Gunicorn: `/var/log/lnk2store/gunicorn.log`
- Celery: `/var/log/lnk2store/celery.log`
- Nginx: `/var/log/nginx/`

### Useful Commands
```bash
# Restart application
sudo supervisorctl restart lnk2store

# Restart Nginx
sudo systemctl restart nginx

# View logs
tail -f /var/log/lnk2store/django.log

# Update application
cd /var/www/lnk2store
git pull
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py collectstatic --noinput
sudo supervisorctl restart lnk2store
```

## 🚨 Troubleshooting

### Common Issues

1. **502 Bad Gateway**
   - Check if Gunicorn is running: `sudo supervisorctl status lnk2store`
   - Check logs: `tail -f /var/log/lnk2store/gunicorn.log`

2. **Database Connection Error**
   - Verify PostgreSQL is running: `sudo systemctl status postgresql`
   - Check database credentials in `.env`

3. **Static Files Not Loading**
   - Run: `python manage.py collectstatic --noinput`
   - Check Nginx configuration

4. **SSL Certificate Issues**
   - Renew certificate: `sudo certbot renew`
   - Check certificate status: `sudo certbot certificates`

## 🔄 Updates and Maintenance

### Regular Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Python packages
cd /var/www/lnk2store
source venv/bin/activate
pip install -r requirements.txt --upgrade

# Restart services
sudo supervisorctl restart lnk2store
```

### Backup Strategy
```bash
# Database backup
sudo -u postgres pg_dump lnk2store_prod > backup_$(date +%Y%m%d).sql

# Files backup
tar -czf lnk2store_backup_$(date +%Y%m%d).tar.gz /var/www/lnk2store
```

## 📞 Support

If you encounter issues:
1. Check the logs in `/var/log/lnk2store/`
2. Verify all services are running
3. Check the troubleshooting section above
4. Contact support with specific error messages

---

🎉 **Congratulations!** Your lnk2store platform should now be live at https://lnk2store.com
