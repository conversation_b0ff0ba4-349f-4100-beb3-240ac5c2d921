from django.db import models
from django.utils import timezone
from django.db.models import Sum, Count, Avg
from datetime import timedelta
from decimal import Decimal

class SystemStats(models.Model):
    """Model to cache system statistics"""
    date = models.DateField(unique=True)
    total_users = models.IntegerField(default=0)
    active_users = models.IntegerField(default=0)
    total_leads = models.IntegerField(default=0)
    total_products = models.IntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"Stats for {self.date}"

class AdminNotification(models.Model):
    """Notifications for admin panel"""
    NOTIFICATION_TYPES = [
        ('low_balance', 'Low Balance Alert'),
        ('new_user', 'New User Registration'),
        ('high_activity', 'High Activity Alert'),
        ('system_error', 'System Error'),
        ('payment_issue', 'Payment Issue'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    title = models.CharField(max_length=200)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} ({self.get_priority_display()})"

class DashboardManager:
    """Manager class for dashboard statistics"""

    @staticmethod
    def get_overview_stats():
        """Get overview statistics for admin dashboard"""
        from accounts.models import User
        from leads.models import Lead
        from products.models import Product
        from wallet.models import Wallet, Transaction

        now = timezone.now()
        today = now.date()
        week_ago = now - timedelta(days=7)
        month_ago = now - timedelta(days=30)

        # User statistics
        total_users = User.objects.count()
        new_users_today = User.objects.filter(date_joined__date=today).count()
        new_users_week = User.objects.filter(date_joined__gte=week_ago).count()

        # Lead statistics
        total_leads = Lead.objects.count()
        leads_today = Lead.objects.filter(created_at__date=today).count()
        leads_week = Lead.objects.filter(created_at__gte=week_ago).count()
        pending_leads = Lead.objects.filter(deducted=False).count()

        # Product statistics
        total_products = Product.objects.count()

        # Revenue statistics
        total_revenue = Transaction.objects.filter(
            amount__lt=0  # Negative amounts are deductions (revenue)
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        total_revenue = abs(total_revenue)

        revenue_today = abs(Transaction.objects.filter(
            timestamp__date=today,
            amount__lt=0
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0'))

        revenue_week = abs(Transaction.objects.filter(
            timestamp__gte=week_ago,
            amount__lt=0
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0'))

        # Wallet statistics
        total_wallet_balance = Wallet.objects.aggregate(
            total=Sum('balance')
        )['total'] or Decimal('0')

        low_balance_users = Wallet.objects.filter(
            balance__lt=models.F('low_balance_threshold')
        ).count()

        return {
            'users': {
                'total': total_users,
                'new_today': new_users_today,
                'new_week': new_users_week,
                'low_balance_count': low_balance_users,
            },
            'leads': {
                'total': total_leads,
                'today': leads_today,
                'week': leads_week,
                'pending': pending_leads,
            },
            'products': {
                'total': total_products,
            },
            'revenue': {
                'total': float(total_revenue),
                'today': float(revenue_today),
                'week': float(revenue_week),
            },
            'wallets': {
                'total_balance': float(total_wallet_balance),
                'low_balance_users': low_balance_users,
            }
        }

    @staticmethod
    def get_chart_data(days=30):
        """Get data for charts"""
        from leads.models import Lead
        from accounts.models import User
        from wallet.models import Transaction

        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)

        # Daily leads data
        leads_data = []
        users_data = []
        revenue_data = []

        current_date = start_date
        while current_date <= end_date:
            # Leads count for this date
            leads_count = Lead.objects.filter(
                created_at__date=current_date
            ).count()

            # New users count for this date
            users_count = User.objects.filter(
                date_joined__date=current_date
            ).count()

            # Revenue for this date
            revenue = abs(Transaction.objects.filter(
                timestamp__date=current_date,
                amount__lt=0
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0'))

            leads_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': leads_count
            })

            users_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': users_count
            })

            revenue_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'amount': float(revenue)
            })

            current_date += timedelta(days=1)

        return {
            'leads': leads_data,
            'users': users_data,
            'revenue': revenue_data
        }