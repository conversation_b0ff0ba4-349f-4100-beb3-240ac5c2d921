{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, Button, Box, Container } from '@mui/material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport Logo from './Logo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    await logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"static\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            style: {\n              textDecoration: 'none',\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo.png\",\n              alt: \"Lnk2Store\",\n              style: {\n                height: '40px',\n                width: 'auto',\n                filter: 'brightness(0) invert(1)' // Makes logo white for dark AppBar\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/dashboard\",\n            children: \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/products\",\n            children: \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/my-pages\",\n            children: \"\\u0635\\u0641\\u062D\\u0627\\u062A\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/templates\",\n            children: \"\\u0627\\u0644\\u0642\\u0648\\u0627\\u0644\\u0628\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/leads\",\n            children: \"\\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/wallet\",\n            children: \"\\u0627\\u0644\\u0645\\u062D\\u0641\\u0638\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this), ((user === null || user === void 0 ? void 0 : user.is_staff) || (user === null || user === void 0 ? void 0 : user.is_superuser)) && /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/admin\",\n            children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            children: [\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C (\", user === null || user === void 0 ? void 0 : user.username, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/login\",\n            children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/register\",\n            children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        mt: 4,\n        mb: 4\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"B5aHiu91piX0w1CsOFDPXF/GbhU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "Container", "Link", "useNavigate", "useAuth", "Logo", "jsxDEV", "_jsxDEV", "Layout", "children", "_s", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "sx", "flexGrow", "position", "to", "style", "textDecoration", "display", "alignItems", "src", "alt", "height", "width", "filter", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "color", "component", "is_staff", "is_superuser", "onClick", "username", "max<PERSON><PERSON><PERSON>", "mt", "mb", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/Layout.js"], "sourcesContent": ["import React from 'react';\nimport { AppB<PERSON>, Too<PERSON><PERSON>, Typo<PERSON>, Button, Box, Container } from '@mui/material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport Logo from './Logo';\n\nconst Layout = ({ children }) => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/');\n  };\n\n  return (\n    <Box sx={{ flexGrow: 1 }}>\n      <AppBar position=\"static\">\n        <Toolbar>\n          <Box sx={{ flexGrow: 1 }}>\n            <Link to=\"/\" style={{ textDecoration: 'none', display: 'flex', alignItems: 'center' }}>\n              <img\n                src=\"/logo.png\"\n                alt=\"Lnk2Store\"\n                style={{\n                  height: '40px',\n                  width: 'auto',\n                  filter: 'brightness(0) invert(1)' // Makes logo white for dark AppBar\n                }}\n              />\n            </Link>\n          </Box>\n          \n          {isAuthenticated ? (\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button color=\"inherit\" component={Link} to=\"/dashboard\">\n                لوحة التحكم\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/products\">\n                المنتجات\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/my-pages\">\n                صفحاتي\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/templates\">\n                القوالب\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/leads\">\n                الطلبات\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/wallet\">\n                المحفظة\n              </Button>\n              {(user?.is_staff || user?.is_superuser) && (\n                <Button color=\"inherit\" component={Link} to=\"/admin\">\n                  إدارة النظام\n                </Button>\n              )}\n              <Button color=\"inherit\" onClick={handleLogout}>\n                تسجيل الخروج ({user?.username})\n              </Button>\n            </Box>\n          ) : (\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button color=\"inherit\" component={Link} to=\"/login\">\n                تسجيل الدخول\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/register\">\n                إنشاء حساب\n              </Button>\n            </Box>\n          )}\n        </Toolbar>\n      </AppBar>\n      \n      <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n        {children}\n      </Container>\n    </Box>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,eAAe;AACnF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,IAAI,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnD,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMF,MAAM,CAAC,CAAC;IACdC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEP,OAAA,CAACP,GAAG;IAACgB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE;IAAAR,QAAA,gBACvBF,OAAA,CAACX,MAAM;MAACsB,QAAQ,EAAC,QAAQ;MAAAT,QAAA,eACvBF,OAAA,CAACV,OAAO;QAAAY,QAAA,gBACNF,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAR,QAAA,eACvBF,OAAA,CAACL,IAAI;YAACiB,EAAE,EAAC,GAAG;YAACC,KAAK,EAAE;cAAEC,cAAc,EAAE,MAAM;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAd,QAAA,eACpFF,OAAA;cACEiB,GAAG,EAAC,WAAW;cACfC,GAAG,EAAC,WAAW;cACfL,KAAK,EAAE;gBACLM,MAAM,EAAE,MAAM;gBACdC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,yBAAyB,CAAC;cACpC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELpB,eAAe,gBACdL,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEW,GAAG,EAAE;UAAE,CAAE;UAAAxB,QAAA,gBACnCF,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,YAAY;YAAAV,QAAA,EAAC;UAEzD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzB,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,WAAW;YAAAV,QAAA,EAAC;UAExD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzB,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,WAAW;YAAAV,QAAA,EAAC;UAExD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzB,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,YAAY;YAAAV,QAAA,EAAC;UAEzD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzB,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,QAAQ;YAAAV,QAAA,EAAC;UAErD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzB,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,SAAS;YAAAV,QAAA,EAAC;UAEtD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACR,CAAC,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,QAAQ,MAAIzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,YAAY,mBACpC9B,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,QAAQ;YAAAV,QAAA,EAAC;UAErD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDzB,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACI,OAAO,EAAEvB,YAAa;YAAAN,QAAA,GAAC,uEAC/B,EAACE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,QAAQ,EAAC,GAChC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENzB,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEW,GAAG,EAAE;UAAE,CAAE;UAAAxB,QAAA,gBACnCF,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,QAAQ;YAAAV,QAAA,EAAC;UAErD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzB,OAAA,CAACR,MAAM;YAACmC,KAAK,EAAC,SAAS;YAACC,SAAS,EAAEjC,IAAK;YAACiB,EAAE,EAAC,WAAW;YAAAV,QAAA,EAAC;UAExD;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAETzB,OAAA,CAACN,SAAS;MAACuC,QAAQ,EAAC,IAAI;MAACxB,EAAE,EAAE;QAAEyB,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAjC,QAAA,EAC3CA;IAAQ;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACtB,EAAA,CA1EIF,MAAM;EAAA,QACgCJ,OAAO,EAChCD,WAAW;AAAA;AAAAwC,EAAA,GAFxBnC,MAAM;AA4EZ,eAAeA,MAAM;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}