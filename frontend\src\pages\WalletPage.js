import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import { AccountBalanceWallet, Add, TrendingUp, TrendingDown } from '@mui/icons-material';
import { useApi, useAsyncOperation } from '../hooks/useApi';
import { walletAPI } from '../services/api';

const WalletPage = () => {
  const [rechargeOpen, setRechargeOpen] = useState(false);
  const [rechargeAmount, setRechargeAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('credit_card');

  const { data: wallet, loading: walletLoading, refetch: refetchWallet } = useApi(() => walletAPI.getWallet());
  const { data: stats, loading: statsLoading } = useApi(() => walletAPI.getWalletStats());
  const { data: transactions, loading: transactionsLoading } = useApi(() => walletAPI.getTransactions());
  const { loading: rechargeLoading, error: rechargeError, execute: executeRecharge } = useAsyncOperation();

  const handleRecharge = async () => {
    const result = await executeRecharge(() => 
      walletAPI.rechargeWallet(parseFloat(rechargeAmount), paymentMethod)
    );

    if (result.success) {
      setRechargeOpen(false);
      setRechargeAmount('');
      refetchWallet();
      // Show success message
    }
  };

  const formatCurrency = (amount) => `${amount} ريال`;
  const formatDate = (dateString) => new Date(dateString).toLocaleDateString('ar-SA');

  if (walletLoading || statsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        المحفظة
      </Typography>

      {/* Wallet Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AccountBalanceWallet sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" color="primary">
                {formatCurrency(wallet?.balance || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                الرصيد الحالي
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h5">
                {formatCurrency(stats?.total_recharged || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الشحن
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingDown sx={{ fontSize: 40, color: 'error.main', mb: 1 }} />
              <Typography variant="h5">
                {formatCurrency(stats?.total_spent || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الإنفاق
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h5">
                {formatCurrency(stats?.average_lead_cost || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                متوسط تكلفة الطلب
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Actions */}
      <Box sx={{ mb: 4 }}>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setRechargeOpen(true)}
          size="large"
        >
          شحن المحفظة
        </Button>
      </Box>

      {/* Low Balance Warning */}
      {wallet?.balance < 50 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          رصيدك منخفض! يُنصح بشحن المحفظة لضمان عدم فقدان الطلبات.
        </Alert>
      )}

      {/* Transaction History */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            سجل المعاملات
          </Typography>
          
          {transactionsLoading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>التاريخ</TableCell>
                    <TableCell>الوصف</TableCell>
                    <TableCell align="right">المبلغ</TableCell>
                    <TableCell>النوع</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions?.transactions?.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{formatDate(transaction.timestamp)}</TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell align="right">
                        {formatCurrency(Math.abs(transaction.amount))}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.amount > 0 ? 'شحن' : 'خصم'}
                          color={transaction.amount > 0 ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Recharge Dialog */}
      <Dialog open={rechargeOpen} onClose={() => setRechargeOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>شحن المحفظة</DialogTitle>
        <DialogContent>
          {rechargeError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {rechargeError}
            </Alert>
          )}
          
          <TextField
            fullWidth
            label="المبلغ (ريال)"
            type="number"
            value={rechargeAmount}
            onChange={(e) => setRechargeAmount(e.target.value)}
            margin="normal"
            inputProps={{ min: 10, max: 10000 }}
          />
          
          <TextField
            fullWidth
            select
            label="طريقة الدفع"
            value={paymentMethod}
            onChange={(e) => setPaymentMethod(e.target.value)}
            margin="normal"
          >
            <MenuItem value="credit_card">بطاقة ائتمان</MenuItem>
            <MenuItem value="bank_transfer">تحويل بنكي</MenuItem>
            <MenuItem value="paypal">PayPal</MenuItem>
          </TextField>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRechargeOpen(false)}>إلغاء</Button>
          <Button
            onClick={handleRecharge}
            variant="contained"
            disabled={rechargeLoading || !rechargeAmount || parseFloat(rechargeAmount) < 10}
          >
            {rechargeLoading ? <CircularProgress size={20} /> : 'شحن'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WalletPage;
