{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\Logo.js\";\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Logo = ({\n  height = '40px',\n  width = 'auto',\n  variant = 'light',\n  // 'light' or 'dark'\n  linkTo = '/',\n  forHeader = false,\n  // Special handling for header\n  sx = {},\n  ...props\n}) => {\n  // Handle responsive height\n  const logoSx = {\n    height: typeof height === 'object' ? height : height,\n    width,\n    objectFit: 'contain',\n    // Apply filter only for header on dark background\n    filter: forHeader ? 'brightness(0) invert(1)' : 'none',\n    ...sx\n  };\n  const LogoImage = () => /*#__PURE__*/_jsxDEV(\"img\", {\n    src: \"/logo.png\",\n    alt: \"Lnk2Store\",\n    style: logoStyle,\n    onError: e => {\n      // Fallback to text if image fails to load\n      e.target.style.display = 'none';\n      e.target.nextSibling.style.display = 'block';\n    },\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n  const FallbackText = () => /*#__PURE__*/_jsxDEV(Box, {\n    component: \"span\",\n    sx: {\n      display: 'none',\n      fontSize: '1.5rem',\n      fontWeight: 'bold',\n      color: variant === 'light' ? 'white' : 'primary.main',\n      fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n    },\n    children: \"Lnk2Store\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n  if (linkTo) {\n    return /*#__PURE__*/_jsxDEV(Link, {\n      to: linkTo,\n      style: {\n        textDecoration: 'none',\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(LogoImage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FallbackText, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(LogoImage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FallbackText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_c = Logo;\nexport default Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");", "map": {"version": 3, "names": ["React", "Box", "Link", "jsxDEV", "_jsxDEV", "Logo", "height", "width", "variant", "linkTo", "for<PERSON><PERSON><PERSON>", "sx", "props", "logoSx", "objectFit", "filter", "LogoImage", "src", "alt", "style", "logoStyle", "onError", "e", "target", "display", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FallbackText", "component", "fontSize", "fontWeight", "color", "fontFamily", "children", "to", "textDecoration", "alignItems", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/Logo.js"], "sourcesContent": ["import React from 'react';\nimport { Box } from '@mui/material';\nimport { Link } from 'react-router-dom';\n\nconst Logo = ({\n  height = '40px',\n  width = 'auto',\n  variant = 'light', // 'light' or 'dark'\n  linkTo = '/',\n  forHeader = false, // Special handling for header\n  sx = {},\n  ...props\n}) => {\n  // Handle responsive height\n  const logoSx = {\n    height: typeof height === 'object' ? height : height,\n    width,\n    objectFit: 'contain',\n    // Apply filter only for header on dark background\n    filter: forHeader ? 'brightness(0) invert(1)' : 'none',\n    ...sx\n  };\n\n  const LogoImage = () => (\n    <img \n      src=\"/logo.png\" \n      alt=\"Lnk2Store\" \n      style={logoStyle}\n      onError={(e) => {\n        // Fallback to text if image fails to load\n        e.target.style.display = 'none';\n        e.target.nextSibling.style.display = 'block';\n      }}\n      {...props}\n    />\n  );\n\n  const FallbackText = () => (\n    <Box\n      component=\"span\"\n      sx={{\n        display: 'none',\n        fontSize: '1.5rem',\n        fontWeight: 'bold',\n        color: variant === 'light' ? 'white' : 'primary.main',\n        fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n      }}\n    >\n      Lnk2Store\n    </Box>\n  );\n\n  if (linkTo) {\n    return (\n      <Link \n        to={linkTo} \n        style={{ \n          textDecoration: 'none', \n          display: 'flex', \n          alignItems: 'center' \n        }}\n      >\n        <LogoImage />\n        <FallbackText />\n      </Link>\n    );\n  }\n\n  return (\n    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n      <LogoImage />\n      <FallbackText />\n    </Box>\n  );\n};\n\nexport default Logo;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAQ,eAAe;AACnC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,IAAI,GAAGA,CAAC;EACZC,MAAM,GAAG,MAAM;EACfC,KAAK,GAAG,MAAM;EACdC,OAAO,GAAG,OAAO;EAAE;EACnBC,MAAM,GAAG,GAAG;EACZC,SAAS,GAAG,KAAK;EAAE;EACnBC,EAAE,GAAG,CAAC,CAAC;EACP,GAAGC;AACL,CAAC,KAAK;EACJ;EACA,MAAMC,MAAM,GAAG;IACbP,MAAM,EAAE,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGA,MAAM;IACpDC,KAAK;IACLO,SAAS,EAAE,SAAS;IACpB;IACAC,MAAM,EAAEL,SAAS,GAAG,yBAAyB,GAAG,MAAM;IACtD,GAAGC;EACL,CAAC;EAED,MAAMK,SAAS,GAAGA,CAAA,kBAChBZ,OAAA;IACEa,GAAG,EAAC,WAAW;IACfC,GAAG,EAAC,WAAW;IACfC,KAAK,EAAEC,SAAU;IACjBC,OAAO,EAAGC,CAAC,IAAK;MACd;MACAA,CAAC,CAACC,MAAM,CAACJ,KAAK,CAACK,OAAO,GAAG,MAAM;MAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACN,KAAK,CAACK,OAAO,GAAG,OAAO;IAC9C,CAAE;IAAA,GACEZ;EAAK;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACF;EAED,MAAMC,YAAY,GAAGA,CAAA,kBACnB1B,OAAA,CAACH,GAAG;IACF8B,SAAS,EAAC,MAAM;IAChBpB,EAAE,EAAE;MACFa,OAAO,EAAE,MAAM;MACfQ,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE1B,OAAO,KAAK,OAAO,GAAG,OAAO,GAAG,cAAc;MACrD2B,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,EACH;EAED;IAAAV,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CACN;EAED,IAAIpB,MAAM,EAAE;IACV,oBACEL,OAAA,CAACF,IAAI;MACHmC,EAAE,EAAE5B,MAAO;MACXU,KAAK,EAAE;QACLmB,cAAc,EAAE,MAAM;QACtBd,OAAO,EAAE,MAAM;QACfe,UAAU,EAAE;MACd,CAAE;MAAAH,QAAA,gBAEFhC,OAAA,CAACY,SAAS;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACbzB,OAAA,CAAC0B,YAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEX;EAEA,oBACEzB,OAAA,CAACH,GAAG;IAACU,EAAE,EAAE;MAAEa,OAAO,EAAE,MAAM;MAAEe,UAAU,EAAE;IAAS,CAAE;IAAAH,QAAA,gBACjDhC,OAAA,CAACY,SAAS;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbzB,OAAA,CAAC0B,YAAY;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAACW,EAAA,GAtEInC,IAAI;AAwEV,eAAeA,IAAI;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}