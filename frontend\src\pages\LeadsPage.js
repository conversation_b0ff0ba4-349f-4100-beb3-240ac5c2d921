import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  TextField,
  MenuItem,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Visibility,
  Phone,
  Email,
  CheckCircle,
  FilterList
} from '@mui/icons-material';
import { useApi, useAsyncOperation } from '../hooks/useApi';
import { leadsAPI } from '../services/api';

const LeadsPage = () => {
  const [selectedLead, setSelectedLead] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [filter, setFilter] = useState('all');

  const { data: leads, loading, refetch } = useApi(() => leadsAPI.getUserLeads());
  const { data: stats } = useApi(() => leadsAPI.getLeadStats());
  const { loading: processingLead, execute: executeMarkProcessed } = useAsyncOperation();

  const handleViewDetails = (lead) => {
    setSelectedLead(lead);
    setDetailsOpen(true);
  };

  const handleMarkProcessed = async (leadId) => {
    const result = await executeMarkProcessed(() => leadsAPI.markProcessed(leadId));
    if (result.success) {
      refetch();
      setDetailsOpen(false);
    }
  };

  const formatDate = (dateString) => new Date(dateString).toLocaleDateString('ar-SA');
  const formatTime = (dateString) => new Date(dateString).toLocaleTimeString('ar-SA');

  const filteredLeads = leads?.filter(lead => {
    if (filter === 'pending') return !lead.deducted;
    if (filter === 'processed') return lead.deducted;
    return true;
  }) || [];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        إدارة الطلبات
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {stats?.total_leads || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الطلبات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {stats?.pending_leads || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                طلبات معلقة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {stats?.processed_leads || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                طلبات معالجة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main">
                {stats?.today_leads || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                طلبات اليوم
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filter */}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <FilterList />
        <TextField
          select
          label="تصفية الطلبات"
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          size="small"
          sx={{ minWidth: 150 }}
        >
          <MenuItem value="all">جميع الطلبات</MenuItem>
          <MenuItem value="pending">طلبات معلقة</MenuItem>
          <MenuItem value="processed">طلبات معالجة</MenuItem>
        </TextField>
      </Box>

      {/* Leads Table */}
      <Card>
        <CardContent>
          {filteredLeads.length === 0 ? (
            <Typography variant="body1" textAlign="center" py={4}>
              لا توجد طلبات
            </Typography>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>الاسم</TableCell>
                    <TableCell>الهاتف</TableCell>
                    <TableCell>البريد الإلكتروني</TableCell>
                    <TableCell>المنتج</TableCell>
                    <TableCell>التاريخ</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredLeads.map((lead) => (
                    <TableRow key={lead.id}>
                      <TableCell>{lead.name}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {lead.phone_number}
                          <IconButton
                            size="small"
                            href={`tel:${lead.phone_number}`}
                            color="primary"
                          >
                            <Phone fontSize="small" />
                          </IconButton>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {lead.email ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {lead.email}
                            <IconButton
                              size="small"
                              href={`mailto:${lead.email}`}
                              color="primary"
                            >
                              <Email fontSize="small" />
                            </IconButton>
                          </Box>
                        ) : (
                          'غير متوفر'
                        )}
                      </TableCell>
                      <TableCell>{lead.product?.name || 'غير محدد'}</TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {formatDate(lead.created_at)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatTime(lead.created_at)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={lead.deducted ? 'معالج' : 'جديد'}
                          color={lead.deducted ? 'success' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          onClick={() => handleViewDetails(lead)}
                          color="primary"
                        >
                          <Visibility />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Lead Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>تفاصيل الطلب</DialogTitle>
        <DialogContent>
          {selectedLead && (
            <Box sx={{ pt: 1 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">الاسم:</Typography>
                  <Typography variant="body1">{selectedLead.name}</Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">رقم الهاتف:</Typography>
                  <Typography variant="body1">{selectedLead.phone_number}</Typography>
                </Grid>
                
                {selectedLead.email && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">البريد الإلكتروني:</Typography>
                    <Typography variant="body1">{selectedLead.email}</Typography>
                  </Grid>
                )}
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">المنتج:</Typography>
                  <Typography variant="body1">
                    {selectedLead.product?.name || 'غير محدد'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2">التاريخ والوقت:</Typography>
                  <Typography variant="body1">
                    {formatDate(selectedLead.created_at)} - {formatTime(selectedLead.created_at)}
                  </Typography>
                </Grid>
                
                {selectedLead.message && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2">الرسالة:</Typography>
                    <Typography variant="body1">{selectedLead.message}</Typography>
                  </Grid>
                )}
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2">الحالة:</Typography>
                  <Chip
                    label={selectedLead.deducted ? 'معالج' : 'جديد'}
                    color={selectedLead.deducted ? 'success' : 'warning'}
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>إغلاق</Button>
          {selectedLead && !selectedLead.deducted && (
            <Button
              onClick={() => handleMarkProcessed(selectedLead.id)}
              variant="contained"
              startIcon={<CheckCircle />}
              disabled={processingLead}
            >
              {processingLead ? <CircularProgress size={20} /> : 'تحديد كمعالج'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LeadsPage;
