import React, { useState } from 'react';
import {
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Switch,
  FormControlLabel,
  Box,
  Typography,
  Chip
} from '@mui/material';
import {
  Settings,
  Language,
  Palette,
  AttachMoney,
  DarkMode,
  LightMode,
  Translate
} from '@mui/icons-material';
import { useApp } from '../contexts/AppContext';

const SettingsMenu = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [languageMenuOpen, setLanguageMenuOpen] = useState(false);
  const [currencyMenuOpen, setCurrencyMenuOpen] = useState(false);
  
  const {
    language,
    currency,
    themeMode,
    changeLanguage,
    changeCurrency,
    toggleTheme,
    languages,
    currencies,
    currentLanguage,
    currentCurrency,
    isDark
  } = useApp();

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setLanguageMenuOpen(false);
    setCurrencyMenuOpen(false);
  };

  const handleLanguageChange = (langCode) => {
    changeLanguage(langCode);
    handleClose();
  };

  const handleCurrencyChange = (currCode) => {
    changeCurrency(currCode);
    handleClose();
  };

  return (
    <>
      <IconButton
        onClick={handleClick}
        color="inherit"
        sx={{ ml: 1 }}
      >
        <Settings />
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            minWidth: 280,
            maxWidth: 320,
          }
        }}
      >
        {/* عنوان الإعدادات */}
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="h6" color="primary">
            ⚙️ الإعدادات
          </Typography>
        </Box>
        
        <Divider />

        {/* تبديل الثيم */}
        <MenuItem onClick={toggleTheme}>
          <ListItemIcon>
            {isDark ? <LightMode /> : <DarkMode />}
          </ListItemIcon>
          <ListItemText 
            primary={isDark ? "الوضع الفاتح" : "الوضع المظلم"}
            secondary={isDark ? "تبديل للوضع الفاتح" : "تبديل للوضع المظلم"}
          />
          <Switch
            checked={isDark}
            onChange={toggleTheme}
            size="small"
          />
        </MenuItem>

        <Divider />

        {/* اللغة */}
        <MenuItem onClick={() => setLanguageMenuOpen(!languageMenuOpen)}>
          <ListItemIcon>
            <Language />
          </ListItemIcon>
          <ListItemText 
            primary="اللغة"
            secondary={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                <span>{currentLanguage.flag}</span>
                <span>{currentLanguage.name}</span>
              </Box>
            }
          />
        </MenuItem>

        {/* قائمة اللغات الفرعية */}
        {languageMenuOpen && (
          <Box sx={{ pl: 4, pr: 2 }}>
            {Object.values(languages).map((lang) => (
              <MenuItem
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code)}
                selected={language === lang.code}
                sx={{ borderRadius: 1, mb: 0.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <span>{lang.flag}</span>
                  <span>{lang.name}</span>
                  {language === lang.code && (
                    <Chip label="✓" size="small" color="primary" />
                  )}
                </Box>
              </MenuItem>
            ))}
          </Box>
        )}

        <Divider />

        {/* العملة */}
        <MenuItem onClick={() => setCurrencyMenuOpen(!currencyMenuOpen)}>
          <ListItemIcon>
            <AttachMoney />
          </ListItemIcon>
          <ListItemText 
            primary="العملة"
            secondary={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                <span>{currentCurrency.symbol}</span>
                <span>{currentCurrency.name}</span>
              </Box>
            }
          />
        </MenuItem>

        {/* قائمة العملات الفرعية */}
        {currencyMenuOpen && (
          <Box sx={{ pl: 4, pr: 2 }}>
            {Object.values(currencies).map((curr) => (
              <MenuItem
                key={curr.code}
                onClick={() => handleCurrencyChange(curr.code)}
                selected={currency === curr.code}
                sx={{ borderRadius: 1, mb: 0.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <span>{curr.symbol}</span>
                  <span>{curr.name}</span>
                  {currency === curr.code && (
                    <Chip label="✓" size="small" color="primary" />
                  )}
                </Box>
              </MenuItem>
            ))}
          </Box>
        )}

        <Divider />

        {/* معلومات إضافية */}
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="caption" color="text.secondary">
            lnk2store v1.0
          </Typography>
        </Box>
      </Menu>
    </>
  );
};

export default SettingsMenu;
