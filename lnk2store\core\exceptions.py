"""
Custom exception handlers and error responses
"""
import logging
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ValidationError
from django.db import IntegrityError

logger = logging.getLogger(__name__)


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides consistent error responses
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # Log the exception
    logger.error(f"API Exception: {exc}", exc_info=True, extra={
        'request': context.get('request'),
        'view': context.get('view'),
    })
    
    if response is not None:
        # Customize the response format
        custom_response_data = {
            'error': True,
            'message': 'حدث خطأ في العملية',
            'details': response.data,
            'status_code': response.status_code
        }
        
        # Handle specific error types
        if response.status_code == 400:
            custom_response_data['message'] = 'بيانات غير صحيحة'
        elif response.status_code == 401:
            custom_response_data['message'] = 'يجب تسجيل الدخول أولاً'
        elif response.status_code == 403:
            custom_response_data['message'] = 'ليس لديك صلاحية للوصول'
        elif response.status_code == 404:
            custom_response_data['message'] = 'العنصر المطلوب غير موجود'
        elif response.status_code == 429:
            custom_response_data['message'] = 'تم تجاوز الحد المسموح من الطلبات'
        elif response.status_code >= 500:
            custom_response_data['message'] = 'خطأ في الخادم'
            
        response.data = custom_response_data
        
    else:
        # Handle Django validation errors
        if isinstance(exc, ValidationError):
            response = Response({
                'error': True,
                'message': 'خطأ في التحقق من البيانات',
                'details': exc.message_dict if hasattr(exc, 'message_dict') else str(exc),
                'status_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # Handle database integrity errors
        elif isinstance(exc, IntegrityError):
            response = Response({
                'error': True,
                'message': 'خطأ في قاعدة البيانات - قد تكون البيانات مكررة',
                'details': str(exc),
                'status_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # Handle other exceptions
        else:
            response = Response({
                'error': True,
                'message': 'حدث خطأ غير متوقع',
                'details': str(exc),
                'status_code': 500
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return response


class APIException(Exception):
    """Base API exception class"""
    def __init__(self, message, status_code=400, details=None):
        self.message = message
        self.status_code = status_code
        self.details = details
        super().__init__(self.message)


class ValidationException(APIException):
    """Validation error exception"""
    def __init__(self, message, details=None):
        super().__init__(message, 400, details)


class PermissionException(APIException):
    """Permission denied exception"""
    def __init__(self, message="ليس لديك صلاحية للوصول"):
        super().__init__(message, 403)


class NotFoundException(APIException):
    """Not found exception"""
    def __init__(self, message="العنصر المطلوب غير موجود"):
        super().__init__(message, 404)


class BusinessLogicException(APIException):
    """Business logic error exception"""
    def __init__(self, message, details=None):
        super().__init__(message, 422, details)
