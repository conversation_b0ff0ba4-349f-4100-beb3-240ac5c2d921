{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { isFragment } from 'react-is';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Avatar, { avatarClasses } from \"../Avatar/index.js\";\nimport avatarGroupClasses, { getAvatarGroupUtilityClass } from \"./avatarGroupClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPACINGS = {\n  small: -16,\n  medium: -8\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, classes);\n};\nconst AvatarGroupRoot = styled('div', {\n  name: 'MuiAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [{\n      [`& .${avatarGroupClasses.avatar}`]: styles.avatar\n    }, styles.root];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  flexDirection: 'row-reverse',\n  [`& .${avatarClasses.root}`]: {\n    border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n    boxSizing: 'content-box',\n    marginLeft: 'var(--AvatarGroup-spacing, -8px)',\n    '&:last-child': {\n      marginLeft: 0\n    }\n  }\n})));\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatarGroup'\n  });\n  const {\n    children: childrenProp,\n    className,\n    component = 'div',\n    componentsProps,\n    max = 5,\n    renderSurplus,\n    slotProps = {},\n    slots = {},\n    spacing = 'medium',\n    total,\n    variant = 'circular',\n    ...other\n  } = props;\n  let clampedMax = max < 2 ? 2 : max;\n  const ownerState = {\n    ...props,\n    max,\n    spacing,\n    component,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const children = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The AvatarGroup component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const totalAvatars = total || children.length;\n  if (totalAvatars === clampedMax) {\n    clampedMax += 1;\n  }\n  clampedMax = Math.min(totalAvatars + 1, clampedMax);\n  const maxAvatars = Math.min(children.length, clampedMax - 1);\n  const extraAvatars = Math.max(totalAvatars - clampedMax, totalAvatars - maxAvatars, 0);\n  const extraAvatarsElement = renderSurplus ? renderSurplus(extraAvatars) : `+${extraAvatars}`;\n  let marginValue;\n  if (ownerState.spacing && SPACINGS[ownerState.spacing] !== undefined) {\n    marginValue = SPACINGS[ownerState.spacing];\n  } else if (ownerState.spacing === 0) {\n    marginValue = 0;\n  } else {\n    marginValue = -ownerState.spacing || SPACINGS.medium;\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      surplus: slotProps.additionalAvatar ?? componentsProps?.additionalAvatar,\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [SurplusSlot, surplusProps] = useSlot('surplus', {\n    elementType: Avatar,\n    externalForwardedProps,\n    className: classes.avatar,\n    ownerState,\n    additionalProps: {\n      variant\n    }\n  });\n  return /*#__PURE__*/_jsxs(AvatarGroupRoot, {\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    style: {\n      '--AvatarGroup-spacing': `${marginValue}px`,\n      // marginValue is always defined\n      ...other.style\n    },\n    children: [extraAvatars ? /*#__PURE__*/_jsx(SurplusSlot, {\n      ...surplusProps,\n      children: extraAvatarsElement\n    }) : null, children.slice(0, maxAvatars).reverse().map(child => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        className: clsx(child.props.className, classes.avatar),\n        variant: child.props.variant || variant\n      });\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The avatars to stack.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   *\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  componentsProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Max avatars to show before +x.\n   * @default 5\n   */\n  max: chainPropTypes(PropTypes.number, props => {\n    if (props.max < 2) {\n      return new Error(['MUI: The prop `max` should be equal to 2 or above.', 'A value below is clamped to 2.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * custom renderer of extraAvatars\n   * @param {number} surplus number of extra avatars\n   * @returns {React.ReactNode} custom element to display\n   */\n  renderSurplus: PropTypes.func,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object,\n    surplus: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    surplus: PropTypes.elementType\n  }),\n  /**\n   * Spacing between avatars.\n   * @default 'medium'\n   */\n  spacing: PropTypes.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.number]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The total number of avatars. Used for calculating the number of extra avatars.\n   * @default children.length\n   */\n  total: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;", "map": {"version": 3, "names": ["React", "PropTypes", "isFragment", "clsx", "chainPropTypes", "composeClasses", "styled", "memoTheme", "useDefaultProps", "Avatar", "avatarClasses", "avatarGroupClasses", "getAvatarGroupUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "SPACINGS", "small", "medium", "useUtilityClasses", "ownerState", "classes", "slots", "root", "avatar", "AvatarGroupRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "display", "flexDirection", "border", "vars", "palette", "background", "default", "boxSizing", "marginLeft", "AvatarGroup", "forwardRef", "inProps", "ref", "children", "childrenProp", "className", "component", "componentsProps", "max", "renderSurplus", "slotProps", "spacing", "total", "variant", "other", "clampedMax", "Children", "toArray", "filter", "child", "process", "env", "NODE_ENV", "console", "error", "join", "isValidElement", "totalAvatars", "length", "Math", "min", "maxAvatars", "extraAvatars", "extraAvatarsElement", "marginValue", "undefined", "externalForwardedProps", "surplus", "additionalAvatar", "SurplusSlot", "surplusProps", "elementType", "additionalProps", "as", "style", "slice", "reverse", "map", "cloneElement", "propTypes", "node", "object", "string", "shape", "number", "Error", "func", "oneOfType", "oneOf", "sx", "arrayOf", "bool"], "sources": ["D:/apps/lnk2store/frontend/node_modules/@mui/material/esm/AvatarGroup/AvatarGroup.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { isFragment } from 'react-is';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Avatar, { avatarClasses } from \"../Avatar/index.js\";\nimport avatarGroupClasses, { getAvatarGroupUtilityClass } from \"./avatarGroupClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPACINGS = {\n  small: -16,\n  medium: -8\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, classes);\n};\nconst AvatarGroupRoot = styled('div', {\n  name: 'MuiAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [{\n      [`& .${avatarGroupClasses.avatar}`]: styles.avatar\n    }, styles.root];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  flexDirection: 'row-reverse',\n  [`& .${avatarClasses.root}`]: {\n    border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n    boxSizing: 'content-box',\n    marginLeft: 'var(--AvatarGroup-spacing, -8px)',\n    '&:last-child': {\n      marginLeft: 0\n    }\n  }\n})));\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatarGroup'\n  });\n  const {\n    children: childrenProp,\n    className,\n    component = 'div',\n    componentsProps,\n    max = 5,\n    renderSurplus,\n    slotProps = {},\n    slots = {},\n    spacing = 'medium',\n    total,\n    variant = 'circular',\n    ...other\n  } = props;\n  let clampedMax = max < 2 ? 2 : max;\n  const ownerState = {\n    ...props,\n    max,\n    spacing,\n    component,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const children = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The AvatarGroup component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const totalAvatars = total || children.length;\n  if (totalAvatars === clampedMax) {\n    clampedMax += 1;\n  }\n  clampedMax = Math.min(totalAvatars + 1, clampedMax);\n  const maxAvatars = Math.min(children.length, clampedMax - 1);\n  const extraAvatars = Math.max(totalAvatars - clampedMax, totalAvatars - maxAvatars, 0);\n  const extraAvatarsElement = renderSurplus ? renderSurplus(extraAvatars) : `+${extraAvatars}`;\n  let marginValue;\n  if (ownerState.spacing && SPACINGS[ownerState.spacing] !== undefined) {\n    marginValue = SPACINGS[ownerState.spacing];\n  } else if (ownerState.spacing === 0) {\n    marginValue = 0;\n  } else {\n    marginValue = -ownerState.spacing || SPACINGS.medium;\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      surplus: slotProps.additionalAvatar ?? componentsProps?.additionalAvatar,\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [SurplusSlot, surplusProps] = useSlot('surplus', {\n    elementType: Avatar,\n    externalForwardedProps,\n    className: classes.avatar,\n    ownerState,\n    additionalProps: {\n      variant\n    }\n  });\n  return /*#__PURE__*/_jsxs(AvatarGroupRoot, {\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    style: {\n      '--AvatarGroup-spacing': `${marginValue}px`,\n      // marginValue is always defined\n      ...other.style\n    },\n    children: [extraAvatars ? /*#__PURE__*/_jsx(SurplusSlot, {\n      ...surplusProps,\n      children: extraAvatarsElement\n    }) : null, children.slice(0, maxAvatars).reverse().map(child => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        className: clsx(child.props.className, classes.avatar),\n        variant: child.props.variant || variant\n      });\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The avatars to stack.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   *\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  componentsProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Max avatars to show before +x.\n   * @default 5\n   */\n  max: chainPropTypes(PropTypes.number, props => {\n    if (props.max < 2) {\n      return new Error(['MUI: The prop `max` should be equal to 2 or above.', 'A value below is clamped to 2.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * custom renderer of extraAvatars\n   * @param {number} surplus number of extra avatars\n   * @returns {React.ReactNode} custom element to display\n   */\n  renderSurplus: PropTypes.func,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object,\n    surplus: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    surplus: PropTypes.elementType\n  }),\n  /**\n   * Spacing between avatars.\n   * @default 'medium'\n   */\n  spacing: PropTypes.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.number]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The total number of avatars. Used for calculating the number of extra avatars.\n   * @default children.length\n   */\n  total: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,MAAM,IAAIC,aAAa,QAAQ,oBAAoB;AAC1D,OAAOC,kBAAkB,IAAIC,0BAA0B,QAAQ,yBAAyB;AACxF,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,QAAQ,GAAG;EACfC,KAAK,EAAE,CAAC,EAAE;EACVC,MAAM,EAAE,CAAC;AACX,CAAC;AACD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,MAAM,EAAE,CAAC,QAAQ;EACnB,CAAC;EACD,OAAOrB,cAAc,CAACmB,KAAK,EAAEZ,0BAA0B,EAAEW,OAAO,CAAC;AACnE,CAAC;AACD,MAAMI,eAAe,GAAGrB,MAAM,CAAC,KAAK,EAAE;EACpCsB,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,OAAO,CAAC;MACN,CAAC,MAAMrB,kBAAkB,CAACe,MAAM,EAAE,GAAGM,MAAM,CAACN;IAC9C,CAAC,EAAEM,MAAM,CAACP,IAAI,CAAC;EACjB;AACF,CAAC,CAAC,CAAClB,SAAS,CAAC,CAAC;EACZ0B;AACF,CAAC,MAAM;EACLC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,aAAa;EAC5B,CAAC,MAAMzB,aAAa,CAACe,IAAI,EAAE,GAAG;IAC5BW,MAAM,EAAE,aAAa,CAACH,KAAK,CAACI,IAAI,IAAIJ,KAAK,EAAEK,OAAO,CAACC,UAAU,CAACC,OAAO,EAAE;IACvEC,SAAS,EAAE,aAAa;IACxBC,UAAU,EAAE,kCAAkC;IAC9C,cAAc,EAAE;MACdA,UAAU,EAAE;IACd;EACF;AACF,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,WAAW,GAAG,aAAa3C,KAAK,CAAC4C,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMf,KAAK,GAAGvB,eAAe,CAAC;IAC5BuB,KAAK,EAAEc,OAAO;IACdjB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJmB,QAAQ,EAAEC,YAAY;IACtBC,SAAS;IACTC,SAAS,GAAG,KAAK;IACjBC,eAAe;IACfC,GAAG,GAAG,CAAC;IACPC,aAAa;IACbC,SAAS,GAAG,CAAC,CAAC;IACd9B,KAAK,GAAG,CAAC,CAAC;IACV+B,OAAO,GAAG,QAAQ;IAClBC,KAAK;IACLC,OAAO,GAAG,UAAU;IACpB,GAAGC;EACL,CAAC,GAAG3B,KAAK;EACT,IAAI4B,UAAU,GAAGP,GAAG,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAClC,MAAM9B,UAAU,GAAG;IACjB,GAAGS,KAAK;IACRqB,GAAG;IACHG,OAAO;IACPL,SAAS;IACTO;EACF,CAAC;EACD,MAAMlC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMyB,QAAQ,GAAG/C,KAAK,CAAC4D,QAAQ,CAACC,OAAO,CAACb,YAAY,CAAC,CAACc,MAAM,CAACC,KAAK,IAAI;IACpE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIhE,UAAU,CAAC6D,KAAK,CAAC,EAAE;QACrBI,OAAO,CAACC,KAAK,CAAC,CAAC,sEAAsE,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5I;IACF;IACA,OAAO,aAAarE,KAAK,CAACsE,cAAc,CAACP,KAAK,CAAC;EACjD,CAAC,CAAC;EACF,MAAMQ,YAAY,GAAGf,KAAK,IAAIT,QAAQ,CAACyB,MAAM;EAC7C,IAAID,YAAY,KAAKZ,UAAU,EAAE;IAC/BA,UAAU,IAAI,CAAC;EACjB;EACAA,UAAU,GAAGc,IAAI,CAACC,GAAG,CAACH,YAAY,GAAG,CAAC,EAAEZ,UAAU,CAAC;EACnD,MAAMgB,UAAU,GAAGF,IAAI,CAACC,GAAG,CAAC3B,QAAQ,CAACyB,MAAM,EAAEb,UAAU,GAAG,CAAC,CAAC;EAC5D,MAAMiB,YAAY,GAAGH,IAAI,CAACrB,GAAG,CAACmB,YAAY,GAAGZ,UAAU,EAAEY,YAAY,GAAGI,UAAU,EAAE,CAAC,CAAC;EACtF,MAAME,mBAAmB,GAAGxB,aAAa,GAAGA,aAAa,CAACuB,YAAY,CAAC,GAAG,IAAIA,YAAY,EAAE;EAC5F,IAAIE,WAAW;EACf,IAAIxD,UAAU,CAACiC,OAAO,IAAIrC,QAAQ,CAACI,UAAU,CAACiC,OAAO,CAAC,KAAKwB,SAAS,EAAE;IACpED,WAAW,GAAG5D,QAAQ,CAACI,UAAU,CAACiC,OAAO,CAAC;EAC5C,CAAC,MAAM,IAAIjC,UAAU,CAACiC,OAAO,KAAK,CAAC,EAAE;IACnCuB,WAAW,GAAG,CAAC;EACjB,CAAC,MAAM;IACLA,WAAW,GAAG,CAACxD,UAAU,CAACiC,OAAO,IAAIrC,QAAQ,CAACE,MAAM;EACtD;EACA,MAAM4D,sBAAsB,GAAG;IAC7BxD,KAAK;IACL8B,SAAS,EAAE;MACT2B,OAAO,EAAE3B,SAAS,CAAC4B,gBAAgB,IAAI/B,eAAe,EAAE+B,gBAAgB;MACxE,GAAG/B,eAAe;MAClB,GAAGG;IACL;EACF,CAAC;EACD,MAAM,CAAC6B,WAAW,EAAEC,YAAY,CAAC,GAAGvE,OAAO,CAAC,SAAS,EAAE;IACrDwE,WAAW,EAAE5E,MAAM;IACnBuE,sBAAsB;IACtB/B,SAAS,EAAE1B,OAAO,CAACG,MAAM;IACzBJ,UAAU;IACVgE,eAAe,EAAE;MACf7B;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAaxC,KAAK,CAACU,eAAe,EAAE;IACzC4D,EAAE,EAAErC,SAAS;IACb5B,UAAU,EAAEA,UAAU;IACtB2B,SAAS,EAAE9C,IAAI,CAACoB,OAAO,CAACE,IAAI,EAAEwB,SAAS,CAAC;IACxCH,GAAG,EAAEA,GAAG;IACR,GAAGY,KAAK;IACR8B,KAAK,EAAE;MACL,uBAAuB,EAAE,GAAGV,WAAW,IAAI;MAC3C;MACA,GAAGpB,KAAK,CAAC8B;IACX,CAAC;IACDzC,QAAQ,EAAE,CAAC6B,YAAY,GAAG,aAAa7D,IAAI,CAACoE,WAAW,EAAE;MACvD,GAAGC,YAAY;MACfrC,QAAQ,EAAE8B;IACZ,CAAC,CAAC,GAAG,IAAI,EAAE9B,QAAQ,CAAC0C,KAAK,CAAC,CAAC,EAAEd,UAAU,CAAC,CAACe,OAAO,CAAC,CAAC,CAACC,GAAG,CAAC5B,KAAK,IAAI;MAC9D,OAAO,aAAa/D,KAAK,CAAC4F,YAAY,CAAC7B,KAAK,EAAE;QAC5Cd,SAAS,EAAE9C,IAAI,CAAC4D,KAAK,CAAChC,KAAK,CAACkB,SAAS,EAAE1B,OAAO,CAACG,MAAM,CAAC;QACtD+B,OAAO,EAAEM,KAAK,CAAChC,KAAK,CAAC0B,OAAO,IAAIA;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,WAAW,CAACkD,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACE9C,QAAQ,EAAE9C,SAAS,CAAC6F,IAAI;EACxB;AACF;AACA;EACEvE,OAAO,EAAEtB,SAAS,CAAC8F,MAAM;EACzB;AACF;AACA;EACE9C,SAAS,EAAEhD,SAAS,CAAC+F,MAAM;EAC3B;AACF;AACA;AACA;EACE9C,SAAS,EAAEjD,SAAS,CAACoF,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACElC,eAAe,EAAElD,SAAS,CAACgG,KAAK,CAAC;IAC/Bf,gBAAgB,EAAEjF,SAAS,CAAC8F;EAC9B,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE3C,GAAG,EAAEhD,cAAc,CAACH,SAAS,CAACiG,MAAM,EAAEnE,KAAK,IAAI;IAC7C,IAAIA,KAAK,CAACqB,GAAG,GAAG,CAAC,EAAE;MACjB,OAAO,IAAI+C,KAAK,CAAC,CAAC,oDAAoD,EAAE,gCAAgC,CAAC,CAAC9B,IAAI,CAAC,IAAI,CAAC,CAAC;IACvH;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEhB,aAAa,EAAEpD,SAAS,CAACmG,IAAI;EAC7B;AACF;AACA;AACA;EACE9C,SAAS,EAAErD,SAAS,CAACgG,KAAK,CAAC;IACzBf,gBAAgB,EAAEjF,SAAS,CAAC8F,MAAM;IAClCd,OAAO,EAAEhF,SAAS,CAACoG,SAAS,CAAC,CAACpG,SAAS,CAACmG,IAAI,EAAEnG,SAAS,CAAC8F,MAAM,CAAC;EACjE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEvE,KAAK,EAAEvB,SAAS,CAACgG,KAAK,CAAC;IACrBhB,OAAO,EAAEhF,SAAS,CAACoF;EACrB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE9B,OAAO,EAAEtD,SAAS,CAACoG,SAAS,CAAC,CAACpG,SAAS,CAACqG,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAErG,SAAS,CAACiG,MAAM,CAAC,CAAC;EACtF;AACF;AACA;EACEV,KAAK,EAAEvF,SAAS,CAAC8F,MAAM;EACvB;AACF;AACA;EACEQ,EAAE,EAAEtG,SAAS,CAACoG,SAAS,CAAC,CAACpG,SAAS,CAACuG,OAAO,CAACvG,SAAS,CAACoG,SAAS,CAAC,CAACpG,SAAS,CAACmG,IAAI,EAAEnG,SAAS,CAAC8F,MAAM,EAAE9F,SAAS,CAACwG,IAAI,CAAC,CAAC,CAAC,EAAExG,SAAS,CAACmG,IAAI,EAAEnG,SAAS,CAAC8F,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEvC,KAAK,EAAEvD,SAAS,CAACiG,MAAM;EACvB;AACF;AACA;AACA;EACEzC,OAAO,EAAExD,SAAS,CAAC,sCAAsCoG,SAAS,CAAC,CAACpG,SAAS,CAACqG,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAErG,SAAS,CAAC+F,MAAM,CAAC;AAC3I,CAAC,GAAG,KAAK,CAAC;AACV,eAAerD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}