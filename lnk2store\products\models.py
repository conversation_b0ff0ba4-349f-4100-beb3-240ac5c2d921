from django.db import models
from django.core.exceptions import ValidationError
from accounts.models import User

class ProductCategory(models.Model):
    """Product categories"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.Char<PERSON>ield(max_length=50, blank=True, help_text="Font Awesome icon class")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Product Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

class Product(models.Model):
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('archived', 'مؤرشف'),
    ]

    # Basic Information
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='products')
    name = models.Char<PERSON>ield(max_length=200)
    description = models.TextField()
    category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, null=True, blank=True)

    # Media
    image = models.ImageField(upload_to='products/', blank=True)

    # Pricing
    price = models.DecimalField(max_digits=10, decimal_places=2)
    old_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Status and Metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    is_featured = models.BooleanField(default=False)
    view_count = models.IntegerField(default=0)
    lead_count = models.IntegerField(default=0)

    # SEO
    meta_title = models.CharField(max_length=60, blank=True)
    meta_description = models.CharField(max_length=160, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['category', 'status']),
        ]

    def __str__(self):
        return f"{self.name} - {self.user.username}"

    def clean(self):
        """Validate product limits for free users"""
        if self.user and not self.user.is_premium:
            # Check if user already has 3 products (excluding current one)
            existing_products = Product.objects.filter(user=self.user).exclude(pk=self.pk)
            if existing_products.count() >= 3:
                raise ValidationError('المستخدمون المجانيون يمكنهم إنشاء 3 منتجات فقط')

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def increment_views(self):
        """Increment view counter"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def increment_leads(self):
        """Increment lead counter"""
        self.lead_count += 1
        self.save(update_fields=['lead_count'])

    @property
    def has_discount(self):
        """Check if product has discount"""
        return self.old_price and self.old_price > self.price

    @property
    def discount_percentage(self):
        """Calculate discount percentage"""
        if self.has_discount:
            return round(((self.old_price - self.price) / self.old_price) * 100)
        return 0

class ProductImage(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/images/')

    def __str__(self):
        return self.product.name + " Image"

class ProductColor(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='colors')
    color = models.CharField(max_length=50)

    def __str__(self):
        return self.product.name + " Color: " + self.color

class ProductSize(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='sizes')
    size = models.CharField(max_length=50)

    def __str__(self):
        return self.product.name + " Size: " + self.size