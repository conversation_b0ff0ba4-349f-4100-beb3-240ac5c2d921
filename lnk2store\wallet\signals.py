from django.db.models.signals import post_save
from django.dispatch import receiver
from accounts.models import User
from .models import Wallet
from decimal import Decimal

@receiver(post_save, sender=User)
def create_user_wallet(sender, instance, created, **kwargs):
    """Create wallet when user is created"""
    if created:
        Wallet.objects.create(
            user=instance,
            balance=Decimal('0.00'),
            low_balance_threshold=Decimal('50.00')
        )

@receiver(post_save, sender=User)
def save_user_wallet(sender, instance, **kwargs):
    """Ensure wallet exists for user"""
    if not hasattr(instance, 'wallet'):
        Wallet.objects.create(
            user=instance,
            balance=Decimal('0.00'),
            low_balance_threshold=Decimal('50.00')
        )
