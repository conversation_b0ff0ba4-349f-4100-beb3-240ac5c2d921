from django.db import models
from django.utils import timezone
from accounts.models import User
from products.models import Product
import logging

logger = logging.getLogger(__name__)

class Lead(models.Model):
    STATUS_CHOICES = [
        ('new', 'جديد'),
        ('contacted', 'تم التواصل'),
        ('converted', 'تم التحويل'),
        ('rejected', 'مرفوض'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=200)
    email = models.EmailField(blank=True)
    phone_number = models.CharField(max_length=20)
    message = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')
    deducted = models.BooleanField(default=False)
    whatsapp_sent = models.BooleanField(default=False)
    whatsapp_sent_at = models.DateTimeField(null=True, blank=True)
    whatsapp_message_id = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.user.username} ({self.get_status_display()})"

    def send_whatsapp_notification(self):
        """Send WhatsApp notification to business owner"""
        if self.whatsapp_sent:
            return {'success': False, 'error': 'WhatsApp already sent for this lead'}

        # Get user's phone number (assuming it's stored in user profile)
        user_phone = getattr(self.user, 'phone_number', None)
        if not user_phone:
            return {'success': False, 'error': 'User phone number not available'}

        try:
            from .whatsapp_service import send_lead_to_whatsapp
            result = send_lead_to_whatsapp(self, user_phone)

            if result['success']:
                self.whatsapp_sent = True
                self.whatsapp_sent_at = timezone.now()
                self.whatsapp_message_id = result.get('message_id', '')
                self.save()

                logger.info(f"WhatsApp sent successfully for lead {self.id}")
            else:
                logger.error(f"WhatsApp send failed for lead {self.id}: {result.get('error')}")

            return result

        except Exception as e:
            logger.error(f"Error sending WhatsApp for lead {self.id}: {str(e)}")
            return {'success': False, 'error': str(e)}

    def process_lead(self):
        """Process lead: deduct from wallet and send WhatsApp"""
        from wallet.models import Wallet
        from decimal import Decimal

        if self.deducted:
            return {'success': False, 'error': 'Lead already processed'}

        try:
            # Get or create wallet
            wallet, created = Wallet.objects.get_or_create(user=self.user)
            lead_cost = Decimal('10.00')  # This could be configurable

            # Check if wallet has sufficient balance
            if not wallet.can_deduct(lead_cost):
                return {
                    'success': False,
                    'error': 'Insufficient wallet balance',
                    'required': float(lead_cost),
                    'available': float(wallet.balance)
                }

            # Deduct from wallet
            wallet.deduct(
                amount=lead_cost,
                description=f'Lead cost for {self.name} - {self.phone_number}'
            )

            # Mark as deducted
            self.deducted = True
            self.save()

            # Send WhatsApp notification
            whatsapp_result = self.send_whatsapp_notification()

            return {
                'success': True,
                'deducted_amount': float(lead_cost),
                'new_balance': float(wallet.balance),
                'whatsapp_sent': whatsapp_result['success'],
                'whatsapp_error': whatsapp_result.get('error') if not whatsapp_result['success'] else None
            }

        except Exception as e:
            logger.error(f"Error processing lead {self.id}: {str(e)}")
            return {'success': False, 'error': str(e)}
