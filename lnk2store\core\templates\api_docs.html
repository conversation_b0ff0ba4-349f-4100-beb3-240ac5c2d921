<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>lnk2store API Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.2rem;
        }
        
        .api-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .api-section h2 {
            color: #667eea;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .endpoint {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .endpoint h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.put { background: #ffc107; color: black; }
        .method.delete { background: #dc3545; color: white; }
        
        .url {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin: 10px 0;
            word-break: break-all;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .feature h3 {
            margin-bottom: 10px;
        }
        
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .status.active {
            background: #28a745;
            color: white;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 lnk2store API</h1>
            <p>منصة SaaS لإنشاء صفحات تسويقية مع نظام جمع الطلبات</p>
            <span class="status active">نشط</span>
        </div>
        
        <div class="api-section">
            <h2>🔐 المصادقة</h2>
            <div class="endpoint">
                <h3><span class="method post">POST</span> تسجيل الدخول</h3>
                <div class="url">/api/v1/accounts/login/</div>
                <p>احصل على JWT token لاستخدامه في الطلبات المحمية</p>
            </div>
            <div class="endpoint">
                <h3><span class="method post">POST</span> إنشاء حساب</h3>
                <div class="url">/api/v1/accounts/register/</div>
                <p>إنشاء حساب مستخدم جديد</p>
            </div>
            <div class="endpoint">
                <h3><span class="method get">GET</span> ملف المستخدم</h3>
                <div class="url">/api/v1/accounts/profile/</div>
                <p>جلب معلومات المستخدم الحالي</p>
            </div>
        </div>
        
        <div class="api-section">
            <h2>📦 المنتجات</h2>
            <div class="endpoint">
                <h3><span class="method get">GET</span> قائمة المنتجات</h3>
                <div class="url">/api/v1/products/</div>
                <p>جلب جميع المنتجات المتاحة</p>
            </div>
            <div class="endpoint">
                <h3><span class="method post">POST</span> إنشاء منتج</h3>
                <div class="url">/api/v1/products/create/</div>
                <p>إنشاء منتج جديد (يتطلب مصادقة)</p>
            </div>
            <div class="endpoint">
                <h3><span class="method get">GET</span> تفاصيل المنتج</h3>
                <div class="url">/api/v1/products/{id}/</div>
                <p>جلب تفاصيل منتج محدد</p>
            </div>
        </div>
        
        <div class="api-section">
            <h2>📋 الطلبات</h2>
            <div class="endpoint">
                <h3><span class="method post">POST</span> إنشاء طلب</h3>
                <div class="url">/api/v1/leads/create/</div>
                <p>إنشاء طلب جديد من العميل</p>
            </div>
            <div class="endpoint">
                <h3><span class="method get">GET</span> طلباتي</h3>
                <div class="url">/api/v1/leads/my-leads/</div>
                <p>جلب طلبات المستخدم الحالي</p>
            </div>
        </div>
        
        <div class="api-section">
            <h2>💰 المحفظة</h2>
            <div class="endpoint">
                <h3><span class="method get">GET</span> رصيد المحفظة</h3>
                <div class="url">/api/v1/wallet/</div>
                <p>جلب رصيد المحفظة الحالي</p>
            </div>
            <div class="endpoint">
                <h3><span class="method post">POST</span> شحن المحفظة</h3>
                <div class="url">/api/v1/wallet/recharge/</div>
                <p>إضافة رصيد للمحفظة</p>
            </div>
        </div>
        
        <div class="api-section">
            <h2>🎨 القوالب</h2>
            <div class="endpoint">
                <h3><span class="method get">GET</span> قائمة القوالب</h3>
                <div class="url">/api/v1/templates/templates/</div>
                <p>جلب جميع القوالب المتاحة</p>
            </div>
            <div class="endpoint">
                <h3><span class="method get">GET</span> صفحاتي</h3>
                <div class="url">/api/v1/templates/my-pages/</div>
                <p>جلب صفحات المستخدم</p>
            </div>
        </div>
        
        <div class="api-section">
            <h2>✨ المزايا الرئيسية</h2>
            <div class="features">
                <div class="feature">
                    <h3>💳 الدفع لكل طلب</h3>
                    <p>ادفع فقط مقابل الطلبات التي تستقبلها</p>
                </div>
                <div class="feature">
                    <h3>📱 تكامل WhatsApp</h3>
                    <p>إرسال الطلبات فوراً للواتساب</p>
                </div>
                <div class="feature">
                    <h3>🎨 قوالب متعددة</h3>
                    <p>اختر من مجموعة قوالب احترافية</p>
                </div>
                <div class="feature">
                    <h3>📊 إحصائيات شاملة</h3>
                    <p>تتبع أداء منتجاتك وطلباتك</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>© 2025 lnk2store - منصة إنشاء الصفحات التسويقية</p>
        <p>API Version 1.0 | Status: <span class="status active">نشط</span></p>
    </div>
</body>
</html>
