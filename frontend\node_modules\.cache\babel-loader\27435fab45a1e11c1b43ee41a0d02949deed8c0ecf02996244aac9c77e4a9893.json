{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\ShoppingTemplate.js\";\nimport React from 'react';\nimport { Box, Container, Typography, Button, Grid, Card, CardMedia, CardContent, Chip, Rating, IconButton, Badge } from '@mui/material';\nimport { ShoppingCart, Favorite, Share, LocalShipping, Security, Support } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShoppingTemplate = ({\n  product\n}) => {\n  const sampleProduct = {\n    id: 1,\n    name: 'منتج مميز',\n    price: 299,\n    originalPrice: 399,\n    rating: 4.5,\n    reviews: 128,\n    images: ['/api/placeholder/400/400', '/api/placeholder/400/400', '/api/placeholder/400/400'],\n    description: 'وصف تفصيلي للمنتج مع جميع المزايا والخصائص المهمة التي يحتاجها العميل لاتخاذ قرار الشراء.',\n    features: ['جودة عالية مضمونة', 'ضمان لمدة سنة كاملة', 'شحن مجاني لجميع المحافظات', 'إمكانية الإرجاع خلال 14 يوم'],\n    inStock: true,\n    discount: 25\n  };\n  const productData = product || sampleProduct;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      bgcolor: 'background.default',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: 'white',\n        borderBottom: '1px solid',\n        borderColor: 'divider',\n        py: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600\n            },\n            children: \"\\u0645\\u062A\\u062C\\u0631 Lnk2Store\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: 3,\n                color: \"primary\",\n                children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        py: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'sticky',\n              top: 20\n            },\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                height: \"400\",\n                image: productData.images[0],\n                alt: productData.name,\n                sx: {\n                  objectFit: 'cover'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), productData.discount > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: `خصم ${productData.discount}%`,\n                color: \"error\",\n                sx: {\n                  position: 'absolute',\n                  top: 16,\n                  right: 16,\n                  fontWeight: 600\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1,\n                overflowX: 'auto'\n              },\n              children: productData.images.slice(0, 4).map((image, index) => /*#__PURE__*/_jsxDEV(Card, {\n                sx: {\n                  minWidth: 80,\n                  cursor: 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(CardMedia, {\n                  component: \"img\",\n                  height: \"80\",\n                  image: image,\n                  alt: `${productData.name} ${index + 1}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'sticky',\n              top: 20\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              sx: {\n                fontWeight: 700\n              },\n              children: productData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Rating, {\n                value: productData.rating,\n                readOnly: true,\n                precision: 0.5\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"(\", productData.reviews, \" \\u062A\\u0642\\u064A\\u064A\\u0645)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"primary\",\n                  sx: {\n                    fontWeight: 700\n                  },\n                  children: [productData.price, \" \\u0631.\\u0633\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), productData.originalPrice > productData.price && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    textDecoration: 'line-through',\n                    color: 'text.secondary'\n                  },\n                  children: [productData.originalPrice, \" \\u0631.\\u0633\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"success.main\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: [\"\\u0648\\u0641\\u0631 \", productData.originalPrice - productData.price, \" \\u0631.\\u0633\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: productData.inStock ? \"متوفر في المخزن\" : \"غير متوفر\",\n                color: productData.inStock ? \"success\" : \"error\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 3,\n                lineHeight: 1.8\n              },\n              children: productData.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 4\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"\\u0627\\u0644\\u0645\\u0632\\u0627\\u064A\\u0627 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 1\n                },\n                children: productData.features.map((feature, index) => /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    component: \"span\",\n                    sx: {\n                      color: 'success.main',\n                      mr: 1\n                    },\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 23\n                  }, this), feature]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                mb: 4\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                size: \"large\",\n                startIcon: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 30\n                }, this),\n                sx: {\n                  flex: 1,\n                  py: 1.5,\n                  fontSize: '1.1rem',\n                  fontWeight: 600\n                },\n                disabled: !productData.inStock,\n                children: \"\\u0623\\u0636\\u0641 \\u0625\\u0644\\u0649 \\u0627\\u0644\\u0633\\u0644\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"large\",\n                color: \"primary\",\n                children: /*#__PURE__*/_jsxDEV(Favorite, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"large\",\n                color: \"primary\",\n                children: /*#__PURE__*/_jsxDEV(Share, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 3,\n                justifyContent: 'center',\n                pt: 3,\n                borderTop: '1px solid',\n                borderColor: 'divider'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(LocalShipping, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: '2rem',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: \"\\u0634\\u062D\\u0646 \\u0645\\u062C\\u0627\\u0646\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Security, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: '2rem',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: \"\\u062F\\u0641\\u0639 \\u0622\\u0645\\u0646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Support, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: '2rem',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: \"\\u062F\\u0639\\u0645 24/7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: 'primary.main',\n        color: 'white',\n        py: 6\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"md\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 700\n          },\n          children: \"\\u0627\\u0637\\u0644\\u0628 \\u0627\\u0644\\u0622\\u0646 \\u0648\\u0627\\u062D\\u0635\\u0644 \\u0639\\u0644\\u0649 \\u062E\\u0635\\u0645 \\u062E\\u0627\\u0635!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          textAlign: \"center\",\n          sx: {\n            mb: 4,\n            opacity: 0.9\n          },\n          children: \"\\u0627\\u0645\\u0644\\u0623 \\u0627\\u0644\\u0646\\u0645\\u0648\\u0630\\u062C \\u0623\\u062F\\u0646\\u0627\\u0647 \\u0648\\u0633\\u0646\\u062A\\u0648\\u0627\\u0635\\u0644 \\u0645\\u0639\\u0643 \\u062E\\u0644\\u0627\\u0644 \\u062F\\u0642\\u0627\\u0626\\u0642\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            p: 4,\n            maxWidth: 500,\n            mx: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            component: \"form\",\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644 *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  border: '2px solid',\n                  borderColor: 'grey.300',\n                  borderRadius: 1,\n                  p: 1.5,\n                  bgcolor: 'grey.50'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0633\\u0645\\u0643 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641 *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  border: '2px solid',\n                  borderColor: 'grey.300',\n                  borderRadius: 1,\n                  p: 1.5,\n                  bgcolor: 'grey.50'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"\\u0623\\u062F\\u062E\\u0644 \\u0631\\u0642\\u0645 \\u0647\\u0627\\u062A\\u0641\\u0643\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  border: '2px solid',\n                  borderColor: 'grey.300',\n                  borderRadius: 1,\n                  p: 1.5,\n                  bgcolor: 'grey.50',\n                  minHeight: 80\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"\\u0623\\u062F\\u062E\\u0644 \\u0639\\u0646\\u0648\\u0627\\u0646\\u0643 \\u0627\\u0644\\u062A\\u0641\\u0635\\u064A\\u0644\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"large\",\n              sx: {\n                bgcolor: 'success.main',\n                py: 2,\n                fontSize: '1.2rem',\n                fontWeight: 700,\n                '&:hover': {\n                  bgcolor: 'success.dark'\n                }\n              },\n              children: [\"\\u0627\\u0637\\u0644\\u0628 \\u0627\\u0644\\u0622\\u0646 - \", productData.price, \" \\u0631.\\u0633\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              textAlign: \"center\",\n              color: \"text.secondary\",\n              children: \"* \\u0627\\u0644\\u062D\\u0642\\u0648\\u0644 \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_c = ShoppingTemplate;\nexport default ShoppingTemplate;\nvar _c;\n$RefreshReg$(_c, \"ShoppingTemplate\");", "map": {"version": 3, "names": ["React", "Box", "Container", "Typography", "<PERSON><PERSON>", "Grid", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "Rating", "IconButton", "Badge", "ShoppingCart", "Favorite", "Share", "LocalShipping", "Security", "Support", "jsxDEV", "_jsxDEV", "ShoppingTemplate", "product", "sampleProduct", "id", "name", "price", "originalPrice", "rating", "reviews", "images", "description", "features", "inStock", "discount", "productData", "sx", "bgcolor", "minHeight", "children", "borderBottom", "borderColor", "py", "max<PERSON><PERSON><PERSON>", "display", "alignItems", "justifyContent", "variant", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "badgeContent", "color", "container", "spacing", "item", "xs", "md", "position", "top", "mb", "component", "height", "image", "alt", "objectFit", "label", "right", "overflowX", "slice", "map", "index", "min<PERSON><PERSON><PERSON>", "cursor", "gutterBottom", "value", "readOnly", "precision", "textDecoration", "lineHeight", "flexDirection", "feature", "mr", "size", "startIcon", "flex", "fontSize", "disabled", "pt", "borderTop", "textAlign", "opacity", "p", "mx", "border", "borderRadius", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/ShoppingTemplate.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Button,\n  Grid,\n  Card,\n  CardMedia,\n  CardContent,\n  Chip,\n  Rating,\n  IconButton,\n  Badge\n} from '@mui/material';\nimport {\n  ShoppingCart,\n  Favorite,\n  Share,\n  LocalShipping,\n  Security,\n  Support\n} from '@mui/icons-material';\n\nconst ShoppingTemplate = ({ product }) => {\n  const sampleProduct = {\n    id: 1,\n    name: 'منتج مميز',\n    price: 299,\n    originalPrice: 399,\n    rating: 4.5,\n    reviews: 128,\n    images: [\n      '/api/placeholder/400/400',\n      '/api/placeholder/400/400',\n      '/api/placeholder/400/400'\n    ],\n    description: 'وصف تفصيلي للمنتج مع جميع المزايا والخصائص المهمة التي يحتاجها العميل لاتخاذ قرار الشراء.',\n    features: [\n      'جودة عالية مضمونة',\n      'ضمان لمدة سنة كاملة',\n      'شحن مجاني لجميع المحافظات',\n      'إمكانية الإرجاع خلال 14 يوم'\n    ],\n    inStock: true,\n    discount: 25\n  };\n\n  const productData = product || sampleProduct;\n\n  return (\n    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>\n      {/* Header */}\n      <Box sx={{ bgcolor: 'white', borderBottom: '1px solid', borderColor: 'divider', py: 2 }}>\n        <Container maxWidth=\"lg\">\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n              متجر Lnk2Store\n            </Typography>\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <IconButton>\n                <Badge badgeContent={3} color=\"primary\">\n                  <ShoppingCart />\n                </Badge>\n              </IconButton>\n            </Box>\n          </Box>\n        </Container>\n      </Box>\n\n      {/* Product Section */}\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <Grid container spacing={4}>\n          {/* Product Images */}\n          <Grid item xs={12} md={6}>\n            <Box sx={{ position: 'sticky', top: 20 }}>\n              <Card sx={{ mb: 2 }}>\n                <CardMedia\n                  component=\"img\"\n                  height=\"400\"\n                  image={productData.images[0]}\n                  alt={productData.name}\n                  sx={{ objectFit: 'cover' }}\n                />\n                {productData.discount > 0 && (\n                  <Chip\n                    label={`خصم ${productData.discount}%`}\n                    color=\"error\"\n                    sx={{\n                      position: 'absolute',\n                      top: 16,\n                      right: 16,\n                      fontWeight: 600\n                    }}\n                  />\n                )}\n              </Card>\n              \n              {/* Thumbnail Images */}\n              <Box sx={{ display: 'flex', gap: 1, overflowX: 'auto' }}>\n                {productData.images.slice(0, 4).map((image, index) => (\n                  <Card key={index} sx={{ minWidth: 80, cursor: 'pointer' }}>\n                    <CardMedia\n                      component=\"img\"\n                      height=\"80\"\n                      image={image}\n                      alt={`${productData.name} ${index + 1}`}\n                    />\n                  </Card>\n                ))}\n              </Box>\n            </Box>\n          </Grid>\n\n          {/* Product Details */}\n          <Grid item xs={12} md={6}>\n            <Box sx={{ position: 'sticky', top: 20 }}>\n              <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 700 }}>\n                {productData.name}\n              </Typography>\n\n              {/* Rating */}\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>\n                <Rating value={productData.rating} readOnly precision={0.5} />\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  ({productData.reviews} تقييم)\n                </Typography>\n              </Box>\n\n              {/* Price */}\n              <Box sx={{ mb: 3 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                  <Typography variant=\"h4\" color=\"primary\" sx={{ fontWeight: 700 }}>\n                    {productData.price} ر.س\n                  </Typography>\n                  {productData.originalPrice > productData.price && (\n                    <Typography \n                      variant=\"h6\" \n                      sx={{ \n                        textDecoration: 'line-through',\n                        color: 'text.secondary'\n                      }}\n                    >\n                      {productData.originalPrice} ر.س\n                    </Typography>\n                  )}\n                </Box>\n                <Typography variant=\"body2\" color=\"success.main\" sx={{ fontWeight: 600 }}>\n                  وفر {productData.originalPrice - productData.price} ر.س\n                </Typography>\n              </Box>\n\n              {/* Stock Status */}\n              <Box sx={{ mb: 3 }}>\n                <Chip\n                  label={productData.inStock ? \"متوفر في المخزن\" : \"غير متوفر\"}\n                  color={productData.inStock ? \"success\" : \"error\"}\n                  variant=\"outlined\"\n                />\n              </Box>\n\n              {/* Description */}\n              <Typography variant=\"body1\" sx={{ mb: 3, lineHeight: 1.8 }}>\n                {productData.description}\n              </Typography>\n\n              {/* Features */}\n              <Box sx={{ mb: 4 }}>\n                <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n                  المزايا الرئيسية:\n                </Typography>\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                  {productData.features.map((feature, index) => (\n                    <Typography key={index} variant=\"body2\" sx={{ display: 'flex', alignItems: 'center' }}>\n                      <Box component=\"span\" sx={{ color: 'success.main', mr: 1 }}>✓</Box>\n                      {feature}\n                    </Typography>\n                  ))}\n                </Box>\n              </Box>\n\n              {/* Action Buttons */}\n              <Box sx={{ display: 'flex', gap: 2, mb: 4 }}>\n                <Button\n                  variant=\"contained\"\n                  size=\"large\"\n                  startIcon={<ShoppingCart />}\n                  sx={{ \n                    flex: 1,\n                    py: 1.5,\n                    fontSize: '1.1rem',\n                    fontWeight: 600\n                  }}\n                  disabled={!productData.inStock}\n                >\n                  أضف إلى السلة\n                </Button>\n                <IconButton size=\"large\" color=\"primary\">\n                  <Favorite />\n                </IconButton>\n                <IconButton size=\"large\" color=\"primary\">\n                  <Share />\n                </IconButton>\n              </Box>\n\n              {/* Trust Badges */}\n              <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', pt: 3, borderTop: '1px solid', borderColor: 'divider' }}>\n                <Box sx={{ textAlign: 'center' }}>\n                  <LocalShipping color=\"primary\" sx={{ fontSize: '2rem', mb: 1 }} />\n                  <Typography variant=\"caption\" display=\"block\">شحن مجاني</Typography>\n                </Box>\n                <Box sx={{ textAlign: 'center' }}>\n                  <Security color=\"primary\" sx={{ fontSize: '2rem', mb: 1 }} />\n                  <Typography variant=\"caption\" display=\"block\">دفع آمن</Typography>\n                </Box>\n                <Box sx={{ textAlign: 'center' }}>\n                  <Support color=\"primary\" sx={{ fontSize: '2rem', mb: 1 }} />\n                  <Typography variant=\"caption\" display=\"block\">دعم 24/7</Typography>\n                </Box>\n              </Box>\n            </Box>\n          </Grid>\n        </Grid>\n      </Container>\n\n      {/* Order Form Section */}\n      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 6 }}>\n        <Container maxWidth=\"md\">\n          <Typography variant=\"h4\" textAlign=\"center\" gutterBottom sx={{ fontWeight: 700 }}>\n            اطلب الآن واحصل على خصم خاص!\n          </Typography>\n          <Typography variant=\"h6\" textAlign=\"center\" sx={{ mb: 4, opacity: 0.9 }}>\n            املأ النموذج أدناه وسنتواصل معك خلال دقائق\n          </Typography>\n          \n          <Card sx={{ p: 4, maxWidth: 500, mx: 'auto' }}>\n            <Box component=\"form\" sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>\n              <Box>\n                <Typography variant=\"body2\" gutterBottom sx={{ fontWeight: 600 }}>\n                  الاسم الكامل *\n                </Typography>\n                <Box\n                  sx={{\n                    border: '2px solid',\n                    borderColor: 'grey.300',\n                    borderRadius: 1,\n                    p: 1.5,\n                    bgcolor: 'grey.50'\n                  }}\n                >\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    أدخل اسمك الكامل\n                  </Typography>\n                </Box>\n              </Box>\n              \n              <Box>\n                <Typography variant=\"body2\" gutterBottom sx={{ fontWeight: 600 }}>\n                  رقم الهاتف *\n                </Typography>\n                <Box\n                  sx={{\n                    border: '2px solid',\n                    borderColor: 'grey.300',\n                    borderRadius: 1,\n                    p: 1.5,\n                    bgcolor: 'grey.50'\n                  }}\n                >\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    أدخل رقم هاتفك\n                  </Typography>\n                </Box>\n              </Box>\n              \n              <Box>\n                <Typography variant=\"body2\" gutterBottom sx={{ fontWeight: 600 }}>\n                  العنوان\n                </Typography>\n                <Box\n                  sx={{\n                    border: '2px solid',\n                    borderColor: 'grey.300',\n                    borderRadius: 1,\n                    p: 1.5,\n                    bgcolor: 'grey.50',\n                    minHeight: 80\n                  }}\n                >\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    أدخل عنوانك التفصيلي\n                  </Typography>\n                </Box>\n              </Box>\n              \n              <Button\n                variant=\"contained\"\n                size=\"large\"\n                sx={{\n                  bgcolor: 'success.main',\n                  py: 2,\n                  fontSize: '1.2rem',\n                  fontWeight: 700,\n                  '&:hover': {\n                    bgcolor: 'success.dark'\n                  }\n                }}\n              >\n                اطلب الآن - {productData.price} ر.س\n              </Button>\n              \n              <Typography variant=\"caption\" textAlign=\"center\" color=\"text.secondary\">\n                * الحقول المطلوبة\n              </Typography>\n            </Box>\n          </Card>\n        </Container>\n      </Box>\n    </Box>\n  );\n};\n\nexport default ShoppingTemplate;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,YAAY,EACZC,QAAQ,EACRC,KAAK,EACLC,aAAa,EACbC,QAAQ,EACRC,OAAO,QACF,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACxC,MAAMC,aAAa,GAAG;IACpBC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,GAAG;IACVC,aAAa,EAAE,GAAG;IAClBC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,GAAG;IACZC,MAAM,EAAE,CACN,0BAA0B,EAC1B,0BAA0B,EAC1B,0BAA0B,CAC3B;IACDC,WAAW,EAAE,2FAA2F;IACxGC,QAAQ,EAAE,CACR,mBAAmB,EACnB,qBAAqB,EACrB,2BAA2B,EAC3B,6BAA6B,CAC9B;IACDC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMC,WAAW,GAAGb,OAAO,IAAIC,aAAa;EAE5C,oBACEH,OAAA,CAACnB,GAAG;IAACmC,EAAE,EAAE;MAAEC,OAAO,EAAE,oBAAoB;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAE7DnB,OAAA,CAACnB,GAAG;MAACmC,EAAE,EAAE;QAAEC,OAAO,EAAE,OAAO;QAAEG,YAAY,EAAE,WAAW;QAAEC,WAAW,EAAE,SAAS;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eACtFnB,OAAA,CAAClB,SAAS;QAACyC,QAAQ,EAAC,IAAI;QAAAJ,QAAA,eACtBnB,OAAA,CAACnB,GAAG;UAACmC,EAAE,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAAP,QAAA,gBAClFnB,OAAA,CAACjB,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAI,CAAE;YAAAT,QAAA,EAAC;UAElD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhC,OAAA,CAACnB,GAAG;YAACmC,EAAE,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAES,GAAG,EAAE;YAAE,CAAE;YAAAd,QAAA,eACnCnB,OAAA,CAACT,UAAU;cAAA4B,QAAA,eACTnB,OAAA,CAACR,KAAK;gBAAC0C,YAAY,EAAE,CAAE;gBAACC,KAAK,EAAC,SAAS;gBAAAhB,QAAA,eACrCnB,OAAA,CAACP,YAAY;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGNhC,OAAA,CAAClB,SAAS;MAACyC,QAAQ,EAAC,IAAI;MAACP,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eACrCnB,OAAA,CAACf,IAAI;QAACmD,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAlB,QAAA,gBAEzBnB,OAAA,CAACf,IAAI;UAACqD,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACvBnB,OAAA,CAACnB,GAAG;YAACmC,EAAE,EAAE;cAAEyB,QAAQ,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAG,CAAE;YAAAvB,QAAA,gBACvCnB,OAAA,CAACd,IAAI;cAAC8B,EAAE,EAAE;gBAAE2B,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBAClBnB,OAAA,CAACb,SAAS;gBACRyD,SAAS,EAAC,KAAK;gBACfC,MAAM,EAAC,KAAK;gBACZC,KAAK,EAAE/B,WAAW,CAACL,MAAM,CAAC,CAAC,CAAE;gBAC7BqC,GAAG,EAAEhC,WAAW,CAACV,IAAK;gBACtBW,EAAE,EAAE;kBAAEgC,SAAS,EAAE;gBAAQ;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACDjB,WAAW,CAACD,QAAQ,GAAG,CAAC,iBACvBd,OAAA,CAACX,IAAI;gBACH4D,KAAK,EAAE,OAAOlC,WAAW,CAACD,QAAQ,GAAI;gBACtCqB,KAAK,EAAC,OAAO;gBACbnB,EAAE,EAAE;kBACFyB,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,EAAE;kBACPQ,KAAK,EAAE,EAAE;kBACTtB,UAAU,EAAE;gBACd;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGPhC,OAAA,CAACnB,GAAG;cAACmC,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAES,GAAG,EAAE,CAAC;gBAAEkB,SAAS,EAAE;cAAO,CAAE;cAAAhC,QAAA,EACrDJ,WAAW,CAACL,MAAM,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACP,KAAK,EAAEQ,KAAK,kBAC/CtD,OAAA,CAACd,IAAI;gBAAa8B,EAAE,EAAE;kBAAEuC,QAAQ,EAAE,EAAE;kBAAEC,MAAM,EAAE;gBAAU,CAAE;gBAAArC,QAAA,eACxDnB,OAAA,CAACb,SAAS;kBACRyD,SAAS,EAAC,KAAK;kBACfC,MAAM,EAAC,IAAI;kBACXC,KAAK,EAAEA,KAAM;kBACbC,GAAG,EAAE,GAAGhC,WAAW,CAACV,IAAI,IAAIiD,KAAK,GAAG,CAAC;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC,GANOsB,KAAK;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPhC,OAAA,CAACf,IAAI;UAACqD,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACvBnB,OAAA,CAACnB,GAAG;YAACmC,EAAE,EAAE;cAAEyB,QAAQ,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAG,CAAE;YAAAvB,QAAA,gBACvCnB,OAAA,CAACjB,UAAU;cAAC4C,OAAO,EAAC,IAAI;cAAC8B,YAAY;cAACzC,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAI,CAAE;cAAAT,QAAA,EAC3DJ,WAAW,CAACV;YAAI;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAGbhC,OAAA,CAACnB,GAAG;cAACmC,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEQ,GAAG,EAAE,CAAC;gBAAEU,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBAChEnB,OAAA,CAACV,MAAM;gBAACoE,KAAK,EAAE3C,WAAW,CAACP,MAAO;gBAACmD,QAAQ;gBAACC,SAAS,EAAE;cAAI;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DhC,OAAA,CAACjB,UAAU;gBAAC4C,OAAO,EAAC,OAAO;gBAACQ,KAAK,EAAC,gBAAgB;gBAAAhB,QAAA,GAAC,GAChD,EAACJ,WAAW,CAACN,OAAO,EAAC,kCACxB;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNhC,OAAA,CAACnB,GAAG;cAACmC,EAAE,EAAE;gBAAE2B,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBACjBnB,OAAA,CAACnB,GAAG;gBAACmC,EAAE,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEQ,GAAG,EAAE;gBAAE,CAAE;gBAAAd,QAAA,gBACzDnB,OAAA,CAACjB,UAAU;kBAAC4C,OAAO,EAAC,IAAI;kBAACQ,KAAK,EAAC,SAAS;kBAACnB,EAAE,EAAE;oBAAEY,UAAU,EAAE;kBAAI,CAAE;kBAAAT,QAAA,GAC9DJ,WAAW,CAACT,KAAK,EAAC,gBACrB;gBAAA;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EACZjB,WAAW,CAACR,aAAa,GAAGQ,WAAW,CAACT,KAAK,iBAC5CN,OAAA,CAACjB,UAAU;kBACT4C,OAAO,EAAC,IAAI;kBACZX,EAAE,EAAE;oBACF6C,cAAc,EAAE,cAAc;oBAC9B1B,KAAK,EAAE;kBACT,CAAE;kBAAAhB,QAAA,GAEDJ,WAAW,CAACR,aAAa,EAAC,gBAC7B;gBAAA;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNhC,OAAA,CAACjB,UAAU;gBAAC4C,OAAO,EAAC,OAAO;gBAACQ,KAAK,EAAC,cAAc;gBAACnB,EAAE,EAAE;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAT,QAAA,GAAC,qBACpE,EAACJ,WAAW,CAACR,aAAa,GAAGQ,WAAW,CAACT,KAAK,EAAC,gBACrD;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNhC,OAAA,CAACnB,GAAG;cAACmC,EAAE,EAAE;gBAAE2B,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,eACjBnB,OAAA,CAACX,IAAI;gBACH4D,KAAK,EAAElC,WAAW,CAACF,OAAO,GAAG,iBAAiB,GAAG,WAAY;gBAC7DsB,KAAK,EAAEpB,WAAW,CAACF,OAAO,GAAG,SAAS,GAAG,OAAQ;gBACjDc,OAAO,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNhC,OAAA,CAACjB,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACX,EAAE,EAAE;gBAAE2B,EAAE,EAAE,CAAC;gBAAEmB,UAAU,EAAE;cAAI,CAAE;cAAA3C,QAAA,EACxDJ,WAAW,CAACJ;YAAW;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGbhC,OAAA,CAACnB,GAAG;cAACmC,EAAE,EAAE;gBAAE2B,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBACjBnB,OAAA,CAACjB,UAAU;gBAAC4C,OAAO,EAAC,IAAI;gBAAC8B,YAAY;gBAACzC,EAAE,EAAE;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAT,QAAA,EAAC;cAE/D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhC,OAAA,CAACnB,GAAG;gBAACmC,EAAE,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEuC,aAAa,EAAE,QAAQ;kBAAE9B,GAAG,EAAE;gBAAE,CAAE;gBAAAd,QAAA,EAC3DJ,WAAW,CAACH,QAAQ,CAACyC,GAAG,CAAC,CAACW,OAAO,EAAEV,KAAK,kBACvCtD,OAAA,CAACjB,UAAU;kBAAa4C,OAAO,EAAC,OAAO;kBAACX,EAAE,EAAE;oBAAEQ,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAN,QAAA,gBACpFnB,OAAA,CAACnB,GAAG;oBAAC+D,SAAS,EAAC,MAAM;oBAAC5B,EAAE,EAAE;sBAAEmB,KAAK,EAAE,cAAc;sBAAE8B,EAAE,EAAE;oBAAE,CAAE;oBAAA9C,QAAA,EAAC;kBAAC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EAClEgC,OAAO;gBAAA,GAFOV,KAAK;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhC,OAAA,CAACnB,GAAG;cAACmC,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAES,GAAG,EAAE,CAAC;gBAAEU,EAAE,EAAE;cAAE,CAAE;cAAAxB,QAAA,gBAC1CnB,OAAA,CAAChB,MAAM;gBACL2C,OAAO,EAAC,WAAW;gBACnBuC,IAAI,EAAC,OAAO;gBACZC,SAAS,eAAEnE,OAAA,CAACP,YAAY;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BhB,EAAE,EAAE;kBACFoD,IAAI,EAAE,CAAC;kBACP9C,EAAE,EAAE,GAAG;kBACP+C,QAAQ,EAAE,QAAQ;kBAClBzC,UAAU,EAAE;gBACd,CAAE;gBACF0C,QAAQ,EAAE,CAACvD,WAAW,CAACF,OAAQ;gBAAAM,QAAA,EAChC;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThC,OAAA,CAACT,UAAU;gBAAC2E,IAAI,EAAC,OAAO;gBAAC/B,KAAK,EAAC,SAAS;gBAAAhB,QAAA,eACtCnB,OAAA,CAACN,QAAQ;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACbhC,OAAA,CAACT,UAAU;gBAAC2E,IAAI,EAAC,OAAO;gBAAC/B,KAAK,EAAC,SAAS;gBAAAhB,QAAA,eACtCnB,OAAA,CAACL,KAAK;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNhC,OAAA,CAACnB,GAAG;cAACmC,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAES,GAAG,EAAE,CAAC;gBAAEP,cAAc,EAAE,QAAQ;gBAAE6C,EAAE,EAAE,CAAC;gBAAEC,SAAS,EAAE,WAAW;gBAAEnD,WAAW,EAAE;cAAU,CAAE;cAAAF,QAAA,gBACpHnB,OAAA,CAACnB,GAAG;gBAACmC,EAAE,EAAE;kBAAEyD,SAAS,EAAE;gBAAS,CAAE;gBAAAtD,QAAA,gBAC/BnB,OAAA,CAACJ,aAAa;kBAACuC,KAAK,EAAC,SAAS;kBAACnB,EAAE,EAAE;oBAAEqD,QAAQ,EAAE,MAAM;oBAAE1B,EAAE,EAAE;kBAAE;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEhC,OAAA,CAACjB,UAAU;kBAAC4C,OAAO,EAAC,SAAS;kBAACH,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNhC,OAAA,CAACnB,GAAG;gBAACmC,EAAE,EAAE;kBAAEyD,SAAS,EAAE;gBAAS,CAAE;gBAAAtD,QAAA,gBAC/BnB,OAAA,CAACH,QAAQ;kBAACsC,KAAK,EAAC,SAAS;kBAACnB,EAAE,EAAE;oBAAEqD,QAAQ,EAAE,MAAM;oBAAE1B,EAAE,EAAE;kBAAE;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7DhC,OAAA,CAACjB,UAAU;kBAAC4C,OAAO,EAAC,SAAS;kBAACH,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNhC,OAAA,CAACnB,GAAG;gBAACmC,EAAE,EAAE;kBAAEyD,SAAS,EAAE;gBAAS,CAAE;gBAAAtD,QAAA,gBAC/BnB,OAAA,CAACF,OAAO;kBAACqC,KAAK,EAAC,SAAS;kBAACnB,EAAE,EAAE;oBAAEqD,QAAQ,EAAE,MAAM;oBAAE1B,EAAE,EAAE;kBAAE;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DhC,OAAA,CAACjB,UAAU;kBAAC4C,OAAO,EAAC,SAAS;kBAACH,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGZhC,OAAA,CAACnB,GAAG;MAACmC,EAAE,EAAE;QAAEC,OAAO,EAAE,cAAc;QAAEkB,KAAK,EAAE,OAAO;QAAEb,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eAC1DnB,OAAA,CAAClB,SAAS;QAACyC,QAAQ,EAAC,IAAI;QAAAJ,QAAA,gBACtBnB,OAAA,CAACjB,UAAU;UAAC4C,OAAO,EAAC,IAAI;UAAC8C,SAAS,EAAC,QAAQ;UAAChB,YAAY;UAACzC,EAAE,EAAE;YAAEY,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAAC;QAElF;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhC,OAAA,CAACjB,UAAU;UAAC4C,OAAO,EAAC,IAAI;UAAC8C,SAAS,EAAC,QAAQ;UAACzD,EAAE,EAAE;YAAE2B,EAAE,EAAE,CAAC;YAAE+B,OAAO,EAAE;UAAI,CAAE;UAAAvD,QAAA,EAAC;QAEzE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhC,OAAA,CAACd,IAAI;UAAC8B,EAAE,EAAE;YAAE2D,CAAC,EAAE,CAAC;YAAEpD,QAAQ,EAAE,GAAG;YAAEqD,EAAE,EAAE;UAAO,CAAE;UAAAzD,QAAA,eAC5CnB,OAAA,CAACnB,GAAG;YAAC+D,SAAS,EAAC,MAAM;YAAC5B,EAAE,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEuC,aAAa,EAAE,QAAQ;cAAE9B,GAAG,EAAE;YAAE,CAAE;YAAAd,QAAA,gBAC7EnB,OAAA,CAACnB,GAAG;cAAAsC,QAAA,gBACFnB,OAAA,CAACjB,UAAU;gBAAC4C,OAAO,EAAC,OAAO;gBAAC8B,YAAY;gBAACzC,EAAE,EAAE;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAT,QAAA,EAAC;cAElE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhC,OAAA,CAACnB,GAAG;gBACFmC,EAAE,EAAE;kBACF6D,MAAM,EAAE,WAAW;kBACnBxD,WAAW,EAAE,UAAU;kBACvByD,YAAY,EAAE,CAAC;kBACfH,CAAC,EAAE,GAAG;kBACN1D,OAAO,EAAE;gBACX,CAAE;gBAAAE,QAAA,eAEFnB,OAAA,CAACjB,UAAU;kBAAC4C,OAAO,EAAC,OAAO;kBAACQ,KAAK,EAAC,gBAAgB;kBAAAhB,QAAA,EAAC;gBAEnD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhC,OAAA,CAACnB,GAAG;cAAAsC,QAAA,gBACFnB,OAAA,CAACjB,UAAU;gBAAC4C,OAAO,EAAC,OAAO;gBAAC8B,YAAY;gBAACzC,EAAE,EAAE;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAT,QAAA,EAAC;cAElE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhC,OAAA,CAACnB,GAAG;gBACFmC,EAAE,EAAE;kBACF6D,MAAM,EAAE,WAAW;kBACnBxD,WAAW,EAAE,UAAU;kBACvByD,YAAY,EAAE,CAAC;kBACfH,CAAC,EAAE,GAAG;kBACN1D,OAAO,EAAE;gBACX,CAAE;gBAAAE,QAAA,eAEFnB,OAAA,CAACjB,UAAU;kBAAC4C,OAAO,EAAC,OAAO;kBAACQ,KAAK,EAAC,gBAAgB;kBAAAhB,QAAA,EAAC;gBAEnD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhC,OAAA,CAACnB,GAAG;cAAAsC,QAAA,gBACFnB,OAAA,CAACjB,UAAU;gBAAC4C,OAAO,EAAC,OAAO;gBAAC8B,YAAY;gBAACzC,EAAE,EAAE;kBAAEY,UAAU,EAAE;gBAAI,CAAE;gBAAAT,QAAA,EAAC;cAElE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhC,OAAA,CAACnB,GAAG;gBACFmC,EAAE,EAAE;kBACF6D,MAAM,EAAE,WAAW;kBACnBxD,WAAW,EAAE,UAAU;kBACvByD,YAAY,EAAE,CAAC;kBACfH,CAAC,EAAE,GAAG;kBACN1D,OAAO,EAAE,SAAS;kBAClBC,SAAS,EAAE;gBACb,CAAE;gBAAAC,QAAA,eAEFnB,OAAA,CAACjB,UAAU;kBAAC4C,OAAO,EAAC,OAAO;kBAACQ,KAAK,EAAC,gBAAgB;kBAAAhB,QAAA,EAAC;gBAEnD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhC,OAAA,CAAChB,MAAM;cACL2C,OAAO,EAAC,WAAW;cACnBuC,IAAI,EAAC,OAAO;cACZlD,EAAE,EAAE;gBACFC,OAAO,EAAE,cAAc;gBACvBK,EAAE,EAAE,CAAC;gBACL+C,QAAQ,EAAE,QAAQ;gBAClBzC,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE;kBACTX,OAAO,EAAE;gBACX;cACF,CAAE;cAAAE,QAAA,GACH,sDACa,EAACJ,WAAW,CAACT,KAAK,EAAC,gBACjC;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEThC,OAAA,CAACjB,UAAU;cAAC4C,OAAO,EAAC,SAAS;cAAC8C,SAAS,EAAC,QAAQ;cAACtC,KAAK,EAAC,gBAAgB;cAAAhB,QAAA,EAAC;YAExE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC+C,EAAA,GAxSI9E,gBAAgB;AA0StB,eAAeA,gBAAgB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}