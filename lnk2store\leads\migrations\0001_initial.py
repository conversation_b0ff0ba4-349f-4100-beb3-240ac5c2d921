# Generated by Django 5.2.4 on 2025-07-10 18:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Lead',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone_number', models.CharField(max_length=20)),
                ('message', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('new', 'جديد'), ('contacted', 'تم التواصل'), ('converted', 'تم التحويل'), ('rejected', 'مرفوض')], default='new', max_length=20)),
                ('deducted', models.BooleanField(default=False)),
                ('whatsapp_sent', models.BooleanField(default=False)),
                ('whatsapp_sent_at', models.DateTimeField(blank=True, null=True)),
                ('whatsapp_message_id', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='products.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
