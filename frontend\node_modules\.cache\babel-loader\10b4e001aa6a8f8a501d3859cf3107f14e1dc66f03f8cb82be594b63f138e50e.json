{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\ProductCard.js\";\nimport React from 'react';\nimport { Card, CardMedia, CardContent, CardActions, Typography, Button, Chip, Box, IconButton } from '@mui/material';\nimport { Edit, Visibility, Star } from '@mui/icons-material';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product,\n  showActions = true,\n  viewMode = 'grid'\n}) => {\n  var _product$description2;\n  const defaultImage = '/placeholder-product.jpg';\n\n  // دالة تنسيق العملة\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-EG', {\n      style: 'currency',\n      currency: 'EGP',\n      minimumFractionDigits: 2\n    }).format(amount);\n  };\n  const hasDiscount = product.old_price && product.old_price > product.price;\n  const discountPercentage = hasDiscount ? Math.round((product.old_price - product.price) / product.old_price * 100) : 0;\n  if (viewMode === 'list') {\n    var _product$description;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        display: 'flex',\n        height: 200,\n        transition: 'all 0.3s ease',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: 4\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n        component: \"img\",\n        sx: {\n          width: 200,\n          objectFit: 'cover'\n        },\n        image: product.image || defaultImage,\n        alt: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), product.is_featured && /*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 25\n              }, this),\n              label: \"\\u0645\\u0645\\u064A\\u0632\",\n              color: \"warning\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 2\n            },\n            children: ((_product$description = product.description) === null || _product$description === void 0 ? void 0 : _product$description.length) > 150 ? `${product.description.substring(0, 150)}...` : product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [hasDiscount && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  textDecoration: 'line-through',\n                  color: 'text.secondary',\n                  mr: 1\n                },\n                children: formatCurrency(product.old_price)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary\",\n                component: \"span\",\n                children: formatCurrency(product.price)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this), hasDiscount && /*#__PURE__*/_jsxDEV(Chip, {\n                label: `-${discountPercentage}%`,\n                color: \"error\",\n                size: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: product.status === 'active' ? 'نشط' : 'غير نشط',\n                color: product.status === 'active' ? 'success' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), showActions && /*#__PURE__*/_jsxDEV(CardActions, {\n          sx: {\n            justifyContent: 'flex-end'\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            component: Link,\n            to: `/products/${product.id}`,\n            color: \"primary\",\n            children: /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            component: Link,\n            to: `/products/${product.id}/edit`,\n            color: \"primary\",\n            children: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      position: 'relative',\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        transform: 'translateY(-4px)',\n        boxShadow: 6\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 8,\n        right: 8,\n        zIndex: 1\n      },\n      children: [product.is_featured && /*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 19\n        }, this),\n        label: \"\\u0645\\u0645\\u064A\\u0632\",\n        color: \"warning\",\n        size: \"small\",\n        sx: {\n          mb: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this), hasDiscount && /*#__PURE__*/_jsxDEV(Chip, {\n        label: `-${discountPercentage}%`,\n        color: \"error\",\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardMedia, {\n      component: \"img\",\n      height: \"200\",\n      image: product.image || defaultImage,\n      alt: product.name,\n      sx: {\n        objectFit: 'cover'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        flexGrow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        gutterBottom: true,\n        variant: \"h6\",\n        component: \"div\",\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: ((_product$description2 = product.description) === null || _product$description2 === void 0 ? void 0 : _product$description2.length) > 100 ? `${product.description.substring(0, 100)}...` : product.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [hasDiscount && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            textDecoration: 'line-through',\n            color: 'text.secondary'\n          },\n          children: formatCurrency(product.old_price)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"primary\",\n          children: formatCurrency(product.price)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), product.colors && product.colors.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 0.5,\n          mb: 2\n        },\n        children: [product.colors.slice(0, 4).map((color, index) => {\n          var _color$color;\n          return /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 20,\n              height: 20,\n              borderRadius: '50%',\n              backgroundColor: ((_color$color = color.color) === null || _color$color === void 0 ? void 0 : _color$color.toLowerCase()) || '#ccc',\n              border: '2px solid',\n              borderColor: 'divider'\n            },\n            title: color.color\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this);\n        }), product.colors.length > 4 && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [\"+\", product.colors.length - 4]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: product.status === 'active' ? 'نشط' : 'غير نشط',\n          color: product.status === 'active' ? 'success' : 'default',\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), product.view_count > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `${product.view_count} مشاهدة`,\n          size: \"small\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this), product.lead_count > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `${product.lead_count} طلب`,\n          size: \"small\",\n          variant: \"outlined\",\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), showActions && /*#__PURE__*/_jsxDEV(CardActions, {\n      sx: {\n        justifyContent: 'space-between',\n        px: 2,\n        pb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        component: Link,\n        to: `/products/${product.id}`,\n        startIcon: /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 24\n        }, this),\n        variant: \"outlined\",\n        children: \"\\u0639\\u0631\\u0636\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        component: Link,\n        to: `/products/${product.id}/edit`,\n        startIcon: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 24\n        }, this),\n        variant: \"contained\",\n        children: \"\\u062A\\u0639\\u062F\\u064A\\u0644\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Typography", "<PERSON><PERSON>", "Chip", "Box", "IconButton", "Edit", "Visibility", "Star", "Link", "jsxDEV", "_jsxDEV", "ProductCard", "product", "showActions", "viewMode", "_product$description2", "defaultImage", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "hasDiscount", "old_price", "price", "discountPercentage", "Math", "round", "_product$description", "sx", "display", "height", "transition", "transform", "boxShadow", "children", "component", "width", "objectFit", "image", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "flex", "justifyContent", "alignItems", "mb", "variant", "is_featured", "icon", "label", "color", "size", "description", "length", "substring", "textDecoration", "mr", "ml", "gap", "status", "to", "id", "position", "top", "right", "zIndex", "flexGrow", "gutterBottom", "colors", "slice", "map", "index", "_color$color", "borderRadius", "backgroundColor", "toLowerCase", "border", "borderColor", "title", "flexWrap", "view_count", "lead_count", "px", "pb", "startIcon", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/ProductCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Card,\n  CardMedia,\n  CardContent,\n  CardActions,\n  Typography,\n  Button,\n  Chip,\n  Box,\n  IconButton\n} from '@mui/material';\nimport { \n  Edit, \n  Visibility, \n  Star\n} from '@mui/icons-material';\nimport { Link } from 'react-router-dom';\n\nconst ProductCard = ({ product, showActions = true, viewMode = 'grid' }) => {\n  const defaultImage = '/placeholder-product.jpg';\n\n  // دالة تنسيق العملة\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-EG', {\n      style: 'currency',\n      currency: 'EGP',\n      minimumFractionDigits: 2\n    }).format(amount);\n  };\n\n  const hasDiscount = product.old_price && product.old_price > product.price;\n  const discountPercentage = hasDiscount \n    ? Math.round(((product.old_price - product.price) / product.old_price) * 100)\n    : 0;\n\n  if (viewMode === 'list') {\n    return (\n      <Card sx={{ \n        display: 'flex', \n        height: 200,\n        transition: 'all 0.3s ease',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: 4\n        }\n      }}>\n        <CardMedia\n          component=\"img\"\n          sx={{ width: 200, objectFit: 'cover' }}\n          image={product.image || defaultImage}\n          alt={product.name}\n        />\n        \n        <Box sx={{ display: 'flex', flexDirection: 'column', flex: 1 }}>\n          <CardContent sx={{ flex: 1 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>\n              <Typography variant=\"h6\" component=\"div\">\n                {product.name}\n              </Typography>\n              \n              {product.is_featured && (\n                <Chip \n                  icon={<Star />}\n                  label=\"مميز\" \n                  color=\"warning\"\n                  size=\"small\"\n                />\n              )}\n            </Box>\n            \n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              {product.description?.length > 150 \n                ? `${product.description.substring(0, 150)}...` \n                : product.description\n              }\n            </Typography>\n            \n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n              <Box>\n                {hasDiscount && (\n                  <Typography \n                    variant=\"body2\" \n                    sx={{ \n                      textDecoration: 'line-through', \n                      color: 'text.secondary',\n                      mr: 1\n                    }}\n                  >\n                    {formatCurrency(product.old_price)}\n                  </Typography>\n                )}\n                <Typography variant=\"h6\" color=\"primary\" component=\"span\">\n                  {formatCurrency(product.price)}\n                </Typography>\n                {hasDiscount && (\n                  <Chip \n                    label={`-${discountPercentage}%`}\n                    color=\"error\"\n                    size=\"small\"\n                    sx={{ ml: 1 }}\n                  />\n                )}\n              </Box>\n              \n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Chip \n                  label={product.status === 'active' ? 'نشط' : 'غير نشط'} \n                  color={product.status === 'active' ? 'success' : 'default'}\n                  size=\"small\"\n                />\n              </Box>\n            </Box>\n          </CardContent>\n          \n          {showActions && (\n            <CardActions sx={{ justifyContent: 'flex-end' }}>\n              <IconButton \n                component={Link} \n                to={`/products/${product.id}`}\n                color=\"primary\"\n              >\n                <Visibility />\n              </IconButton>\n              <IconButton \n                component={Link} \n                to={`/products/${product.id}/edit`}\n                color=\"primary\"\n              >\n                <Edit />\n              </IconButton>\n            </CardActions>\n          )}\n        </Box>\n      </Card>\n    );\n  }\n\n  return (\n    <Card sx={{ \n      height: '100%', \n      display: 'flex', \n      flexDirection: 'column',\n      position: 'relative',\n      transition: 'all 0.3s ease',\n      '&:hover': {\n        transform: 'translateY(-4px)',\n        boxShadow: 6\n      }\n    }}>\n      <Box sx={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}>\n        {product.is_featured && (\n          <Chip \n            icon={<Star />}\n            label=\"مميز\" \n            color=\"warning\"\n            size=\"small\"\n            sx={{ mb: 0.5 }}\n          />\n        )}\n        {hasDiscount && (\n          <Chip \n            label={`-${discountPercentage}%`}\n            color=\"error\"\n            size=\"small\"\n          />\n        )}\n      </Box>\n      \n      <CardMedia\n        component=\"img\"\n        height=\"200\"\n        image={product.image || defaultImage}\n        alt={product.name}\n        sx={{ objectFit: 'cover' }}\n      />\n      \n      <CardContent sx={{ flexGrow: 1 }}>\n        <Typography gutterBottom variant=\"h6\" component=\"div\">\n          {product.name}\n        </Typography>\n        \n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          {product.description?.length > 100 \n            ? `${product.description.substring(0, 100)}...` \n            : product.description\n          }\n        </Typography>\n        \n        <Box sx={{ mb: 2 }}>\n          {hasDiscount && (\n            <Typography \n              variant=\"body2\" \n              sx={{ \n                textDecoration: 'line-through', \n                color: 'text.secondary'\n              }}\n            >\n              {formatCurrency(product.old_price)}\n            </Typography>\n          )}\n          <Typography variant=\"h6\" color=\"primary\">\n            {formatCurrency(product.price)}\n          </Typography>\n        </Box>\n        \n        {product.colors && product.colors.length > 0 && (\n          <Box sx={{ display: 'flex', gap: 0.5, mb: 2 }}>\n            {product.colors.slice(0, 4).map((color, index) => (\n              <Box\n                key={index}\n                sx={{\n                  width: 20,\n                  height: 20,\n                  borderRadius: '50%',\n                  backgroundColor: color.color?.toLowerCase() || '#ccc',\n                  border: '2px solid',\n                  borderColor: 'divider'\n                }}\n                title={color.color}\n              />\n            ))}\n            {product.colors.length > 4 && (\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                +{product.colors.length - 4}\n              </Typography>\n            )}\n          </Box>\n        )}\n        \n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          <Chip \n            label={product.status === 'active' ? 'نشط' : 'غير نشط'} \n            color={product.status === 'active' ? 'success' : 'default'}\n            size=\"small\"\n          />\n          \n          {product.view_count > 0 && (\n            <Chip \n              label={`${product.view_count} مشاهدة`}\n              size=\"small\"\n              variant=\"outlined\"\n            />\n          )}\n          \n          {product.lead_count > 0 && (\n            <Chip \n              label={`${product.lead_count} طلب`}\n              size=\"small\"\n              variant=\"outlined\"\n              color=\"primary\"\n            />\n          )}\n        </Box>\n      </CardContent>\n      \n      {showActions && (\n        <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>\n          <Button \n            size=\"small\" \n            component={Link} \n            to={`/products/${product.id}`}\n            startIcon={<Visibility />}\n            variant=\"outlined\"\n          >\n            عرض\n          </Button>\n          \n          <Button \n            size=\"small\" \n            component={Link} \n            to={`/products/${product.id}/edit`}\n            startIcon={<Edit />}\n            variant=\"contained\"\n          >\n            تعديل\n          </Button>\n        </CardActions>\n      )}\n    </Card>\n  );\n};\n\nexport default ProductCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,UAAU,QACL,eAAe;AACtB,SACEC,IAAI,EACJC,UAAU,EACVC,IAAI,QACC,qBAAqB;AAC5B,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,WAAW,GAAG,IAAI;EAAEC,QAAQ,GAAG;AAAO,CAAC,KAAK;EAAA,IAAAC,qBAAA;EAC1E,MAAMC,YAAY,GAAG,0BAA0B;;EAE/C;EACA,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACN,MAAM,CAAC;EACnB,CAAC;EAED,MAAMO,WAAW,GAAGb,OAAO,CAACc,SAAS,IAAId,OAAO,CAACc,SAAS,GAAGd,OAAO,CAACe,KAAK;EAC1E,MAAMC,kBAAkB,GAAGH,WAAW,GAClCI,IAAI,CAACC,KAAK,CAAE,CAAClB,OAAO,CAACc,SAAS,GAAGd,OAAO,CAACe,KAAK,IAAIf,OAAO,CAACc,SAAS,GAAI,GAAG,CAAC,GAC3E,CAAC;EAEL,IAAIZ,QAAQ,KAAK,MAAM,EAAE;IAAA,IAAAiB,oBAAA;IACvB,oBACErB,OAAA,CAACd,IAAI;MAACoC,EAAE,EAAE;QACRC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,eAAe;QAC3B,SAAS,EAAE;UACTC,SAAS,EAAE,kBAAkB;UAC7BC,SAAS,EAAE;QACb;MACF,CAAE;MAAAC,QAAA,gBACA5B,OAAA,CAACb,SAAS;QACR0C,SAAS,EAAC,KAAK;QACfP,EAAE,EAAE;UAAEQ,KAAK,EAAE,GAAG;UAAEC,SAAS,EAAE;QAAQ,CAAE;QACvCC,KAAK,EAAE9B,OAAO,CAAC8B,KAAK,IAAI1B,YAAa;QACrC2B,GAAG,EAAE/B,OAAO,CAACgC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAEFtC,OAAA,CAACP,GAAG;QAAC6B,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEgB,aAAa,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAZ,QAAA,gBAC7D5B,OAAA,CAACZ,WAAW;UAACkC,EAAE,EAAE;YAAEkB,IAAI,EAAE;UAAE,CAAE;UAAAZ,QAAA,gBAC3B5B,OAAA,CAACP,GAAG;YAAC6B,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEkB,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,YAAY;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAf,QAAA,gBAC7F5B,OAAA,CAACV,UAAU;cAACsD,OAAO,EAAC,IAAI;cAACf,SAAS,EAAC,KAAK;cAAAD,QAAA,EACrC1B,OAAO,CAACgC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEZpC,OAAO,CAAC2C,WAAW,iBAClB7C,OAAA,CAACR,IAAI;cACHsD,IAAI,eAAE9C,OAAA,CAACH,IAAI;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACfS,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENtC,OAAA,CAACV,UAAU;YAACsD,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAAC1B,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE,CAAE;YAAAf,QAAA,EAC9D,EAAAP,oBAAA,GAAAnB,OAAO,CAACgD,WAAW,cAAA7B,oBAAA,uBAAnBA,oBAAA,CAAqB8B,MAAM,IAAG,GAAG,GAC9B,GAAGjD,OAAO,CAACgD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAC7ClD,OAAO,CAACgD;UAAW;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CAAC,eAEbtC,OAAA,CAACP,GAAG;YAAC6B,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEkB,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAd,QAAA,gBAClF5B,OAAA,CAACP,GAAG;cAAAmC,QAAA,GACDb,WAAW,iBACVf,OAAA,CAACV,UAAU;gBACTsD,OAAO,EAAC,OAAO;gBACftB,EAAE,EAAE;kBACF+B,cAAc,EAAE,cAAc;kBAC9BL,KAAK,EAAE,gBAAgB;kBACvBM,EAAE,EAAE;gBACN,CAAE;gBAAA1B,QAAA,EAEDrB,cAAc,CAACL,OAAO,CAACc,SAAS;cAAC;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CACb,eACDtC,OAAA,CAACV,UAAU;gBAACsD,OAAO,EAAC,IAAI;gBAACI,KAAK,EAAC,SAAS;gBAACnB,SAAS,EAAC,MAAM;gBAAAD,QAAA,EACtDrB,cAAc,CAACL,OAAO,CAACe,KAAK;cAAC;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EACZvB,WAAW,iBACVf,OAAA,CAACR,IAAI;gBACHuD,KAAK,EAAE,IAAI7B,kBAAkB,GAAI;gBACjC8B,KAAK,EAAC,OAAO;gBACbC,IAAI,EAAC,OAAO;gBACZ3B,EAAE,EAAE;kBAAEiC,EAAE,EAAE;gBAAE;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENtC,OAAA,CAACP,GAAG;cAAC6B,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEiC,GAAG,EAAE;cAAE,CAAE;cAAA5B,QAAA,eACnC5B,OAAA,CAACR,IAAI;gBACHuD,KAAK,EAAE7C,OAAO,CAACuD,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG,SAAU;gBACvDT,KAAK,EAAE9C,OAAO,CAACuD,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;gBAC3DR,IAAI,EAAC;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,EAEbnC,WAAW,iBACVH,OAAA,CAACX,WAAW;UAACiC,EAAE,EAAE;YAAEmB,cAAc,EAAE;UAAW,CAAE;UAAAb,QAAA,gBAC9C5B,OAAA,CAACN,UAAU;YACTmC,SAAS,EAAE/B,IAAK;YAChB4D,EAAE,EAAE,aAAaxD,OAAO,CAACyD,EAAE,EAAG;YAC9BX,KAAK,EAAC,SAAS;YAAApB,QAAA,eAEf5B,OAAA,CAACJ,UAAU;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACbtC,OAAA,CAACN,UAAU;YACTmC,SAAS,EAAE/B,IAAK;YAChB4D,EAAE,EAAE,aAAaxD,OAAO,CAACyD,EAAE,OAAQ;YACnCX,KAAK,EAAC,SAAS;YAAApB,QAAA,eAEf5B,OAAA,CAACL,IAAI;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACd;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACEtC,OAAA,CAACd,IAAI;IAACoC,EAAE,EAAE;MACRE,MAAM,EAAE,MAAM;MACdD,OAAO,EAAE,MAAM;MACfgB,aAAa,EAAE,QAAQ;MACvBqB,QAAQ,EAAE,UAAU;MACpBnC,UAAU,EAAE,eAAe;MAC3B,SAAS,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE;MACb;IACF,CAAE;IAAAC,QAAA,gBACA5B,OAAA,CAACP,GAAG;MAAC6B,EAAE,EAAE;QAAEsC,QAAQ,EAAE,UAAU;QAAEC,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAE;MAAAnC,QAAA,GAC5D1B,OAAO,CAAC2C,WAAW,iBAClB7C,OAAA,CAACR,IAAI;QACHsD,IAAI,eAAE9C,OAAA,CAACH,IAAI;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACfS,KAAK,EAAC,0BAAM;QACZC,KAAK,EAAC,SAAS;QACfC,IAAI,EAAC,OAAO;QACZ3B,EAAE,EAAE;UAAEqB,EAAE,EAAE;QAAI;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF,EACAvB,WAAW,iBACVf,OAAA,CAACR,IAAI;QACHuD,KAAK,EAAE,IAAI7B,kBAAkB,GAAI;QACjC8B,KAAK,EAAC,OAAO;QACbC,IAAI,EAAC;MAAO;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENtC,OAAA,CAACb,SAAS;MACR0C,SAAS,EAAC,KAAK;MACfL,MAAM,EAAC,KAAK;MACZQ,KAAK,EAAE9B,OAAO,CAAC8B,KAAK,IAAI1B,YAAa;MACrC2B,GAAG,EAAE/B,OAAO,CAACgC,IAAK;MAClBZ,EAAE,EAAE;QAAES,SAAS,EAAE;MAAQ;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFtC,OAAA,CAACZ,WAAW;MAACkC,EAAE,EAAE;QAAE0C,QAAQ,EAAE;MAAE,CAAE;MAAApC,QAAA,gBAC/B5B,OAAA,CAACV,UAAU;QAAC2E,YAAY;QAACrB,OAAO,EAAC,IAAI;QAACf,SAAS,EAAC,KAAK;QAAAD,QAAA,EAClD1B,OAAO,CAACgC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEbtC,OAAA,CAACV,UAAU;QAACsD,OAAO,EAAC,OAAO;QAACI,KAAK,EAAC,gBAAgB;QAAC1B,EAAE,EAAE;UAAEqB,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,EAC9D,EAAAvB,qBAAA,GAAAH,OAAO,CAACgD,WAAW,cAAA7C,qBAAA,uBAAnBA,qBAAA,CAAqB8C,MAAM,IAAG,GAAG,GAC9B,GAAGjD,OAAO,CAACgD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAC7ClD,OAAO,CAACgD;MAAW;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEb,CAAC,eAEbtC,OAAA,CAACP,GAAG;QAAC6B,EAAE,EAAE;UAAEqB,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,GAChBb,WAAW,iBACVf,OAAA,CAACV,UAAU;UACTsD,OAAO,EAAC,OAAO;UACftB,EAAE,EAAE;YACF+B,cAAc,EAAE,cAAc;YAC9BL,KAAK,EAAE;UACT,CAAE;UAAApB,QAAA,EAEDrB,cAAc,CAACL,OAAO,CAACc,SAAS;QAAC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACb,eACDtC,OAAA,CAACV,UAAU;UAACsD,OAAO,EAAC,IAAI;UAACI,KAAK,EAAC,SAAS;UAAApB,QAAA,EACrCrB,cAAc,CAACL,OAAO,CAACe,KAAK;QAAC;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAELpC,OAAO,CAACgE,MAAM,IAAIhE,OAAO,CAACgE,MAAM,CAACf,MAAM,GAAG,CAAC,iBAC1CnD,OAAA,CAACP,GAAG;QAAC6B,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEiC,GAAG,EAAE,GAAG;UAAEb,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,GAC3C1B,OAAO,CAACgE,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACpB,KAAK,EAAEqB,KAAK;UAAA,IAAAC,YAAA;UAAA,oBAC3CtE,OAAA,CAACP,GAAG;YAEF6B,EAAE,EAAE;cACFQ,KAAK,EAAE,EAAE;cACTN,MAAM,EAAE,EAAE;cACV+C,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAE,EAAAF,YAAA,GAAAtB,KAAK,CAACA,KAAK,cAAAsB,YAAA,uBAAXA,YAAA,CAAaG,WAAW,CAAC,CAAC,KAAI,MAAM;cACrDC,MAAM,EAAE,WAAW;cACnBC,WAAW,EAAE;YACf,CAAE;YACFC,KAAK,EAAE5B,KAAK,CAACA;UAAM,GATdqB,KAAK;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUX,CAAC;QAAA,CACH,CAAC,EACDpC,OAAO,CAACgE,MAAM,CAACf,MAAM,GAAG,CAAC,iBACxBnD,OAAA,CAACV,UAAU;UAACsD,OAAO,EAAC,SAAS;UAACI,KAAK,EAAC,gBAAgB;UAAApB,QAAA,GAAC,GAClD,EAAC1B,OAAO,CAACgE,MAAM,CAACf,MAAM,GAAG,CAAC;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAEDtC,OAAA,CAACP,GAAG;QAAC6B,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEiC,GAAG,EAAE,CAAC;UAAEqB,QAAQ,EAAE;QAAO,CAAE;QAAAjD,QAAA,gBACrD5B,OAAA,CAACR,IAAI;UACHuD,KAAK,EAAE7C,OAAO,CAACuD,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG,SAAU;UACvDT,KAAK,EAAE9C,OAAO,CAACuD,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;UAC3DR,IAAI,EAAC;QAAO;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,EAEDpC,OAAO,CAAC4E,UAAU,GAAG,CAAC,iBACrB9E,OAAA,CAACR,IAAI;UACHuD,KAAK,EAAE,GAAG7C,OAAO,CAAC4E,UAAU,SAAU;UACtC7B,IAAI,EAAC,OAAO;UACZL,OAAO,EAAC;QAAU;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACF,EAEApC,OAAO,CAAC6E,UAAU,GAAG,CAAC,iBACrB/E,OAAA,CAACR,IAAI;UACHuD,KAAK,EAAE,GAAG7C,OAAO,CAAC6E,UAAU,MAAO;UACnC9B,IAAI,EAAC,OAAO;UACZL,OAAO,EAAC,UAAU;UAClBI,KAAK,EAAC;QAAS;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,EAEbnC,WAAW,iBACVH,OAAA,CAACX,WAAW;MAACiC,EAAE,EAAE;QAAEmB,cAAc,EAAE,eAAe;QAAEuC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAArD,QAAA,gBACjE5B,OAAA,CAACT,MAAM;QACL0D,IAAI,EAAC,OAAO;QACZpB,SAAS,EAAE/B,IAAK;QAChB4D,EAAE,EAAE,aAAaxD,OAAO,CAACyD,EAAE,EAAG;QAC9BuB,SAAS,eAAElF,OAAA,CAACJ,UAAU;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1BM,OAAO,EAAC,UAAU;QAAAhB,QAAA,EACnB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETtC,OAAA,CAACT,MAAM;QACL0D,IAAI,EAAC,OAAO;QACZpB,SAAS,EAAE/B,IAAK;QAChB4D,EAAE,EAAE,aAAaxD,OAAO,CAACyD,EAAE,OAAQ;QACnCuB,SAAS,eAAElF,OAAA,CAACL,IAAI;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACpBM,OAAO,EAAC,WAAW;QAAAhB,QAAA,EACpB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACd;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAC6C,EAAA,GAtQIlF,WAAW;AAwQjB,eAAeA,WAAW;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}