# Production Environment Configuration
# This file should be customized for your production server

DJANGO_ENVIRONMENT=production
SECRET_KEY=CHANGE_THIS_TO_A_SECURE_SECRET_KEY
DEBUG=False

# Database Configuration
DB_NAME=lnk2store_prod
DB_USER=lnk2store_user
DB_PASSWORD=CHANGE_THIS_PASSWORD
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://127.0.0.1:6379/1

# Celery Configuration
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Server Configuration
SERVER_IP=YOUR_SERVER_IP

# WhatsApp Configuration
WHATSAPP_API_URL=https://api.whatsapp.com/send
WHATSAPP_BUSINESS_PHONE=+966500000000

# Monitoring (Optional)
# SENTRY_DSN=your-sentry-dsn-here
