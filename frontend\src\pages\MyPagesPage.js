import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardActions,
  Grid,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Fab
} from '@mui/material';
import {
  Add,
  MoreVert,
  Edit,
  Visibility,
  Delete,
  Share,
  Publish,
  UnpublishedOutlined,
  Analytics,
  ContentCopy
} from '@mui/icons-material';
import { Link, useNavigate } from 'react-router-dom';
import { useApi, useAsyncOperation } from '../hooks/useApi';

// Mock API for now
const myPagesAPI = {
  getMyPages: () => {
    return Promise.resolve({
      data: [
        {
          id: 1,
          title: 'صفحة منتجاتي',
          slug: 'my-products-page',
          status: 'published',
          template_name: 'عرض منتج احترافي',
          view_count: 245,
          lead_count: 12,
          conversion_rate: 4.9,
          created_at: '2024-01-15T10:30:00Z',
          published_at: '2024-01-16T09:00:00Z'
        },
        {
          id: 2,
          title: 'صفحة خدماتي',
          slug: 'my-services-page',
          status: 'draft',
          template_name: 'صفحة خدمة احترافية',
          view_count: 0,
          lead_count: 0,
          conversion_rate: 0,
          created_at: '2024-01-20T14:15:00Z'
        }
      ]
    });
  },
  deletePage: (id) => Promise.resolve({ success: true }),
  publishPage: (id) => Promise.resolve({ success: true }),
  unpublishPage: (id) => Promise.resolve({ success: true })
};

const MyPagesPage = () => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedPage, setSelectedPage] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { data: pages, loading, error, refetch } = useApi(() => myPagesAPI.getMyPages());
  const { loading: actionLoading, execute } = useAsyncOperation();

  const handleMenuOpen = (event, page) => {
    setAnchorEl(event.currentTarget);
    setSelectedPage(page);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPage(null);
  };

  const handleDelete = async () => {
    if (!selectedPage) return;
    
    const result = await execute(() => myPagesAPI.deletePage(selectedPage.id));
    if (result.success) {
      setDeleteDialogOpen(false);
      handleMenuClose();
      refetch();
    }
  };

  const handlePublish = async () => {
    if (!selectedPage) return;
    
    const result = await execute(() => myPagesAPI.publishPage(selectedPage.id));
    if (result.success) {
      handleMenuClose();
      refetch();
    }
  };

  const handleUnpublish = async () => {
    if (!selectedPage) return;
    
    const result = await execute(() => myPagesAPI.unpublishPage(selectedPage.id));
    if (result.success) {
      handleMenuClose();
      refetch();
    }
  };

  const handleShare = (page) => {
    const url = `${window.location.origin}/page/${page.slug}`;
    if (navigator.share) {
      navigator.share({
        title: page.title,
        url: url,
      });
    } else {
      navigator.clipboard.writeText(url);
      alert('تم نسخ الرابط');
    }
    handleMenuClose();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return 'success';
      case 'draft': return 'default';
      case 'archived': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'published': return 'منشور';
      case 'draft': return 'مسودة';
      case 'archived': return 'مؤرشف';
      default: return status;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4">
          صفحاتي التسويقية
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          component={Link}
          to="/templates"
        >
          إنشاء صفحة جديدة
        </Button>
      </Box>

      {pages?.length === 0 ? (
        <Card sx={{ textAlign: 'center', py: 8 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              لا توجد صفحات تسويقية بعد
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              ابدأ بإنشاء صفحتك التسويقية الأولى لعرض منتجاتك وخدماتك
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              component={Link}
              to="/templates"
            >
              إنشاء صفحة جديدة
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {pages?.map((page) => (
            <Grid item xs={12} md={6} lg={4} key={page.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" component="h3">
                      {page.title}
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, page)}
                    >
                      <MoreVert />
                    </IconButton>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <Chip
                      label={getStatusLabel(page.status)}
                      color={getStatusColor(page.status)}
                      size="small"
                    />
                    <Chip
                      label={page.template_name}
                      variant="outlined"
                      size="small"
                    />
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    الرابط: /page/{page.slug}
                  </Typography>

                  {/* Stats */}
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" color="primary">
                          {page.view_count}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          مشاهدة
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" color="success.main">
                          {page.lead_count}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          طلب
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" color="warning.main">
                          {page.conversion_rate}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          تحويل
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  <Typography variant="caption" color="text.secondary">
                    تم الإنشاء: {formatDate(page.created_at)}
                    {page.published_at && (
                      <> | تم النشر: {formatDate(page.published_at)}</>
                    )}
                  </Typography>
                </CardContent>

                <CardActions>
                  <Button
                    size="small"
                    startIcon={<Edit />}
                    component={Link}
                    to={`/pages/${page.id}/edit`}
                  >
                    تعديل
                  </Button>
                  {page.status === 'published' && (
                    <Button
                      size="small"
                      startIcon={<Visibility />}
                      component={Link}
                      to={`/page/${page.slug}`}
                      target="_blank"
                    >
                      عرض
                    </Button>
                  )}
                  <Button
                    size="small"
                    startIcon={<Analytics />}
                    component={Link}
                    to={`/pages/${page.id}/analytics`}
                  >
                    إحصائيات
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => navigate(`/pages/${selectedPage?.id}/edit`)}>
          <Edit sx={{ mr: 1 }} />
          تعديل
        </MenuItem>
        
        {selectedPage?.status === 'published' ? (
          <MenuItem onClick={handleUnpublish} disabled={actionLoading}>
            <UnpublishedOutlined sx={{ mr: 1 }} />
            إلغاء النشر
          </MenuItem>
        ) : (
          <MenuItem onClick={handlePublish} disabled={actionLoading}>
            <Publish sx={{ mr: 1 }} />
            نشر
          </MenuItem>
        )}
        
        {selectedPage?.status === 'published' && (
          <MenuItem onClick={() => window.open(`/page/${selectedPage.slug}`, '_blank')}>
            <Visibility sx={{ mr: 1 }} />
            عرض الصفحة
          </MenuItem>
        )}
        
        <MenuItem onClick={() => handleShare(selectedPage)}>
          <Share sx={{ mr: 1 }} />
          مشاركة
        </MenuItem>
        
        <MenuItem onClick={() => navigator.clipboard.writeText(`/page/${selectedPage?.slug}`)}>
          <ContentCopy sx={{ mr: 1 }} />
          نسخ الرابط
        </MenuItem>
        
        <MenuItem onClick={() => navigate(`/pages/${selectedPage?.id}/analytics`)}>
          <Analytics sx={{ mr: 1 }} />
          الإحصائيات
        </MenuItem>
        
        <MenuItem 
          onClick={() => setDeleteDialogOpen(true)}
          sx={{ color: 'error.main' }}
        >
          <Delete sx={{ mr: 1 }} />
          حذف
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف الصفحة "{selectedPage?.title}"؟ هذا الإجراء لا يمكن التراجع عنه.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            إلغاء
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={actionLoading}
            startIcon={actionLoading ? <CircularProgress size={20} /> : <Delete />}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        component={Link}
        to="/templates"
      >
        <Add />
      </Fab>
    </Box>
  );
};

export default MyPagesPage;
