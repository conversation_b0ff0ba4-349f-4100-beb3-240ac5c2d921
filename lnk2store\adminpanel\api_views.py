from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import DashboardManager, AdminNotification
from accounts.models import User
from leads.models import Lead
from products.models import Product
from wallet.models import Wallet, Transaction

def is_admin_user(user):
    """Check if user is admin/staff"""
    return user.is_authenticated and (user.is_staff or user.is_superuser)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@user_passes_test(is_admin_user)
def admin_overview(request):
    """Get admin dashboard overview statistics"""
    try:
        stats = DashboardManager.get_overview_stats()
        return Response(stats)
    except Exception as e:
        return Response({
            'error': f'Error fetching overview stats: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@user_passes_test(is_admin_user)
def admin_charts(request):
    """Get chart data for admin dashboard"""
    try:
        days = int(request.GET.get('days', 30))
        chart_data = DashboardManager.get_chart_data(days)
        return Response(chart_data)
    except Exception as e:
        return Response({
            'error': f'Error fetching chart data: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@user_passes_test(is_admin_user)
def admin_users(request):
    """Get users list with pagination and filtering"""
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        search = request.GET.get('search', '')
        filter_type = request.GET.get('filter', 'all')  # all, active, low_balance
        
        users_query = User.objects.select_related('wallet')
        
        # Apply search filter
        if search:
            users_query = users_query.filter(
                username__icontains=search
            ) | users_query.filter(
                email__icontains=search
            ) | users_query.filter(
                first_name__icontains=search
            ) | users_query.filter(
                last_name__icontains=search
            )
        
        # Apply type filter
        if filter_type == 'active':
            week_ago = timezone.now() - timedelta(days=7)
            users_query = users_query.filter(last_login__gte=week_ago)
        elif filter_type == 'low_balance':
            users_query = users_query.filter(
                wallet__balance__lt=50  # Assuming 50 is low balance threshold
            )
        
        # Pagination
        total_count = users_query.count()
        offset = (page - 1) * page_size
        users = users_query[offset:offset + page_size]
        
        users_data = []
        for user in users:
            wallet = getattr(user, 'wallet', None)
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'phone_number': user.phone_number,
                'business_type': user.business_type,
                'date_joined': user.date_joined,
                'last_login': user.last_login,
                'is_active': user.is_active,
                'wallet_balance': float(wallet.balance) if wallet else 0,
                'leads_count': user.lead_set.count(),
            })
        
        return Response({
            'users': users_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })
        
    except Exception as e:
        return Response({
            'error': f'Error fetching users: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@user_passes_test(is_admin_user)
def admin_leads(request):
    """Get leads list with pagination and filtering"""
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        filter_type = request.GET.get('filter', 'all')  # all, pending, processed
        
        leads_query = Lead.objects.select_related('user', 'product')
        
        # Apply filter
        if filter_type == 'pending':
            leads_query = leads_query.filter(deducted=False)
        elif filter_type == 'processed':
            leads_query = leads_query.filter(deducted=True)
        
        # Pagination
        total_count = leads_query.count()
        offset = (page - 1) * page_size
        leads = leads_query[offset:offset + page_size]
        
        leads_data = []
        for lead in leads:
            leads_data.append({
                'id': lead.id,
                'name': lead.name,
                'email': lead.email,
                'phone_number': lead.phone_number,
                'message': lead.message,
                'status': lead.status,
                'deducted': lead.deducted,
                'whatsapp_sent': lead.whatsapp_sent,
                'created_at': lead.created_at,
                'user': {
                    'id': lead.user.id,
                    'username': lead.user.username,
                    'email': lead.user.email,
                },
                'product': {
                    'id': lead.product.id,
                    'name': lead.product.name,
                } if lead.product else None,
            })
        
        return Response({
            'leads': leads_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })
        
    except Exception as e:
        return Response({
            'error': f'Error fetching leads: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@user_passes_test(is_admin_user)
def admin_transactions(request):
    """Get transactions list with pagination"""
    try:
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        
        transactions_query = Transaction.objects.select_related('wallet__user')
        
        # Pagination
        total_count = transactions_query.count()
        offset = (page - 1) * page_size
        transactions = transactions_query[offset:offset + page_size]
        
        transactions_data = []
        for transaction in transactions:
            transactions_data.append({
                'id': transaction.id,
                'amount': float(transaction.amount),
                'transaction_type': transaction.transaction_type,
                'description': transaction.description,
                'timestamp': transaction.timestamp,
                'reference_id': transaction.reference_id,
                'user': {
                    'id': transaction.wallet.user.id,
                    'username': transaction.wallet.user.username,
                    'email': transaction.wallet.user.email,
                },
                'wallet_balance': float(transaction.wallet.balance),
            })
        
        return Response({
            'transactions': transactions_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })
        
    except Exception as e:
        return Response({
            'error': f'Error fetching transactions: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@user_passes_test(is_admin_user)
def admin_notifications(request):
    """Get admin notifications"""
    try:
        notifications = AdminNotification.objects.all()[:20]
        
        notifications_data = []
        for notification in notifications:
            notifications_data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'notification_type': notification.notification_type,
                'priority': notification.priority,
                'is_read': notification.is_read,
                'created_at': notification.created_at,
            })
        
        return Response({
            'notifications': notifications_data,
            'unread_count': AdminNotification.objects.filter(is_read=False).count()
        })
        
    except Exception as e:
        return Response({
            'error': f'Error fetching notifications: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@user_passes_test(is_admin_user)
def mark_notification_read(request, notification_id):
    """Mark notification as read"""
    try:
        notification = AdminNotification.objects.get(id=notification_id)
        notification.is_read = True
        notification.save()
        
        return Response({'message': 'Notification marked as read'})
        
    except AdminNotification.DoesNotExist:
        return Response({
            'error': 'Notification not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': f'Error marking notification: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
