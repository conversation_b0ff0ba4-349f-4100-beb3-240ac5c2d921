from rest_framework import serializers
from .models import Product, ProductImage, ProductColor, ProductSize, ProductCategory


class ProductCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductCategory
        fields = ('id', 'name', 'description', 'icon', 'is_active')


class ProductImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ('id', 'image')


class ProductColorSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductColor
        fields = ('id', 'color')


class ProductSizeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductSize
        fields = ('id', 'size')


class ProductListSerializer(serializers.ModelSerializer):
    """Serializer for product list view"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    user_name = serializers.CharField(source='user.username', read_only=True)
    has_discount = serializers.ReadOnlyField()
    discount_percentage = serializers.ReadOnlyField()

    class Meta:
        model = Product
        fields = ('id', 'name', 'description', 'image', 'price', 'old_price',
                 'status', 'category_name', 'user_name', 'view_count', 'lead_count',
                 'has_discount', 'discount_percentage', 'created_at')


class ProductDetailSerializer(serializers.ModelSerializer):
    """Serializer for product detail view"""
    category = ProductCategorySerializer(read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)
    colors = ProductColorSerializer(many=True, read_only=True)
    sizes = ProductSizeSerializer(many=True, read_only=True)
    user_name = serializers.CharField(source='user.username', read_only=True)
    has_discount = serializers.ReadOnlyField()
    discount_percentage = serializers.ReadOnlyField()

    class Meta:
        model = Product
        fields = ('id', 'name', 'description', 'image', 'price', 'old_price',
                 'status', 'is_featured', 'view_count', 'lead_count',
                 'meta_title', 'meta_description', 'category', 'user_name',
                 'images', 'colors', 'sizes', 'has_discount', 'discount_percentage',
                 'created_at', 'updated_at')


class ProductCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating products"""
    colors = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        allow_empty=True
    )
    sizes = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        allow_empty=True
    )

    class Meta:
        model = Product
        fields = ('name', 'description', 'image', 'price', 'old_price',
                 'category', 'status', 'is_featured', 'meta_title',
                 'meta_description', 'colors', 'sizes')

    def validate(self, data):
        """Custom validation"""
        # Validate price
        if data.get('price') and data.get('old_price'):
            if data['old_price'] <= data['price']:
                raise serializers.ValidationError(
                    "السعر القديم يجب أن يكون أكبر من السعر الحالي"
                )
        return data

    def create(self, validated_data):
        colors_data = validated_data.pop('colors', [])
        sizes_data = validated_data.pop('sizes', [])
        
        product = Product.objects.create(**validated_data)
        
        # Create colors
        for color in colors_data:
            ProductColor.objects.create(product=product, color=color)
        
        # Create sizes
        for size in sizes_data:
            ProductSize.objects.create(product=product, size=size)
        
        return product

    def update(self, instance, validated_data):
        colors_data = validated_data.pop('colors', None)
        sizes_data = validated_data.pop('sizes', None)
        
        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update colors if provided
        if colors_data is not None:
            instance.colors.all().delete()
            for color in colors_data:
                ProductColor.objects.create(product=instance, color=color)
        
        # Update sizes if provided
        if sizes_data is not None:
            instance.sizes.all().delete()
            for size in sizes_data:
                ProductSize.objects.create(product=instance, size=size)
        
        return instance
