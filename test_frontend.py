#!/usr/bin/env python3
"""
اختبار سريع للواجهة الأمامية
"""
import requests
import time

FRONTEND_URL = "http://localhost:3001"

def test_frontend():
    """اختبار الواجهة الأمامية"""
    print("🔍 اختبار الواجهة الأمامية...")
    
    try:
        # اختبار الصفحة الرئيسية
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل بشكل مثالي")
            print(f"   📊 حالة الاستجابة: {response.status_code}")
            print(f"   📏 حجم المحتوى: {len(response.content)} بايت")
            
            # التحقق من وجود React في المحتوى
            if b'react' in response.content.lower() or b'root' in response.content:
                print("   ⚛️ تطبيق React محمل بنجاح")
            
            return True
        else:
            print(f"❌ فشل تحميل الصفحة الرئيسية: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم الأمامي")
        print("   💡 تأكد من تشغيل الخادم على المنفذ 3001")
        return False
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

def test_api_connection():
    """اختبار الاتصال بـ API"""
    print("\n🔍 اختبار الاتصال بـ API...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/", timeout=5)
        if response.status_code == 200:
            print("✅ API يعمل بشكل مثالي")
            return True
        else:
            print(f"❌ مشكلة في API: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار شامل للمنصة")
    print("=" * 50)
    
    # اختبار الواجهة الأمامية
    frontend_ok = test_frontend()
    
    # اختبار API
    api_ok = test_api_connection()
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   🌐 الواجهة الأمامية: {'✅ تعمل' if frontend_ok else '❌ لا تعمل'}")
    print(f"   🔗 API الخلفي: {'✅ يعمل' if api_ok else '❌ لا يعمل'}")
    
    if frontend_ok and api_ok:
        print("\n🎉 المنصة تعمل بشكل مثالي!")
        print("🌐 يمكنك الوصول للمنصة على: http://localhost:3001")
        print("🔑 بيانات الدخول: testadmin / test123")
    elif frontend_ok:
        print("\n⚠️ الواجهة الأمامية تعمل لكن هناك مشكلة في API")
        print("💡 تأكد من تشغيل الخادم الخلفي على المنفذ 8000")
    elif api_ok:
        print("\n⚠️ API يعمل لكن هناك مشكلة في الواجهة الأمامية")
        print("💡 تأكد من تشغيل الخادم الأمامي على المنفذ 3001")
    else:
        print("\n❌ هناك مشاكل في كلا الخادمين")
        print("💡 تأكد من تشغيل كلا الخادمين")

if __name__ == "__main__":
    main()
