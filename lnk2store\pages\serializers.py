from rest_framework import serializers
from .models import Page


class PageListSerializer(serializers.ModelSerializer):
    """Serializer for page list view"""
    class Meta:
        model = Page
        fields = ('id', 'title', 'slug')


class PageDetailSerializer(serializers.ModelSerializer):
    """Serializer for page detail view"""
    class Meta:
        model = Page
        fields = ('id', 'title', 'slug', 'content', 'template_name')


class PageCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating pages"""
    class Meta:
        model = Page
        fields = ('title', 'slug', 'content', 'template_name')

    def validate_slug(self, value):
        # Check if slug already exists (excluding current instance for updates)
        queryset = Page.objects.filter(slug=value)
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError('صفحة بهذا الرابط موجودة بالفعل')
        return value
