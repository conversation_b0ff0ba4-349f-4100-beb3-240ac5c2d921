from django.urls import path
from . import api_views

urlpatterns = [
    # Page CRUD endpoints
    path('', api_views.PageListView.as_view(), name='api_page_list'),
    path('create/', api_views.PageCreateView.as_view(), name='api_page_create'),
    path('<slug:slug>/', api_views.PageDetailView.as_view(), name='api_page_detail'),
    path('<slug:slug>/update/', api_views.PageUpdateView.as_view(), name='api_page_update'),
    path('<slug:slug>/delete/', api_views.PageDeleteView.as_view(), name='api_page_delete'),
    
    # Special endpoints
    path('by-slug/<slug:slug>/', api_views.page_by_slug, name='api_page_by_slug'),
    path('user/<str:username>/', api_views.user_page, name='api_user_page'),
]
