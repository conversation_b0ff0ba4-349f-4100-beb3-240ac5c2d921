import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './hooks/useAuth';
import { AppProvider } from './contexts/AppContext';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';
import ProductsPage from './pages/ProductsPage';
import ProductCreatePage from './pages/ProductCreatePage';
import ProductDetailPage from './pages/ProductDetailPage';
import LeadsPage from './pages/LeadsPage';
import WalletPage from './pages/WalletPage';
import AdminDashboard from './pages/AdminDashboard';
import TemplateSelectionPage from './pages/TemplateSelectionPage';
import TemplateCustomizePage from './pages/TemplateCustomizePage';
import MyPagesPage from './pages/MyPagesPage';
import ShoppingTemplate from './components/ShoppingTemplate';
import './App.css';

function App() {
  return (
    <ErrorBoundary>
      <AppProvider>
        <AuthProvider>
          <Router>
            <Layout>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />
                
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <DashboardPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/products" element={
                  <ProtectedRoute>
                    <ProductsPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/products/create" element={
                  <ProtectedRoute>
                    <ProductCreatePage />
                  </ProtectedRoute>
                } />
                
                <Route path="/products/:id" element={
                  <ProtectedRoute>
                    <ProductDetailPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/products/:id/edit" element={
                  <ProtectedRoute>
                    <ProductCreatePage />
                  </ProtectedRoute>
                } />
                
                <Route path="/leads" element={
                  <ProtectedRoute>
                    <LeadsPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/wallet" element={
                  <ProtectedRoute>
                    <WalletPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/admin" element={
                  <ProtectedRoute>
                    <AdminDashboard />
                  </ProtectedRoute>
                } />
                
                <Route path="/templates" element={
                  <ProtectedRoute>
                    <TemplateSelectionPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/templates/:templateId/customize" element={
                  <ProtectedRoute>
                    <TemplateCustomizePage />
                  </ProtectedRoute>
                } />
                
                <Route path="/my-pages" element={
                  <ProtectedRoute>
                    <MyPagesPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/page/:slug" element={<ShoppingTemplate />} />
                <Route path="/p/:slug" element={<ShoppingTemplate />} />
              </Routes>
            </Layout>
          </Router>
        </AuthProvider>
      </AppProvider>
    </ErrorBoundary>
  );
}

export default App;
