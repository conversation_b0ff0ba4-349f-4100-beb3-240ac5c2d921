#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'lnk2store.settings')
django.setup()

from django.contrib.auth import get_user_model

def create_test_user():
    User = get_user_model()
    
    email = '<EMAIL>'
    password = 'testpassword123'
    username = 'ahmed.hassan'
    
    try:
        # Check if user already exists
        if User.objects.filter(email=email).exists():
            print(f'User with email {email} already exists')
            user = User.objects.get(email=email)
            print(f'Username: {user.username}')
            print(f'Email: {user.email}')
            return
        
        # Create the user
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name='<PERSON>',
            last_name='<PERSON>'
        )
        
        print(f'Successfully created test user!')
        print(f'Username: {username}')
        print(f'Email: {email}')
        print(f'Password: {password}')
        
    except Exception as e:
        print(f'Error creating user: {e}')

if __name__ == '__main__':
    create_test_user()
