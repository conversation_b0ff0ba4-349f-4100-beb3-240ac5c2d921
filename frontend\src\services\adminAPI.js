import api from './api';

// Admin API endpoints
export const adminAPI = {
  // Dashboard overview
  getOverview: () => api.get('/admin/overview/'),
  getCharts: (days = 30) => api.get(`/admin/charts/?days=${days}`),
  
  // Data management
  getUsers: (page = 1, pageSize = 20, search = '', filter = 'all') => 
    api.get(`/admin/users/?page=${page}&page_size=${pageSize}&search=${search}&filter=${filter}`),
  
  getLeads: (page = 1, pageSize = 20, filter = 'all') => 
    api.get(`/admin/leads/?page=${page}&page_size=${pageSize}&filter=${filter}`),
  
  getTransactions: (page = 1, pageSize = 20) => 
    api.get(`/admin/transactions/?page=${page}&page_size=${pageSize}`),
  
  // Notifications
  getNotifications: () => api.get('/admin/notifications/'),
  markNotificationRead: (notificationId) => api.post(`/admin/notifications/${notificationId}/read/`),
};

export default adminAPI;
