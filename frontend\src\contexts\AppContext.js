import React, { createContext, useContext, useState, useEffect } from 'react';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

// إنشاء Context
const AppContext = createContext();

// اللغات المدعومة
export const LANGUAGES = {
  ar: {
    code: 'ar',
    name: 'العربية',
    dir: 'rtl',
    flag: '🇪🇬'
  },
  en: {
    code: 'en',
    name: 'English',
    dir: 'ltr',
    flag: '🇺🇸'
  }
};

// العملات المدعومة
export const CURRENCIES = {
  EGP: {
    code: 'EGP',
    symbol: 'ج.م',
    name: 'جنيه مصري',
    nameEn: 'Egyptian Pound'
  },
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'دولار أمريكي',
    nameEn: 'US Dollar'
  },
  SAR: {
    code: 'SAR',
    symbol: 'ر.س',
    name: 'ريال سعودي',
    nameEn: 'Saudi Riyal'
  }
};

// الثيمات
const createAppTheme = (mode, language) => {
  const isRTL = language === 'ar';
  
  return createTheme({
    direction: isRTL ? 'rtl' : 'ltr',
    palette: {
      mode,
      primary: {
        main: mode === 'dark' ? '#90caf9' : '#1976d2',
        light: mode === 'dark' ? '#bbdefb' : '#42a5f5',
        dark: mode === 'dark' ? '#64b5f6' : '#1565c0',
      },
      secondary: {
        main: mode === 'dark' ? '#f48fb1' : '#dc004e',
      },
      background: {
        default: mode === 'dark' ? '#121212' : '#fafafa',
        paper: mode === 'dark' ? '#1e1e1e' : '#ffffff',
      },
      text: {
        primary: mode === 'dark' ? '#ffffff' : '#000000',
        secondary: mode === 'dark' ? '#b0b0b0' : '#666666',
      }
    },
    typography: {
      fontFamily: isRTL 
        ? '"Cairo", "Roboto", "Helvetica", "Arial", sans-serif'
        : '"Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontSize: '2.5rem',
        fontWeight: 600,
      },
      h2: {
        fontSize: '2rem',
        fontWeight: 600,
      },
      h3: {
        fontSize: '1.75rem',
        fontWeight: 600,
      },
      h4: {
        fontSize: '1.5rem',
        fontWeight: 600,
      },
      h5: {
        fontSize: '1.25rem',
        fontWeight: 600,
      },
      h6: {
        fontSize: '1rem',
        fontWeight: 600,
      }
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            textTransform: 'none',
            fontWeight: 600,
          }
        }
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            boxShadow: mode === 'dark' 
              ? '0 4px 20px rgba(0,0,0,0.3)'
              : '0 4px 20px rgba(0,0,0,0.1)',
          }
        }
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
            }
          }
        }
      }
    }
  });
};

// مزود السياق
export const AppProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    return localStorage.getItem('language') || 'ar';
  });
  
  const [currency, setCurrency] = useState(() => {
    return localStorage.getItem('currency') || 'EGP';
  });
  
  const [themeMode, setThemeMode] = useState(() => {
    return localStorage.getItem('themeMode') || 'light';
  });

  // حفظ الإعدادات في localStorage
  useEffect(() => {
    localStorage.setItem('language', language);
    document.dir = LANGUAGES[language].dir;
    document.documentElement.lang = language;
  }, [language]);

  useEffect(() => {
    localStorage.setItem('currency', currency);
  }, [currency]);

  useEffect(() => {
    localStorage.setItem('themeMode', themeMode);
  }, [themeMode]);

  // إنشاء الثيم
  const theme = createAppTheme(themeMode, language);

  // دوال التحكم
  const toggleTheme = () => {
    setThemeMode(prev => prev === 'light' ? 'dark' : 'light');
  };

  const changeLanguage = (newLanguage) => {
    setLanguage(newLanguage);
  };

  const changeCurrency = (newCurrency) => {
    setCurrency(newCurrency);
  };

  // دالة تنسيق العملة
  const formatCurrency = (amount, showSymbol = true) => {
    const currencyInfo = CURRENCIES[currency];
    const formattedAmount = new Intl.NumberFormat(
      language === 'ar' ? 'ar-EG' : 'en-US',
      {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }
    ).format(amount);

    if (!showSymbol) return formattedAmount;
    
    return language === 'ar' 
      ? `${formattedAmount} ${currencyInfo.symbol}`
      : `${currencyInfo.symbol}${formattedAmount}`;
  };

  // دالة الترجمة البسيطة
  const t = (key, fallback = key) => {
    // يمكن توسيعها لاحقاً لتشمل ملفات ترجمة
    return fallback;
  };

  const value = {
    // الحالة
    language,
    currency,
    themeMode,
    theme,
    
    // المعلومات
    currentLanguage: LANGUAGES[language],
    currentCurrency: CURRENCIES[currency],
    isRTL: language === 'ar',
    isDark: themeMode === 'dark',
    
    // الدوال
    changeLanguage,
    changeCurrency,
    toggleTheme,
    formatCurrency,
    t,
    
    // البيانات
    languages: LANGUAGES,
    currencies: CURRENCIES
  };

  return (
    <AppContext.Provider value={value}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </AppContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};
