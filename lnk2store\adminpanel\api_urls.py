from django.urls import path
from . import api_views

urlpatterns = [
    # Dashboard overview
    path('overview/', api_views.admin_overview, name='api_admin_overview'),
    path('charts/', api_views.admin_charts, name='api_admin_charts'),
    
    # Data management
    path('users/', api_views.admin_users, name='api_admin_users'),
    path('leads/', api_views.admin_leads, name='api_admin_leads'),
    path('transactions/', api_views.admin_transactions, name='api_admin_transactions'),
    
    # Notifications
    path('notifications/', api_views.admin_notifications, name='api_admin_notifications'),
    path('notifications/<int:notification_id>/read/', api_views.mark_notification_read, name='api_mark_notification_read'),
]
