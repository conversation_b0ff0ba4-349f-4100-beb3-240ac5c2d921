import React, { useState } from 'react';
import {
  Box,
  Grid,
  <PERSON><PERSON><PERSON>,
  Button,
  Chip,
  Card,
  CardContent,
  CardMedia,
  Divider,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Edit,
  Delete,
  Share,
  Visibility,
  ShoppingCart,
  Palette,
  Straighten
} from '@mui/icons-material';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useApi, useAsyncOperation } from '../hooks/useApi';
import { productsAPI } from '../services/api';
import LeadForm from '../components/LeadForm';

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);

  const { data: product, loading, error, refetch } = useApi(() => productsAPI.getProduct(id), [id]);
  const { loading: deleting, execute: executeDelete } = useAsyncOperation();

  const handleDelete = async () => {
    const result = await executeDelete(() => productsAPI.deleteProduct(id));
    if (result.success) {
      navigate('/products');
    }
  };

  const handleShare = () => {
    const url = `${window.location.origin}/product/${id}`;
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: url,
      });
    } else {
      navigator.clipboard.writeText(url);
      alert('تم نسخ الرابط');
    }
  };

  const formatCurrency = (amount) => `${amount} ريال`;

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        {error}
      </Alert>
    );
  }

  if (!product) {
    return (
      <Alert severity="warning">
        المنتج غير موجود
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4">
          تفاصيل المنتج
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={handleShare} color="primary">
            <Share />
          </IconButton>
          <Button
            variant="outlined"
            startIcon={<Edit />}
            component={Link}
            to={`/products/${id}/edit`}
          >
            تعديل
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<Delete />}
            onClick={() => setDeleteDialogOpen(true)}
          >
            حذف
          </Button>
        </Box>
      </Box>

      <Grid container spacing={4}>
        {/* Product Image */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardMedia
              component="img"
              height="400"
              image={product.image || '/placeholder-product.jpg'}
              alt={product.name}
              sx={{ objectFit: 'cover' }}
            />
          </Card>
        </Grid>

        {/* Product Details */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h4" gutterBottom>
                {product.name}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                {product.old_price && (
                  <Typography
                    variant="h6"
                    sx={{ textDecoration: 'line-through', color: 'text.secondary' }}
                  >
                    {formatCurrency(product.old_price)}
                  </Typography>
                )}
                <Typography variant="h5" color="primary" fontWeight="bold">
                  {formatCurrency(product.price)}
                </Typography>
                {product.old_price && (
                  <Chip
                    label={`خصم ${Math.round(((product.old_price - product.price) / product.old_price) * 100)}%`}
                    color="error"
                    size="small"
                  />
                )}
              </Box>

              <Typography variant="body1" paragraph>
                {product.description}
              </Typography>

              <Divider sx={{ my: 2 }} />

              {/* Colors */}
              {product.colors && product.colors.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Palette fontSize="small" />
                    <Typography variant="subtitle1" fontWeight="bold">
                      الألوان المتاحة:
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {product.colors.map((color, index) => (
                      <Chip key={index} label={color.color} variant="outlined" />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Sizes */}
              {product.sizes && product.sizes.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Straighten fontSize="small" />
                    <Typography variant="subtitle1" fontWeight="bold">
                      المقاسات المتاحة:
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {product.sizes.map((size, index) => (
                      <Chip key={index} label={size.size} variant="outlined" />
                    ))}
                  </Box>
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              {/* Stats */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" color="primary">
                      {product.view_count || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      مشاهدة
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" color="success.main">
                      {product.lead_count || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      طلب
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<ShoppingCart />}
                  onClick={() => setOrderDialogOpen(true)}
                >
                  اطلب الآن
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Visibility />}
                  component={Link}
                  to={`/page/${product.user?.username}`}
                >
                  عرض الصفحة
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Additional Images */}
        {product.images && product.images.length > 0 && (
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              صور إضافية
            </Typography>
            <Grid container spacing={2}>
              {product.images.map((image, index) => (
                <Grid item xs={6} sm={4} md={3} key={index}>
                  <Card>
                    <CardMedia
                      component="img"
                      height="200"
                      image={image.image}
                      alt={`${product.name} - صورة ${index + 1}`}
                      sx={{ objectFit: 'cover' }}
                    />
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Grid>
        )}
      </Grid>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          <Typography>
            هل أنت متأكد من حذف المنتج "{product.name}"؟ هذا الإجراء لا يمكن التراجع عنه.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            إلغاء
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={deleting}
            startIcon={deleting ? <CircularProgress size={20} /> : <Delete />}
          >
            حذف
          </Button>
        </DialogActions>
      </Dialog>

      {/* Order Dialog */}
      <Dialog 
        open={orderDialogOpen} 
        onClose={() => setOrderDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>طلب {product.name}</DialogTitle>
        <DialogContent>
          <LeadForm
            productId={product.id}
            onSuccess={() => {
              setOrderDialogOpen(false);
              refetch(); // Refresh product data to update lead count
            }}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ProductDetailPage;
