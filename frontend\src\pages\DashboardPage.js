import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  AccountBalanceWallet,
  ShoppingCart,
  ContactMail,
  TrendingUp,
  Add,
  Visibility
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useApi } from '../hooks/useApi';
import { authAPI, leadsAPI, walletAPI } from '../services/api';

const DashboardPage = () => {
  const { user } = useAuth();
  const { data: userStats, loading: statsLoading } = useApi(() => authAPI.getUserStats());
  const { data: leadStats, loading: leadStatsLoading } = useApi(() => leadsAPI.getLeadStats());
  const { data: recentLeads, loading: recentLeadsLoading } = useApi(() => leadsAPI.getRecentLeads(5));
  const { data: walletStats, loading: walletStatsLoading } = useApi(() => walletAPI.getWalletStats());

  const formatCurrency = (amount) => `${amount} ريال`;
  const formatDate = (dateString) => new Date(dateString).toLocaleDateString('ar-SA');

  if (statsLoading || leadStatsLoading || walletStatsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        مرحباً، {user?.first_name || user?.username}
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        إليك نظرة عامة على نشاطك
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AccountBalanceWallet sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h5">
                {formatCurrency(walletStats?.current_balance || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                رصيد المحفظة
              </Typography>
              <Button
                size="small"
                component={Link}
                to="/wallet"
                sx={{ mt: 1 }}
              >
                إدارة المحفظة
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ContactMail sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h5">
                {leadStats?.total_leads || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                إجمالي الطلبات
              </Typography>
              <Button
                size="small"
                component={Link}
                to="/leads"
                sx={{ mt: 1 }}
              >
                عرض الطلبات
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ShoppingCart sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
              <Typography variant="h5">
                {userStats?.total_products || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                المنتجات
              </Typography>
              <Button
                size="small"
                component={Link}
                to="/products"
                sx={{ mt: 1 }}
              >
                إدارة المنتجات
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h5">
                {leadStats?.today_leads || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                طلبات اليوم
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Low Balance Alert */}
      {walletStats?.current_balance < 50 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          رصيدك منخفض ({formatCurrency(walletStats.current_balance)})! 
          <Button component={Link} to="/wallet" sx={{ ml: 1 }}>
            شحن المحفظة
          </Button>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Recent Leads */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  آخر الطلبات
                </Typography>
                <Button
                  component={Link}
                  to="/leads"
                  startIcon={<Visibility />}
                  size="small"
                >
                  عرض الكل
                </Button>
              </Box>

              {recentLeadsLoading ? (
                <Box display="flex" justifyContent="center" p={3}>
                  <CircularProgress />
                </Box>
              ) : recentLeads && recentLeads.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>الاسم</TableCell>
                        <TableCell>الهاتف</TableCell>
                        <TableCell>المنتج</TableCell>
                        <TableCell>التاريخ</TableCell>
                        <TableCell>الحالة</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {recentLeads.map((lead) => (
                        <TableRow key={lead.id}>
                          <TableCell>{lead.name}</TableCell>
                          <TableCell>{lead.phone_number}</TableCell>
                          <TableCell>{lead.product?.name || 'غير محدد'}</TableCell>
                          <TableCell>{formatDate(lead.created_at)}</TableCell>
                          <TableCell>
                            <Chip
                              label={lead.deducted ? 'معالج' : 'جديد'}
                              color={lead.deducted ? 'success' : 'warning'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={3}>
                  لا توجد طلبات حتى الآن
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                إجراءات سريعة
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  component={Link}
                  to="/products/create"
                  fullWidth
                >
                  إضافة منتج جديد
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<AccountBalanceWallet />}
                  component={Link}
                  to="/wallet"
                  fullWidth
                >
                  شحن المحفظة
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<Visibility />}
                  component={Link}
                  to={`/page/${user?.username}`}
                  fullWidth
                >
                  عرض صفحتي
                </Button>
              </Box>

              {/* Stats Summary */}
              <Box sx={{ mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
                <Typography variant="subtitle2" gutterBottom>
                  إحصائيات سريعة
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • طلبات هذا الأسبوع: {leadStats?.this_week_leads || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • طلبات هذا الشهر: {leadStats?.this_month_leads || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • طلبات معلقة: {leadStats?.pending_leads || 0}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
