import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  Avatar,
  LinearProgress
} from '@mui/material';
import {
  AccountBalanceWallet,
  ShoppingCart,
  ContactMail,
  TrendingUp,
  Add,
  Visibility,
  Dashboard,
  Analytics,
  Star,
  Timeline
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useApi } from '../hooks/useApi';
import { useApp } from '../contexts/AppContext';
import { authAPI, leadsAPI, walletAPI } from '../services/api';

const DashboardPage = () => {
  const { user } = useAuth();
  const { formatCurrency, isDark, currentLanguage } = useApp();
  const { data: userStats, loading: statsLoading } = useApi(() => authAPI.getUserStats());
  const { data: leadStats, loading: leadStatsLoading } = useApi(() => leadsAPI.getLeadStats());
  const { data: recentLeads, loading: recentLeadsLoading } = useApi(() => leadsAPI.getRecentLeads(5));
  const { data: walletStats, loading: walletStatsLoading } = useApi(() => walletAPI.getWalletStats());

  const formatDate = (dateString) => new Date(dateString).toLocaleDateString('ar-SA');

  if (statsLoading || leadStatsLoading || walletStatsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{
        mb: 4,
        p: 3,
        background: isDark
          ? 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
          : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: 3,
        color: 'white'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
            <Dashboard />
          </Avatar>
          <Box>
            <Typography variant="h4" gutterBottom sx={{ color: 'white', mb: 0 }}>
              مرحباً، {user?.username}! 👋
            </Typography>
            <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.9)' }}>
              إليك نظرة عامة على أداء متجرك اليوم
            </Typography>
          </Box>
        </Box>

        {/* شريط التقدم للمستخدم المميز */}
        {user?.is_premium ? (
          <Chip
            icon={<Star />}
            label="مستخدم مميز"
            sx={{
              bgcolor: 'rgba(255,215,0,0.2)',
              color: 'gold',
              fontWeight: 'bold'
            }}
          />
        ) : (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)', mb: 1 }}>
              المنتجات: {userStats?.total_products || 0} / 3
            </Typography>
            <LinearProgress
              variant="determinate"
              value={((userStats?.total_products || 0) / 3) * 100}
              sx={{
                height: 8,
                borderRadius: 4,
                bgcolor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  bgcolor: 'rgba(255,255,255,0.8)'
                }
              }}
            />
          </Box>
        )}
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            transition: 'transform 0.3s ease',
            '&:hover': { transform: 'translateY(-4px)' }
          }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                width: 56,
                height: 56,
                mx: 'auto',
                mb: 2
              }}>
                <AccountBalanceWallet sx={{ fontSize: 30 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                {formatCurrency(walletStats?.current_balance || 0)}
              </Typography>
              <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.9)', mb: 2 }}>
                💰 رصيد المحفظة
              </Typography>
              <Button
                variant="contained"
                size="small"
                component={Link}
                to="/wallet"
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
                }}
              >
                إدارة المحفظة
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
            color: 'white',
            transition: 'transform 0.3s ease',
            '&:hover': { transform: 'translateY(-4px)' }
          }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                width: 56,
                height: 56,
                mx: 'auto',
                mb: 2
              }}>
                <ContactMail sx={{ fontSize: 30 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                {leadStats?.total_leads || 0}
              </Typography>
              <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.9)', mb: 2 }}>
                📋 إجمالي الطلبات
              </Typography>
              <Button
                variant="contained"
                size="small"
                component={Link}
                to="/leads"
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
                }}
              >
                عرض الطلبات
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            transition: 'transform 0.3s ease',
            '&:hover': { transform: 'translateY(-4px)' }
          }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                width: 56,
                height: 56,
                mx: 'auto',
                mb: 2
              }}>
                <ShoppingCart sx={{ fontSize: 30 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                {userStats?.total_products || 0}
              </Typography>
              <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.9)', mb: 2 }}>
                📦 المنتجات
              </Typography>
              <Button
                variant="contained"
                size="small"
                component={Link}
                to="/products"
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
                }}
              >
                إدارة المنتجات
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h5">
                {leadStats?.today_leads || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                طلبات اليوم
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Low Balance Alert */}
      {walletStats?.current_balance < 50 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          رصيدك منخفض ({formatCurrency(walletStats.current_balance)})! 
          <Button component={Link} to="/wallet" sx={{ ml: 1 }}>
            شحن المحفظة
          </Button>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Recent Leads */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  آخر الطلبات
                </Typography>
                <Button
                  component={Link}
                  to="/leads"
                  startIcon={<Visibility />}
                  size="small"
                >
                  عرض الكل
                </Button>
              </Box>

              {recentLeadsLoading ? (
                <Box display="flex" justifyContent="center" p={3}>
                  <CircularProgress />
                </Box>
              ) : recentLeads && recentLeads.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>الاسم</TableCell>
                        <TableCell>الهاتف</TableCell>
                        <TableCell>المنتج</TableCell>
                        <TableCell>التاريخ</TableCell>
                        <TableCell>الحالة</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {recentLeads.map((lead) => (
                        <TableRow key={lead.id}>
                          <TableCell>{lead.name}</TableCell>
                          <TableCell>{lead.phone_number}</TableCell>
                          <TableCell>{lead.product?.name || 'غير محدد'}</TableCell>
                          <TableCell>{formatDate(lead.created_at)}</TableCell>
                          <TableCell>
                            <Chip
                              label={lead.deducted ? 'معالج' : 'جديد'}
                              color={lead.deducted ? 'success' : 'warning'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={3}>
                  لا توجد طلبات حتى الآن
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                إجراءات سريعة
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  component={Link}
                  to="/products/create"
                  fullWidth
                >
                  إضافة منتج جديد
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<AccountBalanceWallet />}
                  component={Link}
                  to="/wallet"
                  fullWidth
                >
                  شحن المحفظة
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<Visibility />}
                  component={Link}
                  to={`/page/${user?.username}`}
                  fullWidth
                >
                  عرض صفحتي
                </Button>
              </Box>

              {/* Stats Summary */}
              <Box sx={{ mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
                <Typography variant="subtitle2" gutterBottom>
                  إحصائيات سريعة
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • طلبات هذا الأسبوع: {leadStats?.this_week_leads || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • طلبات هذا الشهر: {leadStats?.this_month_leads || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • طلبات معلقة: {leadStats?.pending_leads || 0}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
