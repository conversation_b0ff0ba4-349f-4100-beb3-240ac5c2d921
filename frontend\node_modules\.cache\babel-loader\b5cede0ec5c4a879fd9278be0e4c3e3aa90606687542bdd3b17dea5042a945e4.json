{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\VideoBackground.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoBackground = ({\n  src,\n  fallbackBackground = 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n  overlay = 'linear-gradient(45deg, rgba(33, 150, 243, 0.8) 30%, rgba(33, 203, 243, 0.8) 90%)',\n  minHeight = '600px',\n  aspectRatio = '16/9',\n  children,\n  ...props\n}) => {\n  _s();\n  const [videoLoaded, setVideoLoaded] = useState(false);\n  const [videoError, setVideoError] = useState(false);\n  const videoRef = useRef(null);\n  useEffect(() => {\n    const video = videoRef.current;\n    if (video) {\n      const handleLoadedData = () => {\n        setVideoLoaded(true);\n        setVideoError(false);\n      };\n      const handleError = () => {\n        setVideoError(true);\n        setVideoLoaded(false);\n      };\n      video.addEventListener('loadeddata', handleLoadedData);\n      video.addEventListener('error', handleError);\n      return () => {\n        video.removeEventListener('loadeddata', handleLoadedData);\n        video.removeEventListener('error', handleError);\n      };\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      overflow: 'hidden',\n      minHeight: {\n        xs: '400px',\n        sm: '500px',\n        md: minHeight\n      },\n      height: {\n        xs: '50vh',\n        sm: '60vh',\n        md: '70vh'\n      },\n      maxHeight: '800px',\n      display: 'flex',\n      alignItems: 'center',\n      background: videoError || !videoLoaded ? fallbackBackground : 'transparent',\n      ...props.sx\n    },\n    ...props,\n    children: [!videoError && /*#__PURE__*/_jsxDEV(Box, {\n      ref: videoRef,\n      component: \"video\",\n      autoPlay: true,\n      muted: true,\n      loop: true,\n      playsInline: true,\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover',\n        zIndex: -2,\n        opacity: videoLoaded ? 1 : 0,\n        transition: 'opacity 0.5s ease-in-out'\n      },\n      onLoadedData: () => setVideoLoaded(true),\n      onError: () => setVideoError(true),\n      children: [/*#__PURE__*/_jsxDEV(\"source\", {\n        src: src,\n        type: \"video/mp4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), \"Your browser does not support the video tag.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        background: overlay,\n        zIndex: -1\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1,\n        width: '100%'\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoBackground, \"UlTjCL37fMpGvE/g5IDFVw6wYTs=\");\n_c = VideoBackground;\nexport default VideoBackground;\nvar _c;\n$RefreshReg$(_c, \"VideoBackground\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "jsxDEV", "_jsxDEV", "VideoBackground", "src", "fallbackBackground", "overlay", "minHeight", "aspectRatio", "children", "props", "_s", "videoLoaded", "setVideoLoaded", "videoError", "setVideoError", "videoRef", "video", "current", "handleLoadedData", "handleError", "addEventListener", "removeEventListener", "sx", "position", "overflow", "xs", "sm", "md", "height", "maxHeight", "display", "alignItems", "background", "ref", "component", "autoPlay", "muted", "loop", "playsInline", "top", "left", "width", "objectFit", "zIndex", "opacity", "transition", "onLoadedData", "onError", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/VideoBackground.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Box } from '@mui/material';\n\nconst VideoBackground = ({\n  src,\n  fallbackBackground = 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',\n  overlay = 'linear-gradient(45deg, rgba(33, 150, 243, 0.8) 30%, rgba(33, 203, 243, 0.8) 90%)',\n  minHeight = '600px',\n  aspectRatio = '16/9',\n  children,\n  ...props\n}) => {\n  const [videoLoaded, setVideoLoaded] = useState(false);\n  const [videoError, setVideoError] = useState(false);\n  const videoRef = useRef(null);\n\n  useEffect(() => {\n    const video = videoRef.current;\n    if (video) {\n      const handleLoadedData = () => {\n        setVideoLoaded(true);\n        setVideoError(false);\n      };\n\n      const handleError = () => {\n        setVideoError(true);\n        setVideoLoaded(false);\n      };\n\n      video.addEventListener('loadeddata', handleLoadedData);\n      video.addEventListener('error', handleError);\n\n      return () => {\n        video.removeEventListener('loadeddata', handleLoadedData);\n        video.removeEventListener('error', handleError);\n      };\n    }\n  }, []);\n\n  return (\n    <Box\n      sx={{\n        position: 'relative',\n        overflow: 'hidden',\n        minHeight: { xs: '400px', sm: '500px', md: minHeight },\n        height: { xs: '50vh', sm: '60vh', md: '70vh' },\n        maxHeight: '800px',\n        display: 'flex',\n        alignItems: 'center',\n        background: videoError || !videoLoaded ? fallbackBackground : 'transparent',\n        ...props.sx\n      }}\n      {...props}\n    >\n      {/* Background Video */}\n      {!videoError && (\n        <Box\n          ref={videoRef}\n          component=\"video\"\n          autoPlay\n          muted\n          loop\n          playsInline\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            zIndex: -2,\n            opacity: videoLoaded ? 1 : 0,\n            transition: 'opacity 0.5s ease-in-out'\n          }}\n          onLoadedData={() => setVideoLoaded(true)}\n          onError={() => setVideoError(true)}\n        >\n          <source src={src} type=\"video/mp4\" />\n          Your browser does not support the video tag.\n        </Box>\n      )}\n      \n      {/* Overlay for better text readability */}\n      <Box\n        sx={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          background: overlay,\n          zIndex: -1\n        }}\n      />\n      \n      {/* Content */}\n      <Box sx={{ position: 'relative', zIndex: 1, width: '100%' }}>\n        {children}\n      </Box>\n    </Box>\n  );\n};\n\nexport default VideoBackground;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGA,CAAC;EACvBC,GAAG;EACHC,kBAAkB,GAAG,kDAAkD;EACvEC,OAAO,GAAG,kFAAkF;EAC5FC,SAAS,GAAG,OAAO;EACnBC,WAAW,GAAG,MAAM;EACpBC,QAAQ;EACR,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmB,QAAQ,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAE7BC,SAAS,CAAC,MAAM;IACd,MAAMkB,KAAK,GAAGD,QAAQ,CAACE,OAAO;IAC9B,IAAID,KAAK,EAAE;MACT,MAAME,gBAAgB,GAAGA,CAAA,KAAM;QAC7BN,cAAc,CAAC,IAAI,CAAC;QACpBE,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC;MAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;QACxBL,aAAa,CAAC,IAAI,CAAC;QACnBF,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;MAEDI,KAAK,CAACI,gBAAgB,CAAC,YAAY,EAAEF,gBAAgB,CAAC;MACtDF,KAAK,CAACI,gBAAgB,CAAC,OAAO,EAAED,WAAW,CAAC;MAE5C,OAAO,MAAM;QACXH,KAAK,CAACK,mBAAmB,CAAC,YAAY,EAAEH,gBAAgB,CAAC;QACzDF,KAAK,CAACK,mBAAmB,CAAC,OAAO,EAAEF,WAAW,CAAC;MACjD,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACElB,OAAA,CAACF,GAAG;IACFuB,EAAE,EAAE;MACFC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBlB,SAAS,EAAE;QAAEmB,EAAE,EAAE,OAAO;QAAEC,EAAE,EAAE,OAAO;QAAEC,EAAE,EAAErB;MAAU,CAAC;MACtDsB,MAAM,EAAE;QAAEH,EAAE,EAAE,MAAM;QAAEC,EAAE,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAO,CAAC;MAC9CE,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAEnB,UAAU,IAAI,CAACF,WAAW,GAAGP,kBAAkB,GAAG,aAAa;MAC3E,GAAGK,KAAK,CAACa;IACX,CAAE;IAAA,GACEb,KAAK;IAAAD,QAAA,GAGR,CAACK,UAAU,iBACVZ,OAAA,CAACF,GAAG;MACFkC,GAAG,EAAElB,QAAS;MACdmB,SAAS,EAAC,OAAO;MACjBC,QAAQ;MACRC,KAAK;MACLC,IAAI;MACJC,WAAW;MACXhB,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpBgB,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,MAAM;QACbb,MAAM,EAAE,MAAM;QACdc,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC,CAAC;QACVC,OAAO,EAAEjC,WAAW,GAAG,CAAC,GAAG,CAAC;QAC5BkC,UAAU,EAAE;MACd,CAAE;MACFC,YAAY,EAAEA,CAAA,KAAMlC,cAAc,CAAC,IAAI,CAAE;MACzCmC,OAAO,EAAEA,CAAA,KAAMjC,aAAa,CAAC,IAAI,CAAE;MAAAN,QAAA,gBAEnCP,OAAA;QAAQE,GAAG,EAAEA,GAAI;QAAC6C,IAAI,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gDAEvC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAGDnD,OAAA,CAACF,GAAG;MACFuB,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpBgB,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,MAAM;QACbb,MAAM,EAAE,MAAM;QACdI,UAAU,EAAE3B,OAAO;QACnBsC,MAAM,EAAE,CAAC;MACX;IAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFnD,OAAA,CAACF,GAAG;MAACuB,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEoB,MAAM,EAAE,CAAC;QAAEF,KAAK,EAAE;MAAO,CAAE;MAAAjC,QAAA,EACzDA;IAAQ;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAlGIR,eAAe;AAAAmD,EAAA,GAAfnD,eAAe;AAoGrB,eAAeA,eAAe;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}