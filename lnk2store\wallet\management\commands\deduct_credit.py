from django.core.management.base import BaseCommand
from django.db import transaction
from leads.models import Lead
from wallet.models import Wallet, Transaction
from decimal import Decimal

class Command(BaseCommand):
    help = 'Deduct credit from users for new leads'

    def add_arguments(self, parser):
        parser.add_argument(
            '--lead-cost',
            type=float,
            default=10.00,
            help='Cost per lead in SAR (default: 10.00)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without making changes'
        )

    def handle(self, *args, **options):
        lead_cost = Decimal(str(options['lead_cost']))
        dry_run = options['dry_run']

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        leads = Lead.objects.filter(deducted=False).select_related('user', 'user__wallet')
        processed_count = 0
        insufficient_balance_count = 0

        for lead in leads:
            try:
                wallet, created = Wallet.objects.get_or_create(user=lead.user)

                if wallet.can_deduct(lead_cost):
                    if not dry_run:
                        with transaction.atomic():
                            wallet.deduct(
                                amount=lead_cost,
                                description=f'Lead cost for {lead.name} - {lead.phone_number}'
                            )
                            lead.deducted = True
                            lead.save()

                    processed_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'{"[DRY RUN] " if dry_run else ""}Successfully deducted {lead_cost} SAR from {lead.user.username} for lead #{lead.id}'
                        )
                    )
                else:
                    insufficient_balance_count += 1
                    self.stdout.write(
                        self.style.WARNING(
                            f'Insufficient balance for {lead.user.username} (Balance: {wallet.balance} SAR, Required: {lead_cost} SAR)'
                        )
                    )

                    # Send low balance notification
                    if not dry_run:
                        wallet.check_and_send_low_balance_alert()

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Error processing lead #{lead.id} for user {lead.user.username}: {str(e)}'
                    )
                )

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\n{"DRY RUN " if dry_run else ""}SUMMARY:\n'
                f'- Processed leads: {processed_count}\n'
                f'- Insufficient balance: {insufficient_balance_count}\n'
                f'- Total leads checked: {leads.count()}'
            )
        )