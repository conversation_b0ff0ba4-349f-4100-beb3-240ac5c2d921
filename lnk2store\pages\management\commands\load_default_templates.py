from django.core.management.base import BaseCommand
from pages.template_models import Template
from pages.default_templates import DEFAULT_TEMPLATES

class Command(BaseCommand):
    help = 'Load default marketing templates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--overwrite',
            action='store_true',
            help='Overwrite existing templates with same name'
        )

    def handle(self, *args, **options):
        overwrite = options['overwrite']
        created_count = 0
        updated_count = 0
        
        for template_data in DEFAULT_TEMPLATES:
            template_name = template_data['name']
            
            # Check if template already exists
            existing_template = Template.objects.filter(name=template_name).first()
            
            if existing_template:
                if overwrite:
                    # Update existing template
                    for key, value in template_data.items():
                        setattr(existing_template, key, value)
                    existing_template.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'Updated template: {template_name}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Template already exists: {template_name} (use --overwrite to update)')
                    )
            else:
                # Create new template
                Template.objects.create(**template_data)
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template_name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nSummary:\n'
                f'- Created: {created_count} templates\n'
                f'- Updated: {updated_count} templates'
            )
        )
