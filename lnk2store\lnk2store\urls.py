"""
URL configuration for lnk2store project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    # Web URLs
    path('pages/', include('pages.urls')),
    path('accounts/', include('accounts.urls')),
    path('products/', include('products.urls')),
    path('leads/', include('leads.urls')),
    path('wallet/', include('wallet.urls')),
    path('adminpanel/', include('adminpanel.urls')),
    # API URLs
    path('api/v1/accounts/', include('accounts.api_urls')),
    path('api/v1/products/', include('products.api_urls')),
    path('api/v1/leads/', include('leads.api_urls')),
    path('api/v1/wallet/', include('wallet.api_urls')),
    path('api/v1/pages/', include('pages.api_urls')),
    path('api/v1/templates/', include('pages.template_api_urls')),
    path('api/v1/admin/', include('adminpanel.api_urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
