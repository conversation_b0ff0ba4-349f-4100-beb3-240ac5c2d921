import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Fab,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import { 
  Add, 
  Search, 
  FilterList, 
  Sort, 
  GridView, 
  ViewList,
  Refresh 
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { useApi } from '../hooks/useApi';
import { productsAPI } from '../services/api';
import { useApp } from '../contexts/AppContext';

const ProductsPage = () => {
  const { formatCurrency, isRTL, isDark } = useApp();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [searching, setSearching] = useState(false);
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('created_at');
  const [filterBy, setFilterBy] = useState('all');
  const [sortMenuAnchor, setSortMenuAnchor] = useState(null);
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);

  const { data: productsData, loading, error, refetch } = useApi(() => productsAPI.getProducts());

  const products = productsData?.results || productsData || [];
  
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults(null);
      return;
    }

    setSearching(true);
    try {
      const response = await productsAPI.searchProducts(searchQuery);
      setSearchResults(response.data);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setSearching(false);
    }
  };

  const handleSearchKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const getFilteredAndSortedProducts = () => {
    let filtered = searchResults?.results || products || [];
    
    if (filterBy !== 'all') {
      filtered = filtered.filter(product => {
        switch (filterBy) {
          case 'active':
            return product.status === 'active';
          case 'featured':
            return product.is_featured;
          case 'discounted':
            return product.old_price && product.old_price > product.price;
          default:
            return true;
        }
      });
    }
    
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price_low':
          return a.price - b.price;
        case 'price_high':
          return b.price - a.price;
        case 'created_at':
        default:
          return new Date(b.created_at) - new Date(a.created_at);
      }
    });
    
    return filtered;
  };

  const displayProducts = getFilteredAndSortedProducts();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        حدث خطأ في تحميل المنتجات. يرجى المحاولة مرة أخرى.
        <Button onClick={refetch} sx={{ ml: 2 }}>
          إعادة المحاولة
        </Button>
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 4,
        flexWrap: 'wrap',
        gap: 2
      }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            📦 منتجاتي
          </Typography>
          <Typography variant="body2" color="text.secondary">
            إدارة وعرض منتجاتك ({displayProducts.length} منتج)
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={refetch} color="primary">
            <Refresh />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add />}
            component={Link}
            to="/products/create"
          >
            إضافة منتج
          </Button>
        </Box>
      </Box>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="البحث في المنتجات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleSearchKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                  endAdornment: searching && (
                    <InputAdornment position="end">
                      <CircularProgress size={20} />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <IconButton 
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  color={viewMode === 'grid' ? 'primary' : 'default'}
                >
                  {viewMode === 'grid' ? <ViewList /> : <GridView />}
                </IconButton>
                
                <IconButton 
                  onClick={(e) => setSortMenuAnchor(e.currentTarget)}
                  color="primary"
                >
                  <Sort />
                </IconButton>
                
                <IconButton 
                  onClick={(e) => setFilterMenuAnchor(e.currentTarget)}
                  color="primary"
                >
                  <FilterList />
                </IconButton>
              </Box>
            </Grid>
          </Grid>
          
          {(filterBy !== 'all' || sortBy !== 'created_at') && (
            <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {filterBy !== 'all' && (
                <Chip 
                  label={`التصفية: ${filterBy}`}
                  onDelete={() => setFilterBy('all')}
                  size="small"
                />
              )}
              {sortBy !== 'created_at' && (
                <Chip 
                  label={`الترتيب: ${sortBy}`}
                  onDelete={() => setSortBy('created_at')}
                  size="small"
                />
              )}
            </Box>
          )}
        </CardContent>
      </Card>

      {displayProducts.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              📦 لا توجد منتجات
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              {searchQuery ? 'لم يتم العثور على منتجات تطابق البحث' : 'ابدأ بإضافة منتجك الأول'}
            </Typography>
            {!searchQuery && (
              <Button
                variant="contained"
                startIcon={<Add />}
                component={Link}
                to="/products/create"
                sx={{ mt: 2 }}
              >
                إضافة منتج
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {displayProducts.map((product) => (
            <Grid 
              item 
              xs={12} 
              sm={viewMode === 'list' ? 12 : 6} 
              md={viewMode === 'list' ? 12 : 4} 
              lg={viewMode === 'list' ? 12 : 3} 
              key={product.id}
            >
              <ProductCard product={product} viewMode={viewMode} />
            </Grid>
          ))}
        </Grid>
      )}

      <Menu
        anchorEl={sortMenuAnchor}
        open={Boolean(sortMenuAnchor)}
        onClose={() => setSortMenuAnchor(null)}
      >
        <MenuItem onClick={() => { setSortBy('created_at'); setSortMenuAnchor(null); }}>
          الأحدث أولاً
        </MenuItem>
        <MenuItem onClick={() => { setSortBy('name'); setSortMenuAnchor(null); }}>
          الاسم (أ-ي)
        </MenuItem>
        <MenuItem onClick={() => { setSortBy('price_low'); setSortMenuAnchor(null); }}>
          السعر (الأقل أولاً)
        </MenuItem>
        <MenuItem onClick={() => { setSortBy('price_high'); setSortMenuAnchor(null); }}>
          السعر (الأعلى أولاً)
        </MenuItem>
      </Menu>

      <Menu
        anchorEl={filterMenuAnchor}
        open={Boolean(filterMenuAnchor)}
        onClose={() => setFilterMenuAnchor(null)}
      >
        <MenuItem onClick={() => { setFilterBy('all'); setFilterMenuAnchor(null); }}>
          جميع المنتجات
        </MenuItem>
        <MenuItem onClick={() => { setFilterBy('active'); setFilterMenuAnchor(null); }}>
          المنتجات النشطة
        </MenuItem>
        <MenuItem onClick={() => { setFilterBy('featured'); setFilterMenuAnchor(null); }}>
          المنتجات المميزة
        </MenuItem>
        <MenuItem onClick={() => { setFilterBy('discounted'); setFilterMenuAnchor(null); }}>
          المنتجات المخفضة
        </MenuItem>
      </Menu>

      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: 16,
          [isRTL ? 'left' : 'right']: 16,
        }}
        component={Link}
        to="/products/create"
      >
        <Add />
      </Fab>
    </Box>
  );
};

export default ProductsPage;
