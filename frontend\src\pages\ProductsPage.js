import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Fab,
  CircularProgress
} from '@mui/material';
import { Add, Search } from '@mui/icons-material';
import { Link } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { useApi } from '../hooks/useApi';
import { productsAPI } from '../services/api';

const ProductsPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [searching, setSearching] = useState(false);

  const { data: products, loading } = useApi(() => productsAPI.getProducts());

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults(null);
      return;
    }

    setSearching(true);
    try {
      const response = await productsAPI.searchProducts(searchQuery);
      setSearchResults(response.data);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setSearching(false);
    }
  };

  const handleSearchKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const displayProducts = searchResults?.results || products || [];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4">
          المنتجات
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          component={Link}
          to="/products/create"
        >
          إضافة منتج جديد
        </Button>
      </Box>

      {/* Search */}
      <Box sx={{ mb: 4 }}>
        <TextField
          fullWidth
          placeholder="البحث في المنتجات..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleSearchKeyPress}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searching && (
              <InputAdornment position="end">
                <CircularProgress size={20} />
              </InputAdornment>
            )
          }}
        />
        <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
          <Button onClick={handleSearch} disabled={searching}>
            بحث
          </Button>
          {searchResults && (
            <Button
              onClick={() => {
                setSearchQuery('');
                setSearchResults(null);
              }}
              variant="outlined"
            >
              إلغاء البحث
            </Button>
          )}
        </Box>
      </Box>

      {/* Search Results Info */}
      {searchResults && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" color="text.secondary">
            نتائج البحث عن "{searchResults.query}": {searchResults.count} منتج
          </Typography>
        </Box>
      )}

      {/* Products Grid */}
      {displayProducts.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            {searchResults ? 'لا توجد نتائج للبحث' : 'لا توجد منتجات'}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {searchResults 
              ? 'جرب البحث بكلمات مختلفة' 
              : 'ابدأ بإضافة منتجك الأول'
            }
          </Typography>
          {!searchResults && (
            <Button
              variant="contained"
              startIcon={<Add />}
              component={Link}
              to="/products/create"
            >
              إضافة منتج جديد
            </Button>
          )}
        </Box>
      ) : (
        <Grid container spacing={3}>
          {displayProducts.map((product) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
              <ProductCard product={product} />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        component={Link}
        to="/products/create"
      >
        <Add />
      </Fab>
    </Box>
  );
};

export default ProductsPage;
