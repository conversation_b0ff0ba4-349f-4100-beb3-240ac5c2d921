import requests
import json
from django.conf import settings
from django.utils import timezone
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)

class WhatsAppService:
    """
    WhatsApp Business API integration service
    Supports multiple providers: Twilio, Meta Business API, or custom webhook
    """
    
    def __init__(self):
        self.provider = getattr(settings, 'WHATSAPP_PROVIDER', 'twilio')
        self.api_key = getattr(settings, 'WHATSAPP_API_KEY', '')
        self.api_secret = getattr(settings, 'WHATSAPP_API_SECRET', '')
        self.phone_number_id = getattr(settings, 'WHATSAPP_PHONE_NUMBER_ID', '')
        self.webhook_url = getattr(settings, 'WHATSAPP_WEBHOOK_URL', '')
        
    def send_lead_notification(self, lead, user_phone: str) -> Dict:
        """
        Send lead notification to business owner via WhatsApp
        
        Args:
            lead: Lead instance
            user_phone: Business owner's WhatsApp number
            
        Returns:
            Dict with success status and message
        """
        try:
            message = self._format_lead_message(lead)
            
            if self.provider == 'twilio':
                return self._send_via_twilio(user_phone, message)
            elif self.provider == 'meta':
                return self._send_via_meta(user_phone, message)
            elif self.provider == 'webhook':
                return self._send_via_webhook(user_phone, message, lead)
            else:
                return self._simulate_send(user_phone, message)
                
        except Exception as e:
            logger.error(f"WhatsApp send error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'provider': self.provider
            }
    
    def _format_lead_message(self, lead) -> str:
        """Format lead data into WhatsApp message"""
        product_info = f"المنتج: {lead.product.name}" if lead.product else "المنتج: غير محدد"
        
        message = f"""
🔔 *طلب جديد من Lnk2Store*

👤 *الاسم:* {lead.name}
📱 *الهاتف:* {lead.phone_number}
📧 *البريد:* {lead.email or 'غير متوفر'}
🛍️ *{product_info}*

💬 *الرسالة:*
{lead.message or 'لا توجد رسالة إضافية'}

⏰ *وقت الطلب:* {lead.created_at.strftime('%Y-%m-%d %H:%M')}

---
تم إرسال هذا الطلب تلقائياً من منصة Lnk2Store
        """.strip()
        
        return message
    
    def _send_via_twilio(self, to_phone: str, message: str) -> Dict:
        """Send via Twilio WhatsApp API"""
        if not self.api_key or not self.api_secret:
            return {'success': False, 'error': 'Twilio credentials not configured'}
        
        url = f"https://api.twilio.com/2010-04-01/Accounts/{self.api_key}/Messages.json"
        
        data = {
            'From': f'whatsapp:{self.phone_number_id}',
            'To': f'whatsapp:{to_phone}',
            'Body': message
        }
        
        response = requests.post(
            url,
            data=data,
            auth=(self.api_key, self.api_secret)
        )
        
        if response.status_code == 201:
            return {
                'success': True,
                'message_id': response.json().get('sid'),
                'provider': 'twilio'
            }
        else:
            return {
                'success': False,
                'error': response.text,
                'provider': 'twilio'
            }
    
    def _send_via_meta(self, to_phone: str, message: str) -> Dict:
        """Send via Meta WhatsApp Business API"""
        if not self.api_key or not self.phone_number_id:
            return {'success': False, 'error': 'Meta WhatsApp credentials not configured'}
        
        url = f"https://graph.facebook.com/v18.0/{self.phone_number_id}/messages"
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'messaging_product': 'whatsapp',
            'to': to_phone,
            'type': 'text',
            'text': {'body': message}
        }
        
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 200:
            return {
                'success': True,
                'message_id': response.json().get('messages', [{}])[0].get('id'),
                'provider': 'meta'
            }
        else:
            return {
                'success': False,
                'error': response.text,
                'provider': 'meta'
            }
    
    def _send_via_webhook(self, to_phone: str, message: str, lead) -> Dict:
        """Send via custom webhook"""
        if not self.webhook_url:
            return {'success': False, 'error': 'Webhook URL not configured'}
        
        data = {
            'to_phone': to_phone,
            'message': message,
            'lead_data': {
                'id': lead.id,
                'name': lead.name,
                'phone': lead.phone_number,
                'email': lead.email,
                'product': lead.product.name if lead.product else None,
                'created_at': lead.created_at.isoformat()
            },
            'timestamp': timezone.now().isoformat()
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}' if self.api_key else None
        }
        
        response = requests.post(
            self.webhook_url,
            json=data,
            headers={k: v for k, v in headers.items() if v}
        )
        
        if response.status_code in [200, 201]:
            return {
                'success': True,
                'webhook_response': response.json() if response.content else {},
                'provider': 'webhook'
            }
        else:
            return {
                'success': False,
                'error': response.text,
                'provider': 'webhook'
            }
    
    def _simulate_send(self, to_phone: str, message: str) -> Dict:
        """Simulate sending for development/testing"""
        logger.info(f"SIMULATED WhatsApp to {to_phone}:\n{message}")
        
        return {
            'success': True,
            'message_id': f'sim_{timezone.now().timestamp()}',
            'provider': 'simulation',
            'note': 'This is a simulated send for development'
        }

# Convenience function
def send_lead_to_whatsapp(lead, user_phone: str) -> Dict:
    """
    Convenience function to send lead notification
    
    Usage:
        result = send_lead_to_whatsapp(lead, '+966501234567')
        if result['success']:
            print("WhatsApp sent successfully")
    """
    service = WhatsAppService()
    return service.send_lead_notification(lead, user_phone)
