{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Button, Container, Grid, Card, CardContent, CardActions } from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport ProductCard from '../components/ProductCard';\nimport { useApi } from '../hooks/useApi';\nimport { productsAPI } from '../services/api';\nimport Logo from '../components/Logo';\nimport VideoBackground from '../components/VideoBackground';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const {\n    data: products,\n    loading\n  } = useApi(() => productsAPI.getProducts());\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        color: 'white',\n        py: 8,\n        textAlign: 'center',\n        mb: 6,\n        overflow: 'hidden',\n        minHeight: '500px',\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        component: \"video\",\n        autoPlay: true,\n        muted: true,\n        loop: true,\n        playsInline: true,\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover',\n          zIndex: -2\n        },\n        children: /*#__PURE__*/_jsxDEV(\"source\", {\n          src: \"/logo_video.mp4\",\n          type: \"video/mp4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          background: 'linear-gradient(45deg, rgba(33, 150, 243, 0.8) 30%, rgba(33, 203, 243, 0.8) 90%)',\n          zIndex: -1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"md\",\n        sx: {\n          position: 'relative',\n          zIndex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Logo, {\n            variant: \"light\",\n            height: \"80px\",\n            linkTo: null\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h2\",\n          component: \"h1\",\n          gutterBottom: true,\n          sx: {\n            textShadow: '2px 2px 4px rgba(0,0,0,0.5)',\n            fontWeight: 'bold'\n          },\n          children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A Lnk2Store\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            mb: 4,\n            opacity: 0.95,\n            textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n          },\n          children: \"\\u0645\\u0646\\u0635\\u0629 SaaS \\u0644\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0635\\u0641\\u062D\\u0627\\u062A \\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u0627\\u062D\\u062A\\u0631\\u0627\\u0641\\u064A\\u0629 \\u0645\\u0639 \\u0646\\u0638\\u0627\\u0645 \\u062C\\u0645\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), !isAuthenticated ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            component: Link,\n            to: \"/register\",\n            sx: {\n              bgcolor: 'white',\n              color: 'primary.main'\n            },\n            children: \"\\u0627\\u0628\\u062F\\u0623 \\u0627\\u0644\\u0622\\u0646 \\u0645\\u062C\\u0627\\u0646\\u0627\\u064B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            component: Link,\n            to: \"/login\",\n            sx: {\n              borderColor: 'white',\n              color: 'white'\n            },\n            children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"large\",\n          component: Link,\n          to: \"/dashboard\",\n          sx: {\n            bgcolor: 'white',\n            color: 'primary.main'\n          },\n          children: \"\\u0627\\u0646\\u062A\\u0642\\u0644 \\u0625\\u0644\\u0649 \\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          children: \"\\u0627\\u0644\\u0645\\u0632\\u0627\\u064A\\u0627 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83D\\uDCB0 \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u062F\\u0641\\u0639 \\u0644\\u0643\\u0644 \\u0637\\u0644\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u062F\\u0641\\u0639 \\u0641\\u0642\\u0637 \\u0645\\u0642\\u0627\\u0628\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u062A\\u064A \\u062A\\u0633\\u062A\\u0642\\u0628\\u0644\\u0647\\u0627. \\u0646\\u0638\\u0627\\u0645 \\u0639\\u0627\\u062F\\u0644 \\u0648\\u0634\\u0641\\u0627\\u0641.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83D\\uDCF1 \\u0625\\u0631\\u0633\\u0627\\u0644 \\u0641\\u0648\\u0631\\u064A \\u0644\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u0633\\u062A\\u0642\\u0628\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A \\u0645\\u0628\\u0627\\u0634\\u0631\\u0629 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628 \\u0641\\u0648\\u0631 \\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u0631\\u0635\\u064A\\u062F.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83C\\uDFA8 \\u0642\\u0648\\u0627\\u0644\\u0628 \\u062C\\u0627\\u0647\\u0632\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0645\\u0646 \\u0645\\u062C\\u0645\\u0648\\u0639\\u0629 \\u0642\\u0648\\u0627\\u0644\\u0628 \\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u0627\\u062D\\u062A\\u0631\\u0627\\u0641\\u064A\\u0629 \\u0642\\u0627\\u0628\\u0644\\u0629 \\u0644\\u0644\\u062A\\u062E\\u0635\\u064A\\u0635.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), products && products.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          children: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0645\\u064A\\u0632\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          sx: {\n            mt: 2\n          },\n          children: products.slice(0, 6).map(product => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(ProductCard, {\n              product: product\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 19\n            }, this)\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            component: Link,\n            to: \"/products\",\n            children: \"\\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          bgcolor: 'grey.100',\n          p: 6,\n          borderRadius: 2,\n          textAlign: 'center',\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          children: \"\\u062C\\u0627\\u0647\\u0632 \\u0644\\u0644\\u0628\\u062F\\u0621\\u061F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mb: 3\n          },\n          children: \"\\u0627\\u0646\\u0636\\u0645 \\u0625\\u0644\\u0649 \\u0622\\u0644\\u0627\\u0641 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631 \\u0627\\u0644\\u0630\\u064A\\u0646 \\u064A\\u0633\\u062A\\u062E\\u062F\\u0645\\u0648\\u0646 Lnk2Store \\u0644\\u062A\\u0646\\u0645\\u064A\\u0629 \\u0623\\u0639\\u0645\\u0627\\u0644\\u0647\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"large\",\n          component: Link,\n          to: \"/register\",\n          children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628 \\u0645\\u062C\\u0627\\u0646\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"lBTdndtKRF+Sc2sQ09iVtGMQeZg=\", false, function () {\n  return [useAuth, useApi];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Link", "useAuth", "ProductCard", "useApi", "productsAPI", "Logo", "VideoBackground", "jsxDEV", "_jsxDEV", "HomePage", "_s", "isAuthenticated", "data", "products", "loading", "getProducts", "children", "sx", "position", "color", "py", "textAlign", "mb", "overflow", "minHeight", "display", "alignItems", "component", "autoPlay", "muted", "loop", "playsInline", "top", "left", "width", "height", "objectFit", "zIndex", "src", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "max<PERSON><PERSON><PERSON>", "justifyContent", "variant", "linkTo", "gutterBottom", "textShadow", "fontWeight", "opacity", "gap", "size", "to", "bgcolor", "borderColor", "container", "spacing", "mt", "item", "xs", "md", "length", "slice", "map", "product", "sm", "id", "p", "borderRadius", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON>,\n  <PERSON>po<PERSON>,\n  Button,\n  Container,\n  Grid,\n  Card,\n  CardContent,\n  CardActions\n} from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport ProductCard from '../components/ProductCard';\nimport { useApi } from '../hooks/useApi';\nimport { productsAPI } from '../services/api';\nimport Logo from '../components/Logo';\nimport VideoBackground from '../components/VideoBackground';\n\nconst HomePage = () => {\n  const { isAuthenticated } = useAuth();\n  const { data: products, loading } = useApi(() => productsAPI.getProducts());\n\n  return (\n    <Box>\n      {/* Hero Section */}\n      <Box\n        sx={{\n          position: 'relative',\n          color: 'white',\n          py: 8,\n          textAlign: 'center',\n          mb: 6,\n          overflow: 'hidden',\n          minHeight: '500px',\n          display: 'flex',\n          alignItems: 'center'\n        }}\n      >\n        {/* Background Video */}\n        <Box\n          component=\"video\"\n          autoPlay\n          muted\n          loop\n          playsInline\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            zIndex: -2\n          }}\n        >\n          <source src=\"/logo_video.mp4\" type=\"video/mp4\" />\n        </Box>\n\n        {/* Overlay for better text readability */}\n        <Box\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            background: 'linear-gradient(45deg, rgba(33, 150, 243, 0.8) 30%, rgba(33, 203, 243, 0.8) 90%)',\n            zIndex: -1\n          }}\n        />\n        <Container maxWidth=\"md\" sx={{ position: 'relative', zIndex: 1 }}>\n          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>\n            <Logo variant=\"light\" height=\"80px\" linkTo={null} />\n          </Box>\n          <Typography\n            variant=\"h2\"\n            component=\"h1\"\n            gutterBottom\n            sx={{\n              textShadow: '2px 2px 4px rgba(0,0,0,0.5)',\n              fontWeight: 'bold'\n            }}\n          >\n            مرحباً بك في Lnk2Store\n          </Typography>\n          <Typography\n            variant=\"h5\"\n            sx={{\n              mb: 4,\n              opacity: 0.95,\n              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n            }}\n          >\n            منصة SaaS لإنشاء صفحات تسويقية احترافية مع نظام جمع الطلبات\n          </Typography>\n          {!isAuthenticated ? (\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>\n              <Button\n                variant=\"contained\"\n                size=\"large\"\n                component={Link}\n                to=\"/register\"\n                sx={{ bgcolor: 'white', color: 'primary.main' }}\n              >\n                ابدأ الآن مجاناً\n              </Button>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                component={Link}\n                to=\"/login\"\n                sx={{ borderColor: 'white', color: 'white' }}\n              >\n                تسجيل الدخول\n              </Button>\n            </Box>\n          ) : (\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              component={Link}\n              to=\"/dashboard\"\n              sx={{ bgcolor: 'white', color: 'primary.main' }}\n            >\n              انتقل إلى لوحة التحكم\n            </Button>\n          )}\n        </Container>\n      </Box>\n\n      <Container maxWidth=\"lg\">\n        {/* Features Section */}\n        <Box sx={{ mb: 8 }}>\n          <Typography variant=\"h3\" textAlign=\"center\" gutterBottom>\n            المزايا الرئيسية\n          </Typography>\n          <Grid container spacing={4} sx={{ mt: 2 }}>\n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    💰 نظام الدفع لكل طلب\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    ادفع فقط مقابل الطلبات التي تستقبلها. نظام عادل وشفاف.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    📱 إرسال فوري للواتساب\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    استقبل الطلبات مباشرة على الواتساب فور تأكيد الرصيد.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    🎨 قوالب جاهزة\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    اختر من مجموعة قوالب تسويقية احترافية قابلة للتخصيص.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Products Preview */}\n        {products && products.length > 0 && (\n          <Box sx={{ mb: 8 }}>\n            <Typography variant=\"h3\" textAlign=\"center\" gutterBottom>\n              منتجات مميزة\n            </Typography>\n            <Grid container spacing={3} sx={{ mt: 2 }}>\n              {products.slice(0, 6).map((product) => (\n                <Grid item xs={12} sm={6} md={4} key={product.id}>\n                  <ProductCard product={product} />\n                </Grid>\n              ))}\n            </Grid>\n            <Box sx={{ textAlign: 'center', mt: 4 }}>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                component={Link}\n                to=\"/products\"\n              >\n                عرض جميع المنتجات\n              </Button>\n            </Box>\n          </Box>\n        )}\n\n        {/* CTA Section */}\n        <Box\n          sx={{\n            bgcolor: 'grey.100',\n            p: 6,\n            borderRadius: 2,\n            textAlign: 'center',\n            mb: 4\n          }}\n        >\n          <Typography variant=\"h4\" gutterBottom>\n            جاهز للبدء؟\n          </Typography>\n          <Typography variant=\"body1\" sx={{ mb: 3 }}>\n            انضم إلى آلاف التجار الذين يستخدمون Lnk2Store لتنمية أعمالهم\n          </Typography>\n          {!isAuthenticated && (\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              component={Link}\n              to=\"/register\"\n            >\n              إنشاء حساب مجاني\n            </Button>\n          )}\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,QACN,eAAe;AACtB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,eAAe,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAgB,CAAC,GAAGV,OAAO,CAAC,CAAC;EACrC,MAAM;IAAEW,IAAI,EAAEC,QAAQ;IAAEC;EAAQ,CAAC,GAAGX,MAAM,CAAC,MAAMC,WAAW,CAACW,WAAW,CAAC,CAAC,CAAC;EAE3E,oBACEP,OAAA,CAAChB,GAAG;IAAAwB,QAAA,gBAEFR,OAAA,CAAChB,GAAG;MACFyB,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,OAAO;QACdC,EAAE,EAAE,CAAC;QACLC,SAAS,EAAE,QAAQ;QACnBC,EAAE,EAAE,CAAC;QACLC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,OAAO;QAClBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE;MACd,CAAE;MAAAV,QAAA,gBAGFR,OAAA,CAAChB,GAAG;QACFmC,SAAS,EAAC,OAAO;QACjBC,QAAQ;QACRC,KAAK;QACLC,IAAI;QACJC,WAAW;QACXd,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBc,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,SAAS,EAAE,OAAO;UAClBC,MAAM,EAAE,CAAC;QACX,CAAE;QAAArB,QAAA,eAEFR,OAAA;UAAQ8B,GAAG,EAAC,iBAAiB;UAACC,IAAI,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eAGNnC,OAAA,CAAChB,GAAG;QACFyB,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBc,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdS,UAAU,EAAE,kFAAkF;UAC9FP,MAAM,EAAE,CAAC;QACX;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFnC,OAAA,CAACb,SAAS;QAACkD,QAAQ,EAAC,IAAI;QAAC5B,EAAE,EAAE;UAAEC,QAAQ,EAAE,UAAU;UAAEmB,MAAM,EAAE;QAAE,CAAE;QAAArB,QAAA,gBAC/DR,OAAA,CAAChB,GAAG;UAACyB,EAAE,EAAE;YAAEK,EAAE,EAAE,CAAC;YAAEG,OAAO,EAAE,MAAM;YAAEqB,cAAc,EAAE;UAAS,CAAE;UAAA9B,QAAA,eAC5DR,OAAA,CAACH,IAAI;YAAC0C,OAAO,EAAC,OAAO;YAACZ,MAAM,EAAC,MAAM;YAACa,MAAM,EAAE;UAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNnC,OAAA,CAACf,UAAU;UACTsD,OAAO,EAAC,IAAI;UACZpB,SAAS,EAAC,IAAI;UACdsB,YAAY;UACZhC,EAAE,EAAE;YACFiC,UAAU,EAAE,6BAA6B;YACzCC,UAAU,EAAE;UACd,CAAE;UAAAnC,QAAA,EACH;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;UACTsD,OAAO,EAAC,IAAI;UACZ9B,EAAE,EAAE;YACFK,EAAE,EAAE,CAAC;YACL8B,OAAO,EAAE,IAAI;YACbF,UAAU,EAAE;UACd,CAAE;UAAAlC,QAAA,EACH;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ,CAAChC,eAAe,gBACfH,OAAA,CAAChB,GAAG;UAACyB,EAAE,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAE4B,GAAG,EAAE,CAAC;YAAEP,cAAc,EAAE;UAAS,CAAE;UAAA9B,QAAA,gBAC7DR,OAAA,CAACd,MAAM;YACLqD,OAAO,EAAC,WAAW;YACnBO,IAAI,EAAC,OAAO;YACZ3B,SAAS,EAAE3B,IAAK;YAChBuD,EAAE,EAAC,WAAW;YACdtC,EAAE,EAAE;cAAEuC,OAAO,EAAE,OAAO;cAAErC,KAAK,EAAE;YAAe,CAAE;YAAAH,QAAA,EACjD;UAED;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnC,OAAA,CAACd,MAAM;YACLqD,OAAO,EAAC,UAAU;YAClBO,IAAI,EAAC,OAAO;YACZ3B,SAAS,EAAE3B,IAAK;YAChBuD,EAAE,EAAC,QAAQ;YACXtC,EAAE,EAAE;cAAEwC,WAAW,EAAE,OAAO;cAAEtC,KAAK,EAAE;YAAQ,CAAE;YAAAH,QAAA,EAC9C;UAED;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENnC,OAAA,CAACd,MAAM;UACLqD,OAAO,EAAC,WAAW;UACnBO,IAAI,EAAC,OAAO;UACZ3B,SAAS,EAAE3B,IAAK;UAChBuD,EAAE,EAAC,YAAY;UACftC,EAAE,EAAE;YAAEuC,OAAO,EAAE,OAAO;YAAErC,KAAK,EAAE;UAAe,CAAE;UAAAH,QAAA,EACjD;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAENnC,OAAA,CAACb,SAAS;MAACkD,QAAQ,EAAC,IAAI;MAAA7B,QAAA,gBAEtBR,OAAA,CAAChB,GAAG;QAACyB,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACjBR,OAAA,CAACf,UAAU;UAACsD,OAAO,EAAC,IAAI;UAAC1B,SAAS,EAAC,QAAQ;UAAC4B,YAAY;UAAAjC,QAAA,EAAC;QAEzD;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACZ,IAAI;UAAC8D,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC1C,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACxCR,OAAA,CAACZ,IAAI;YAACiE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/C,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACoB,EAAE,EAAE;gBAAEkB,MAAM,EAAE,MAAM;gBAAEd,SAAS,EAAE;cAAS,CAAE;cAAAL,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,IAAI;kBAACE,YAAY;kBAAC9B,KAAK,EAAC,SAAS;kBAAAH,QAAA,EAAC;gBAEtD;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,OAAO;kBAAA/B,QAAA,EAAC;gBAE5B;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPnC,OAAA,CAACZ,IAAI;YAACiE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/C,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACoB,EAAE,EAAE;gBAAEkB,MAAM,EAAE,MAAM;gBAAEd,SAAS,EAAE;cAAS,CAAE;cAAAL,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,IAAI;kBAACE,YAAY;kBAAC9B,KAAK,EAAC,SAAS;kBAAAH,QAAA,EAAC;gBAEtD;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,OAAO;kBAAA/B,QAAA,EAAC;gBAE5B;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPnC,OAAA,CAACZ,IAAI;YAACiE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/C,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACoB,EAAE,EAAE;gBAAEkB,MAAM,EAAE,MAAM;gBAAEd,SAAS,EAAE;cAAS,CAAE;cAAAL,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,IAAI;kBAACE,YAAY;kBAAC9B,KAAK,EAAC,SAAS;kBAAAH,QAAA,EAAC;gBAEtD;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;kBAACsD,OAAO,EAAC,OAAO;kBAAA/B,QAAA,EAAC;gBAE5B;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGL9B,QAAQ,IAAIA,QAAQ,CAACmD,MAAM,GAAG,CAAC,iBAC9BxD,OAAA,CAAChB,GAAG;QAACyB,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACjBR,OAAA,CAACf,UAAU;UAACsD,OAAO,EAAC,IAAI;UAAC1B,SAAS,EAAC,QAAQ;UAAC4B,YAAY;UAAAjC,QAAA,EAAC;QAEzD;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACZ,IAAI;UAAC8D,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC1C,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAA5C,QAAA,EACvCH,QAAQ,CAACoD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,OAAO,iBAChC3D,OAAA,CAACZ,IAAI;YAACiE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACM,EAAE,EAAE,CAAE;YAACL,EAAE,EAAE,CAAE;YAAA/C,QAAA,eAC9BR,OAAA,CAACN,WAAW;cAACiE,OAAO,EAAEA;YAAQ;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADGwB,OAAO,CAACE,EAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE1C,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPnC,OAAA,CAAChB,GAAG;UAACyB,EAAE,EAAE;YAAEI,SAAS,EAAE,QAAQ;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAA5C,QAAA,eACtCR,OAAA,CAACd,MAAM;YACLqD,OAAO,EAAC,UAAU;YAClBO,IAAI,EAAC,OAAO;YACZ3B,SAAS,EAAE3B,IAAK;YAChBuD,EAAE,EAAC,WAAW;YAAAvC,QAAA,EACf;UAED;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDnC,OAAA,CAAChB,GAAG;QACFyB,EAAE,EAAE;UACFuC,OAAO,EAAE,UAAU;UACnBc,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,CAAC;UACflD,SAAS,EAAE,QAAQ;UACnBC,EAAE,EAAE;QACN,CAAE;QAAAN,QAAA,gBAEFR,OAAA,CAACf,UAAU;UAACsD,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAjC,QAAA,EAAC;QAEtC;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnC,OAAA,CAACf,UAAU;UAACsD,OAAO,EAAC,OAAO;UAAC9B,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAE3C;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ,CAAChC,eAAe,iBACfH,OAAA,CAACd,MAAM;UACLqD,OAAO,EAAC,WAAW;UACnBO,IAAI,EAAC,OAAO;UACZ3B,SAAS,EAAE3B,IAAK;UAChBuD,EAAE,EAAC,WAAW;UAAAvC,QAAA,EACf;QAED;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACjC,EAAA,CAxNID,QAAQ;EAAA,QACgBR,OAAO,EACCE,MAAM;AAAA;AAAAqE,EAAA,GAFtC/D,QAAQ;AA0Nd,eAAeA,QAAQ;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}