import React, { useState } from 'react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Link as MuiLink
} from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

const RegisterPage = () => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    password_confirm: '',
    first_name: '',
    last_name: '',
    phone_number: '',
    business_type: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { register } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Client-side validation
    if (formData.password !== formData.password_confirm) {
      setError('كلمات المرور غير متطابقة');
      setLoading(false);
      return;
    }

    const result = await register(formData);

    if (result.success) {
      navigate('/dashboard');
    } else {
      setError(result.error);
    }

    setLoading(false);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '80vh'
      }}
    >
      <Paper elevation={3} sx={{ p: 4, maxWidth: 500, width: '100%' }}>
        <Typography variant="h4" textAlign="center" gutterBottom>
          إنشاء حساب جديد
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit}>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              fullWidth
              label="الاسم الأول"
              name="first_name"
              value={formData.first_name}
              onChange={handleChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="الاسم الأخير"
              name="last_name"
              value={formData.last_name}
              onChange={handleChange}
              margin="normal"
            />
          </Box>

          <TextField
            fullWidth
            label="اسم المستخدم *"
            name="username"
            value={formData.username}
            onChange={handleChange}
            required
            margin="normal"
          />

          <TextField
            fullWidth
            label="البريد الإلكتروني *"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            required
            margin="normal"
          />

          <TextField
            fullWidth
            label="رقم الهاتف"
            name="phone_number"
            value={formData.phone_number}
            onChange={handleChange}
            margin="normal"
          />

          <TextField
            fullWidth
            label="نوع النشاط التجاري"
            name="business_type"
            value={formData.business_type}
            onChange={handleChange}
            margin="normal"
            placeholder="مثال: ملابس، إلكترونيات، طعام..."
          />

          <TextField
            fullWidth
            label="كلمة المرور *"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleChange}
            required
            margin="normal"
          />

          <TextField
            fullWidth
            label="تأكيد كلمة المرور *"
            name="password_confirm"
            type="password"
            value={formData.password_confirm}
            onChange={handleChange}
            required
            margin="normal"
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={loading}
            sx={{ mt: 3, mb: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : 'إنشاء الحساب'}
          </Button>

          <Box textAlign="center">
            <Typography variant="body2">
              لديك حساب بالفعل؟{' '}
              <MuiLink component={Link} to="/login">
                تسجيل الدخول
              </MuiLink>
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default RegisterPage;
