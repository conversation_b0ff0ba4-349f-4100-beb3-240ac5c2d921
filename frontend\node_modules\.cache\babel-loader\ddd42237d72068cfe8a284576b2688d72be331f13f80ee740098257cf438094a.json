{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\ProductCard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Card, CardMedia, CardContent, CardActions, Typography, Button, Chip, Box, IconButton, Badge } from '@mui/material';\nimport { Edit, Delete, Visibility, Star, StarBorder, LocalOffer } from '@mui/icons-material';\nimport { Link } from 'react-router-dom';\nimport { useApp } from '../contexts/AppContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product,\n  showActions = true,\n  viewMode = 'grid'\n}) => {\n  _s();\n  var _product$description;\n  const {\n    formatCurrency,\n    isDark\n  } = useApp();\n  const defaultImage = '/placeholder-product.jpg';\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n      component: \"img\",\n      height: \"200\",\n      image: product.image || defaultImage,\n      alt: product.name,\n      sx: {\n        objectFit: 'cover'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        flexGrow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        gutterBottom: true,\n        variant: \"h6\",\n        component: \"div\",\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: ((_product$description = product.description) === null || _product$description === void 0 ? void 0 : _product$description.length) > 100 ? `${product.description.substring(0, 100)}...` : product.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"primary\",\n          children: [product.price, \" \\u0631\\u064A\\u0627\\u0644\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), product.colors && product.colors.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 0.5\n          },\n          children: [product.colors.slice(0, 3).map((color, index) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: color.color,\n            size: \"small\",\n            variant: \"outlined\"\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this)), product.colors.length > 3 && /*#__PURE__*/_jsxDEV(Chip, {\n            label: `+${product.colors.length - 3}`,\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), product.sizes && product.sizes.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 1,\n          display: 'flex',\n          gap: 0.5,\n          flexWrap: 'wrap'\n        },\n        children: [product.sizes.slice(0, 4).map((size, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: size.size,\n          size: \"small\",\n          variant: \"outlined\"\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 15\n        }, this)), product.sizes.length > 4 && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `+${product.sizes.length - 4}`,\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), showActions && /*#__PURE__*/_jsxDEV(CardActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        component: Link,\n        to: `/product/${product.id}`,\n        children: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        variant: \"contained\",\n        component: Link,\n        to: `/product/${product.id}/order`,\n        children: \"\\u0627\\u0637\\u0644\\u0628 \\u0627\\u0644\\u0622\\u0646\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductCard, \"ZkhKVLpZhrJ8LD1H1zdmn7Yj1ek=\", false, function () {\n  return [useApp];\n});\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Typography", "<PERSON><PERSON>", "Chip", "Box", "IconButton", "Badge", "Edit", "Delete", "Visibility", "Star", "StarBorder", "LocalOffer", "Link", "useApp", "jsxDEV", "_jsxDEV", "ProductCard", "product", "showActions", "viewMode", "_s", "_product$description", "formatCurrency", "isDark", "defaultImage", "sx", "height", "display", "flexDirection", "children", "component", "image", "alt", "name", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexGrow", "gutterBottom", "variant", "color", "mb", "description", "length", "substring", "justifyContent", "alignItems", "price", "colors", "gap", "slice", "map", "index", "label", "size", "sizes", "mt", "flexWrap", "to", "id", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/ProductCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Card,\n  CardMedia,\n  CardContent,\n  CardActions,\n  Typography,\n  Button,\n  Chip,\n  Box,\n  IconButton,\n  Badge\n} from '@mui/material';\nimport {\n  Edit,\n  Delete,\n  Visibility,\n  Star,\n  StarBorder,\n  LocalOffer\n} from '@mui/icons-material';\nimport { Link } from 'react-router-dom';\nimport { useApp } from '../contexts/AppContext';\n\nconst ProductCard = ({ product, showActions = true, viewMode = 'grid' }) => {\n  const { formatCurrency, isDark } = useApp();\n  const defaultImage = '/placeholder-product.jpg';\n\n  return (\n    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <CardMedia\n        component=\"img\"\n        height=\"200\"\n        image={product.image || defaultImage}\n        alt={product.name}\n        sx={{ objectFit: 'cover' }}\n      />\n      \n      <CardContent sx={{ flexGrow: 1 }}>\n        <Typography gutterBottom variant=\"h6\" component=\"div\">\n          {product.name}\n        </Typography>\n        \n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          {product.description?.length > 100 \n            ? `${product.description.substring(0, 100)}...` \n            : product.description\n          }\n        </Typography>\n        \n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\" color=\"primary\">\n            {product.price} ريال\n          </Typography>\n          \n          {product.colors && product.colors.length > 0 && (\n            <Box sx={{ display: 'flex', gap: 0.5 }}>\n              {product.colors.slice(0, 3).map((color, index) => (\n                <Chip \n                  key={index} \n                  label={color.color} \n                  size=\"small\" \n                  variant=\"outlined\" \n                />\n              ))}\n              {product.colors.length > 3 && (\n                <Chip label={`+${product.colors.length - 3}`} size=\"small\" />\n              )}\n            </Box>\n          )}\n        </Box>\n        \n        {product.sizes && product.sizes.length > 0 && (\n          <Box sx={{ mt: 1, display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>\n            {product.sizes.slice(0, 4).map((size, index) => (\n              <Chip \n                key={index} \n                label={size.size} \n                size=\"small\" \n                variant=\"outlined\" \n              />\n            ))}\n            {product.sizes.length > 4 && (\n              <Chip label={`+${product.sizes.length - 4}`} size=\"small\" />\n            )}\n          </Box>\n        )}\n      </CardContent>\n      \n      {showActions && (\n        <CardActions>\n          <Button \n            size=\"small\" \n            component={Link} \n            to={`/product/${product.id}`}\n          >\n            عرض التفاصيل\n          </Button>\n          <Button \n            size=\"small\" \n            variant=\"contained\" \n            component={Link} \n            to={`/product/${product.id}/order`}\n          >\n            اطلب الآن\n          </Button>\n        </CardActions>\n      )}\n    </Card>\n  );\n};\n\nexport default ProductCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAC5B,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,WAAW,GAAG,IAAI;EAAEC,QAAQ,GAAG;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EAC1E,MAAM;IAAEC,cAAc;IAAEC;EAAO,CAAC,GAAGV,MAAM,CAAC,CAAC;EAC3C,MAAMW,YAAY,GAAG,0BAA0B;EAE/C,oBACET,OAAA,CAACnB,IAAI;IAAC6B,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACrEd,OAAA,CAAClB,SAAS;MACRiC,SAAS,EAAC,KAAK;MACfJ,MAAM,EAAC,KAAK;MACZK,KAAK,EAAEd,OAAO,CAACc,KAAK,IAAIP,YAAa;MACrCQ,GAAG,EAAEf,OAAO,CAACgB,IAAK;MAClBR,EAAE,EAAE;QAAES,SAAS,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFvB,OAAA,CAACjB,WAAW;MAAC2B,EAAE,EAAE;QAAEc,QAAQ,EAAE;MAAE,CAAE;MAAAV,QAAA,gBAC/Bd,OAAA,CAACf,UAAU;QAACwC,YAAY;QAACC,OAAO,EAAC,IAAI;QAACX,SAAS,EAAC,KAAK;QAAAD,QAAA,EAClDZ,OAAO,CAACgB;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEbvB,OAAA,CAACf,UAAU;QAACyC,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAACjB,EAAE,EAAE;UAAEkB,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,EAC9D,EAAAR,oBAAA,GAAAJ,OAAO,CAAC2B,WAAW,cAAAvB,oBAAA,uBAAnBA,oBAAA,CAAqBwB,MAAM,IAAG,GAAG,GAC9B,GAAG5B,OAAO,CAAC2B,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAC7C7B,OAAO,CAAC2B;MAAW;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEb,CAAC,eAEbvB,OAAA,CAACZ,GAAG;QAACsB,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEoB,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAnB,QAAA,gBAClFd,OAAA,CAACf,UAAU;UAACyC,OAAO,EAAC,IAAI;UAACC,KAAK,EAAC,SAAS;UAAAb,QAAA,GACrCZ,OAAO,CAACgC,KAAK,EAAC,2BACjB;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZrB,OAAO,CAACiC,MAAM,IAAIjC,OAAO,CAACiC,MAAM,CAACL,MAAM,GAAG,CAAC,iBAC1C9B,OAAA,CAACZ,GAAG;UAACsB,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEwB,GAAG,EAAE;UAAI,CAAE;UAAAtB,QAAA,GACpCZ,OAAO,CAACiC,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACX,KAAK,EAAEY,KAAK,kBAC3CvC,OAAA,CAACb,IAAI;YAEHqD,KAAK,EAAEb,KAAK,CAACA,KAAM;YACnBc,IAAI,EAAC,OAAO;YACZf,OAAO,EAAC;UAAU,GAHba,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIX,CACF,CAAC,EACDrB,OAAO,CAACiC,MAAM,CAACL,MAAM,GAAG,CAAC,iBACxB9B,OAAA,CAACb,IAAI;YAACqD,KAAK,EAAE,IAAItC,OAAO,CAACiC,MAAM,CAACL,MAAM,GAAG,CAAC,EAAG;YAACW,IAAI,EAAC;UAAO;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC7D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELrB,OAAO,CAACwC,KAAK,IAAIxC,OAAO,CAACwC,KAAK,CAACZ,MAAM,GAAG,CAAC,iBACxC9B,OAAA,CAACZ,GAAG;QAACsB,EAAE,EAAE;UAAEiC,EAAE,EAAE,CAAC;UAAE/B,OAAO,EAAE,MAAM;UAAEwB,GAAG,EAAE,GAAG;UAAEQ,QAAQ,EAAE;QAAO,CAAE;QAAA9B,QAAA,GAC7DZ,OAAO,CAACwC,KAAK,CAACL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACG,IAAI,EAAEF,KAAK,kBACzCvC,OAAA,CAACb,IAAI;UAEHqD,KAAK,EAAEC,IAAI,CAACA,IAAK;UACjBA,IAAI,EAAC,OAAO;UACZf,OAAO,EAAC;QAAU,GAHba,KAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIX,CACF,CAAC,EACDrB,OAAO,CAACwC,KAAK,CAACZ,MAAM,GAAG,CAAC,iBACvB9B,OAAA,CAACb,IAAI;UAACqD,KAAK,EAAE,IAAItC,OAAO,CAACwC,KAAK,CAACZ,MAAM,GAAG,CAAC,EAAG;UAACW,IAAI,EAAC;QAAO;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC5D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,EAEbpB,WAAW,iBACVH,OAAA,CAAChB,WAAW;MAAA8B,QAAA,gBACVd,OAAA,CAACd,MAAM;QACLuD,IAAI,EAAC,OAAO;QACZ1B,SAAS,EAAElB,IAAK;QAChBgD,EAAE,EAAE,YAAY3C,OAAO,CAAC4C,EAAE,EAAG;QAAAhC,QAAA,EAC9B;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvB,OAAA,CAACd,MAAM;QACLuD,IAAI,EAAC,OAAO;QACZf,OAAO,EAAC,WAAW;QACnBX,SAAS,EAAElB,IAAK;QAChBgD,EAAE,EAAE,YAAY3C,OAAO,CAAC4C,EAAE,QAAS;QAAAhC,QAAA,EACpC;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACd;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAClB,EAAA,CAtFIJ,WAAW;EAAA,QACoBH,MAAM;AAAA;AAAAiD,EAAA,GADrC9C,WAAW;AAwFjB,eAAeA,WAAW;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}