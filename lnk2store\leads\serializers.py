from rest_framework import serializers
from .models import Lead
from products.serializers import ProductListSerializer
from accounts.serializers import UserProfileSerializer


class LeadCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new leads"""
    class Meta:
        model = Lead
        fields = ('name', 'email', 'phone_number', 'message', 'product')

    def validate_phone_number(self):
        phone = self.validated_data.get('phone_number')
        if not phone:
            raise serializers.ValidationError('رقم الهاتف مطلوب')
        return phone


class LeadListSerializer(serializers.ModelSerializer):
    """Serializer for listing leads"""
    product = ProductListSerializer(read_only=True)
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = Lead
        fields = ('id', 'name', 'email', 'phone_number', 'message', 
                 'product', 'user', 'deducted', 'created_at')


class LeadDetailSerializer(serializers.ModelSerializer):
    """Serializer for lead details"""
    product = ProductListSerializer(read_only=True)
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = Lead
        fields = ('id', 'name', 'email', 'phone_number', 'message', 
                 'product', 'user', 'deducted', 'created_at')


class LeadStatsSerializer(serializers.Serializer):
    """Serializer for lead statistics"""
    total_leads = serializers.IntegerField()
    pending_leads = serializers.IntegerField()
    processed_leads = serializers.IntegerField()
    today_leads = serializers.IntegerField()
    this_week_leads = serializers.IntegerField()
    this_month_leads = serializers.IntegerField()
