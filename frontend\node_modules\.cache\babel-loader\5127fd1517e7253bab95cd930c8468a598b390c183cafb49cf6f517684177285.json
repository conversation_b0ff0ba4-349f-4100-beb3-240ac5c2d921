{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Button, Container, Grid, Card, CardContent, CardActions } from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport ProductCard from '../components/ProductCard';\nimport { useApi } from '../hooks/useApi';\nimport { productsAPI } from '../services/api';\nimport Logo from '../components/Logo';\nimport VideoBackground from '../components/VideoBackground';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const {\n    data: products,\n    loading\n  } = useApi(() => productsAPI.getProducts());\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(VideoBackground, {\n      src: \"/logo_video.mp4\",\n      minHeight: \"700px\",\n      sx: {\n        color: 'white',\n        py: {\n          xs: 4,\n          sm: 6,\n          md: 8\n        },\n        textAlign: 'center',\n        mb: 6\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"md\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Logo, {\n            variant: \"light\",\n            height: \"80px\",\n            linkTo: null\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h2\",\n          component: \"h1\",\n          gutterBottom: true,\n          sx: {\n            textShadow: '2px 2px 4px rgba(0,0,0,0.5)',\n            fontWeight: 'bold'\n          },\n          children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A Lnk2Store\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            mb: 4,\n            opacity: 0.95,\n            textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n          },\n          children: \"\\u0645\\u0646\\u0635\\u0629 SaaS \\u0644\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0635\\u0641\\u062D\\u0627\\u062A \\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u0627\\u062D\\u062A\\u0631\\u0627\\u0641\\u064A\\u0629 \\u0645\\u0639 \\u0646\\u0638\\u0627\\u0645 \\u062C\\u0645\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), !isAuthenticated ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            component: Link,\n            to: \"/register\",\n            sx: {\n              bgcolor: 'white',\n              color: 'primary.main'\n            },\n            children: \"\\u0627\\u0628\\u062F\\u0623 \\u0627\\u0644\\u0622\\u0646 \\u0645\\u062C\\u0627\\u0646\\u0627\\u064B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            component: Link,\n            to: \"/login\",\n            sx: {\n              borderColor: 'white',\n              color: 'white'\n            },\n            children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"large\",\n          component: Link,\n          to: \"/dashboard\",\n          sx: {\n            bgcolor: 'white',\n            color: 'primary.main'\n          },\n          children: \"\\u0627\\u0646\\u062A\\u0642\\u0644 \\u0625\\u0644\\u0649 \\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          children: \"\\u0627\\u0644\\u0645\\u0632\\u0627\\u064A\\u0627 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83D\\uDCB0 \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u062F\\u0641\\u0639 \\u0644\\u0643\\u0644 \\u0637\\u0644\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u062F\\u0641\\u0639 \\u0641\\u0642\\u0637 \\u0645\\u0642\\u0627\\u0628\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u062A\\u064A \\u062A\\u0633\\u062A\\u0642\\u0628\\u0644\\u0647\\u0627. \\u0646\\u0638\\u0627\\u0645 \\u0639\\u0627\\u062F\\u0644 \\u0648\\u0634\\u0641\\u0627\\u0641.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83D\\uDCF1 \\u0625\\u0631\\u0633\\u0627\\u0644 \\u0641\\u0648\\u0631\\u064A \\u0644\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u0633\\u062A\\u0642\\u0628\\u0644 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A \\u0645\\u0628\\u0627\\u0634\\u0631\\u0629 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628 \\u0641\\u0648\\u0631 \\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u0631\\u0635\\u064A\\u062F.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  color: \"primary\",\n                  children: \"\\uD83C\\uDFA8 \\u0642\\u0648\\u0627\\u0644\\u0628 \\u062C\\u0627\\u0647\\u0632\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0645\\u0646 \\u0645\\u062C\\u0645\\u0648\\u0639\\u0629 \\u0642\\u0648\\u0627\\u0644\\u0628 \\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u0627\\u062D\\u062A\\u0631\\u0627\\u0641\\u064A\\u0629 \\u0642\\u0627\\u0628\\u0644\\u0629 \\u0644\\u0644\\u062A\\u062E\\u0635\\u064A\\u0635.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), products && products.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          children: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0645\\u064A\\u0632\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          sx: {\n            mt: 2\n          },\n          children: products.slice(0, 6).map(product => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(ProductCard, {\n              product: product\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 19\n            }, this)\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            component: Link,\n            to: \"/products\",\n            children: \"\\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          bgcolor: 'grey.100',\n          p: 6,\n          borderRadius: 2,\n          textAlign: 'center',\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          children: \"\\u062C\\u0627\\u0647\\u0632 \\u0644\\u0644\\u0628\\u062F\\u0621\\u061F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mb: 3\n          },\n          children: \"\\u0627\\u0646\\u0636\\u0645 \\u0625\\u0644\\u0649 \\u0622\\u0644\\u0627\\u0641 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631 \\u0627\\u0644\\u0630\\u064A\\u0646 \\u064A\\u0633\\u062A\\u062E\\u062F\\u0645\\u0648\\u0646 Lnk2Store \\u0644\\u062A\\u0646\\u0645\\u064A\\u0629 \\u0623\\u0639\\u0645\\u0627\\u0644\\u0647\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"large\",\n          component: Link,\n          to: \"/register\",\n          children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628 \\u0645\\u062C\\u0627\\u0646\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"lBTdndtKRF+Sc2sQ09iVtGMQeZg=\", false, function () {\n  return [useAuth, useApi];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Link", "useAuth", "ProductCard", "useApi", "productsAPI", "Logo", "VideoBackground", "jsxDEV", "_jsxDEV", "HomePage", "_s", "isAuthenticated", "data", "products", "loading", "getProducts", "children", "src", "minHeight", "sx", "color", "py", "xs", "sm", "md", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "display", "justifyContent", "variant", "height", "linkTo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "gutterBottom", "textShadow", "fontWeight", "opacity", "gap", "size", "to", "bgcolor", "borderColor", "container", "spacing", "mt", "item", "length", "slice", "map", "product", "id", "p", "borderRadius", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  Button,\n  Container,\n  Grid,\n  Card,\n  CardContent,\n  CardActions\n} from '@mui/material';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport ProductCard from '../components/ProductCard';\nimport { useApi } from '../hooks/useApi';\nimport { productsAPI } from '../services/api';\nimport Logo from '../components/Logo';\nimport VideoBackground from '../components/VideoBackground';\n\nconst HomePage = () => {\n  const { isAuthenticated } = useAuth();\n  const { data: products, loading } = useApi(() => productsAPI.getProducts());\n\n  return (\n    <Box>\n      {/* Hero Section */}\n      <VideoBackground\n        src=\"/logo_video.mp4\"\n        minHeight=\"700px\"\n        sx={{\n          color: 'white',\n          py: { xs: 4, sm: 6, md: 8 },\n          textAlign: 'center',\n          mb: 6\n        }}\n      >\n        <Container maxWidth=\"md\">\n          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>\n            <Logo variant=\"light\" height=\"80px\" linkTo={null} />\n          </Box>\n          <Typography\n            variant=\"h2\"\n            component=\"h1\"\n            gutterBottom\n            sx={{\n              textShadow: '2px 2px 4px rgba(0,0,0,0.5)',\n              fontWeight: 'bold'\n            }}\n          >\n            مرحباً بك في Lnk2Store\n          </Typography>\n          <Typography\n            variant=\"h5\"\n            sx={{\n              mb: 4,\n              opacity: 0.95,\n              textShadow: '1px 1px 2px rgba(0,0,0,0.5)'\n            }}\n          >\n            منصة SaaS لإنشاء صفحات تسويقية احترافية مع نظام جمع الطلبات\n          </Typography>\n          {!isAuthenticated ? (\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>\n              <Button\n                variant=\"contained\"\n                size=\"large\"\n                component={Link}\n                to=\"/register\"\n                sx={{ bgcolor: 'white', color: 'primary.main' }}\n              >\n                ابدأ الآن مجاناً\n              </Button>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                component={Link}\n                to=\"/login\"\n                sx={{ borderColor: 'white', color: 'white' }}\n              >\n                تسجيل الدخول\n              </Button>\n            </Box>\n          ) : (\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              component={Link}\n              to=\"/dashboard\"\n              sx={{ bgcolor: 'white', color: 'primary.main' }}\n            >\n              انتقل إلى لوحة التحكم\n            </Button>\n          )}\n        </Container>\n      </VideoBackground>\n\n      <Container maxWidth=\"lg\">\n        {/* Features Section */}\n        <Box sx={{ mb: 8 }}>\n          <Typography variant=\"h3\" textAlign=\"center\" gutterBottom>\n            المزايا الرئيسية\n          </Typography>\n          <Grid container spacing={4} sx={{ mt: 2 }}>\n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    💰 نظام الدفع لكل طلب\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    ادفع فقط مقابل الطلبات التي تستقبلها. نظام عادل وشفاف.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    📱 إرسال فوري للواتساب\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    استقبل الطلبات مباشرة على الواتساب فور تأكيد الرصيد.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <Card sx={{ height: '100%', textAlign: 'center' }}>\n                <CardContent>\n                  <Typography variant=\"h5\" gutterBottom color=\"primary\">\n                    🎨 قوالب جاهزة\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    اختر من مجموعة قوالب تسويقية احترافية قابلة للتخصيص.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Products Preview */}\n        {products && products.length > 0 && (\n          <Box sx={{ mb: 8 }}>\n            <Typography variant=\"h3\" textAlign=\"center\" gutterBottom>\n              منتجات مميزة\n            </Typography>\n            <Grid container spacing={3} sx={{ mt: 2 }}>\n              {products.slice(0, 6).map((product) => (\n                <Grid item xs={12} sm={6} md={4} key={product.id}>\n                  <ProductCard product={product} />\n                </Grid>\n              ))}\n            </Grid>\n            <Box sx={{ textAlign: 'center', mt: 4 }}>\n              <Button\n                variant=\"outlined\"\n                size=\"large\"\n                component={Link}\n                to=\"/products\"\n              >\n                عرض جميع المنتجات\n              </Button>\n            </Box>\n          </Box>\n        )}\n\n        {/* CTA Section */}\n        <Box\n          sx={{\n            bgcolor: 'grey.100',\n            p: 6,\n            borderRadius: 2,\n            textAlign: 'center',\n            mb: 4\n          }}\n        >\n          <Typography variant=\"h4\" gutterBottom>\n            جاهز للبدء؟\n          </Typography>\n          <Typography variant=\"body1\" sx={{ mb: 3 }}>\n            انضم إلى آلاف التجار الذين يستخدمون Lnk2Store لتنمية أعمالهم\n          </Typography>\n          {!isAuthenticated && (\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              component={Link}\n              to=\"/register\"\n            >\n              إنشاء حساب مجاني\n            </Button>\n          )}\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,QACN,eAAe;AACtB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,eAAe,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAgB,CAAC,GAAGV,OAAO,CAAC,CAAC;EACrC,MAAM;IAAEW,IAAI,EAAEC,QAAQ;IAAEC;EAAQ,CAAC,GAAGX,MAAM,CAAC,MAAMC,WAAW,CAACW,WAAW,CAAC,CAAC,CAAC;EAE3E,oBACEP,OAAA,CAAChB,GAAG;IAAAwB,QAAA,gBAEFR,OAAA,CAACF,eAAe;MACdW,GAAG,EAAC,iBAAiB;MACrBC,SAAS,EAAC,OAAO;MACjBC,EAAE,EAAE;QACFC,KAAK,EAAE,OAAO;QACdC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAC3BC,SAAS,EAAE,QAAQ;QACnBC,EAAE,EAAE;MACN,CAAE;MAAAV,QAAA,eAEFR,OAAA,CAACb,SAAS;QAACgC,QAAQ,EAAC,IAAI;QAAAX,QAAA,gBACtBR,OAAA,CAAChB,GAAG;UAAC2B,EAAE,EAAE;YAAEO,EAAE,EAAE,CAAC;YAAEE,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAb,QAAA,eAC5DR,OAAA,CAACH,IAAI;YAACyB,OAAO,EAAC,OAAO;YAACC,MAAM,EAAC,MAAM;YAACC,MAAM,EAAE;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACN5B,OAAA,CAACf,UAAU;UACTqC,OAAO,EAAC,IAAI;UACZO,SAAS,EAAC,IAAI;UACdC,YAAY;UACZnB,EAAE,EAAE;YACFoB,UAAU,EAAE,6BAA6B;YACzCC,UAAU,EAAE;UACd,CAAE;UAAAxB,QAAA,EACH;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5B,OAAA,CAACf,UAAU;UACTqC,OAAO,EAAC,IAAI;UACZX,EAAE,EAAE;YACFO,EAAE,EAAE,CAAC;YACLe,OAAO,EAAE,IAAI;YACbF,UAAU,EAAE;UACd,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ,CAACzB,eAAe,gBACfH,OAAA,CAAChB,GAAG;UAAC2B,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEc,GAAG,EAAE,CAAC;YAAEb,cAAc,EAAE;UAAS,CAAE;UAAAb,QAAA,gBAC7DR,OAAA,CAACd,MAAM;YACLoC,OAAO,EAAC,WAAW;YACnBa,IAAI,EAAC,OAAO;YACZN,SAAS,EAAErC,IAAK;YAChB4C,EAAE,EAAC,WAAW;YACdzB,EAAE,EAAE;cAAE0B,OAAO,EAAE,OAAO;cAAEzB,KAAK,EAAE;YAAe,CAAE;YAAAJ,QAAA,EACjD;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5B,OAAA,CAACd,MAAM;YACLoC,OAAO,EAAC,UAAU;YAClBa,IAAI,EAAC,OAAO;YACZN,SAAS,EAAErC,IAAK;YAChB4C,EAAE,EAAC,QAAQ;YACXzB,EAAE,EAAE;cAAE2B,WAAW,EAAE,OAAO;cAAE1B,KAAK,EAAE;YAAQ,CAAE;YAAAJ,QAAA,EAC9C;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN5B,OAAA,CAACd,MAAM;UACLoC,OAAO,EAAC,WAAW;UACnBa,IAAI,EAAC,OAAO;UACZN,SAAS,EAAErC,IAAK;UAChB4C,EAAE,EAAC,YAAY;UACfzB,EAAE,EAAE;YAAE0B,OAAO,EAAE,OAAO;YAAEzB,KAAK,EAAE;UAAe,CAAE;UAAAJ,QAAA,EACjD;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAElB5B,OAAA,CAACb,SAAS;MAACgC,QAAQ,EAAC,IAAI;MAAAX,QAAA,gBAEtBR,OAAA,CAAChB,GAAG;QAAC2B,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,gBACjBR,OAAA,CAACf,UAAU;UAACqC,OAAO,EAAC,IAAI;UAACL,SAAS,EAAC,QAAQ;UAACa,YAAY;UAAAtB,QAAA,EAAC;QAEzD;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5B,OAAA,CAACZ,IAAI;UAACmD,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC7B,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,gBACxCR,OAAA,CAACZ,IAAI;YAACsD,IAAI;YAAC5B,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAR,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACsB,EAAE,EAAE;gBAAEY,MAAM,EAAE,MAAM;gBAAEN,SAAS,EAAE;cAAS,CAAE;cAAAT,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACqC,OAAO,EAAC,IAAI;kBAACQ,YAAY;kBAAClB,KAAK,EAAC,SAAS;kBAAAJ,QAAA,EAAC;gBAEtD;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5B,OAAA,CAACf,UAAU;kBAACqC,OAAO,EAAC,OAAO;kBAAAd,QAAA,EAAC;gBAE5B;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP5B,OAAA,CAACZ,IAAI;YAACsD,IAAI;YAAC5B,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAR,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACsB,EAAE,EAAE;gBAAEY,MAAM,EAAE,MAAM;gBAAEN,SAAS,EAAE;cAAS,CAAE;cAAAT,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACqC,OAAO,EAAC,IAAI;kBAACQ,YAAY;kBAAClB,KAAK,EAAC,SAAS;kBAAAJ,QAAA,EAAC;gBAEtD;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5B,OAAA,CAACf,UAAU;kBAACqC,OAAO,EAAC,OAAO;kBAAAd,QAAA,EAAC;gBAE5B;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP5B,OAAA,CAACZ,IAAI;YAACsD,IAAI;YAAC5B,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAR,QAAA,eACvBR,OAAA,CAACX,IAAI;cAACsB,EAAE,EAAE;gBAAEY,MAAM,EAAE,MAAM;gBAAEN,SAAS,EAAE;cAAS,CAAE;cAAAT,QAAA,eAChDR,OAAA,CAACV,WAAW;gBAAAkB,QAAA,gBACVR,OAAA,CAACf,UAAU;kBAACqC,OAAO,EAAC,IAAI;kBAACQ,YAAY;kBAAClB,KAAK,EAAC,SAAS;kBAAAJ,QAAA,EAAC;gBAEtD;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5B,OAAA,CAACf,UAAU;kBAACqC,OAAO,EAAC,OAAO;kBAAAd,QAAA,EAAC;gBAE5B;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGLvB,QAAQ,IAAIA,QAAQ,CAACsC,MAAM,GAAG,CAAC,iBAC9B3C,OAAA,CAAChB,GAAG;QAAC2B,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,gBACjBR,OAAA,CAACf,UAAU;UAACqC,OAAO,EAAC,IAAI;UAACL,SAAS,EAAC,QAAQ;UAACa,YAAY;UAAAtB,QAAA,EAAC;QAEzD;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5B,OAAA,CAACZ,IAAI;UAACmD,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC7B,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EACvCH,QAAQ,CAACuC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,OAAO,iBAChC9C,OAAA,CAACZ,IAAI;YAACsD,IAAI;YAAC5B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAR,QAAA,eAC9BR,OAAA,CAACN,WAAW;cAACoD,OAAO,EAAEA;YAAQ;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADGkB,OAAO,CAACC,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE1C,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP5B,OAAA,CAAChB,GAAG;UAAC2B,EAAE,EAAE;YAAEM,SAAS,EAAE,QAAQ;YAAEwB,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,eACtCR,OAAA,CAACd,MAAM;YACLoC,OAAO,EAAC,UAAU;YAClBa,IAAI,EAAC,OAAO;YACZN,SAAS,EAAErC,IAAK;YAChB4C,EAAE,EAAC,WAAW;YAAA5B,QAAA,EACf;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD5B,OAAA,CAAChB,GAAG;QACF2B,EAAE,EAAE;UACF0B,OAAO,EAAE,UAAU;UACnBW,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,CAAC;UACfhC,SAAS,EAAE,QAAQ;UACnBC,EAAE,EAAE;QACN,CAAE;QAAAV,QAAA,gBAEFR,OAAA,CAACf,UAAU;UAACqC,OAAO,EAAC,IAAI;UAACQ,YAAY;UAAAtB,QAAA,EAAC;QAEtC;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5B,OAAA,CAACf,UAAU;UAACqC,OAAO,EAAC,OAAO;UAACX,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UAAAV,QAAA,EAAC;QAE3C;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ,CAACzB,eAAe,iBACfH,OAAA,CAACd,MAAM;UACLoC,OAAO,EAAC,WAAW;UACnBa,IAAI,EAAC,OAAO;UACZN,SAAS,EAAErC,IAAK;UAChB4C,EAAE,EAAC,WAAW;UAAA5B,QAAA,EACf;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC1B,EAAA,CArLID,QAAQ;EAAA,QACgBR,OAAO,EACCE,MAAM;AAAA;AAAAuD,EAAA,GAFtCjD,QAAQ;AAuLd,eAAeA,QAAQ;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}