from django.urls import path
from . import api_views

urlpatterns = [
    # Lead creation (public)
    path('create/', api_views.create_lead, name='api_lead_create'),
    
    # User leads management
    path('my-leads/', api_views.UserLeadsListView.as_view(), name='api_user_leads'),
    path('<int:pk>/', api_views.LeadDetailView.as_view(), name='api_lead_detail'),
    path('<int:lead_id>/mark-processed/', api_views.mark_lead_processed, name='api_lead_mark_processed'),
    
    # Statistics and reports
    path('stats/', api_views.lead_stats, name='api_lead_stats'),
    path('recent/', api_views.recent_leads, name='api_recent_leads'),
]
