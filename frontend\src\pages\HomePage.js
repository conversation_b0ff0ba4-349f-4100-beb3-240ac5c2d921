import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>rid,
  Card,
  CardContent,
  Chip
} from '@mui/material';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import ProductCard from '../components/ProductCard';
import { useApi } from '../hooks/useApi';
import { productsAPI } from '../services/api';

const HomePage = () => {
  const { isAuthenticated } = useAuth();
  const { data: products } = useApi(() => productsAPI.getProducts());

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: { xs: 12, md: 20 },
          minHeight: { xs: '80vh', md: '90vh' },
          display: 'flex',
          alignItems: 'center',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background Video */}
        <Box
          component="video"
          autoPlay
          muted
          loop
          playsInline
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 0,
            opacity: 0.3
          }}
        >
          <source src="/logo_video.mp4" type="video/mp4" />
        </Box>

        {/* Content */}
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Typography
            variant="h1"
            component="h1"
            sx={{
              fontWeight: 700,
              mb: 3,
              fontSize: { xs: '2.5rem', md: '4rem' },
              textAlign: 'center'
            }}
          >
            مرحباً بك في Lnk2Store
          </Typography>

          <Typography
            variant="h5"
            sx={{
              mb: 6,
              opacity: 0.9,
              fontSize: { xs: '1.1rem', md: '1.5rem' },
              maxWidth: '800px',
              mx: 'auto'
            }}
          >
            منصة SaaS لإنشاء صفحات تسويقية احترافية مع نظام جمع الطلبات
          </Typography>

          {!isAuthenticated ? (
            <Box sx={{
              display: 'flex',
              gap: 3,
              justifyContent: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: 'center'
            }}>
              <Button
                variant="contained"
                size="large"
                component={Link}
                to="/register"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'grey.100'
                  }
                }}
              >
                ابدأ الآن مجاناً
              </Button>
              <Button
                variant="outlined"
                size="large"
                component={Link}
                to="/login"
                sx={{
                  borderColor: 'white',
                  color: 'white',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255,255,255,0.1)'
                  }
                }}
              >
                تسجيل الدخول
              </Button>
            </Box>
          ) : (
            <Button
              variant="contained"
              size="large"
              component={Link}
              to="/dashboard"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600
              }}
            >
              انتقل إلى لوحة التحكم
            </Button>
          )}
        </Container>
      </Box>

      {/* Features Section */}
      <Box sx={{ py: 8, bgcolor: 'grey.50' }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            textAlign="center"
            gutterBottom
            sx={{
              fontWeight: 700,
              mb: 6,
              color: 'text.primary'
            }}
          >
            المزايا الرئيسية
          </Typography>

          {/* Icons Row */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              gap: { xs: 4, md: 8 },
              mb: 6,
              flexWrap: 'wrap'
            }}
          >
            <Box sx={{ textAlign: 'center' }}>
              <Box sx={{ fontSize: { xs: '4rem', md: '5rem' }, mb: 1 }}>💰</Box>
              <Typography variant="body2" color="text.secondary" sx={{ maxWidth: '120px' }}>
                نظام الدفع لكل طلب
              </Typography>
            </Box>

            <Box sx={{ textAlign: 'center' }}>
              <Box sx={{ fontSize: { xs: '4rem', md: '5rem' }, mb: 1 }}>📱</Box>
              <Typography variant="body2" color="text.secondary" sx={{ maxWidth: '120px' }}>
                إرسال فوري للواتساب
              </Typography>
            </Box>

            <Box sx={{ textAlign: 'center' }}>
              <Box sx={{ fontSize: { xs: '4rem', md: '5rem' }, mb: 1 }}>🎨</Box>
              <Typography variant="body2" color="text.secondary" sx={{ maxWidth: '120px' }}>
                قوالب جاهزة
              </Typography>
            </Box>
          </Box>

          {/* Feature Cards */}
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'transform 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)'
                  }
                }}
              >
                <Typography variant="h6" gutterBottom color="primary" fontWeight={600}>
                  نظام الدفع لكل طلب
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  ادفع فقط مقابل الطلبات التي تستقبلها. نظام عادل وشفاف يضمن لك الحصول على قيمة حقيقية مقابل استثمارك.
                </Typography>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'transform 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)'
                  }
                }}
              >
                <Typography variant="h6" gutterBottom color="primary" fontWeight={600}>
                  إرسال فوري للواتساب
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  استقبل الطلبات مباشرة على الواتساب فور تأكيد الرصيد. تواصل سريع ومباشر مع عملائك المحتملين.
                </Typography>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'transform 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)'
                  }
                }}
              >
                <Typography variant="h6" gutterBottom color="primary" fontWeight={600}>
                  قوالب جاهزة
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  اختر من مجموعة قوالب تسويقية احترافية قابلة للتخصيص. صمم صفحتك في دقائق معدودة.
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Template Showcase */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography
          variant="h3"
          textAlign="center"
          gutterBottom
          sx={{
            fontWeight: 700,
            mb: 2,
            color: 'text.primary'
          }}
        >
          قوالب تسويقية احترافية
        </Typography>
        <Typography
          variant="h6"
          textAlign="center"
          sx={{
            mb: 6,
            color: 'text.secondary',
            maxWidth: '600px',
            mx: 'auto'
          }}
        >
          اختر من مجموعة متنوعة من القوالب المصممة خصيصاً لزيادة المبيعات
        </Typography>

        <Grid container spacing={4}>
          {/* Shopping Template Preview */}
          <Grid item xs={12} md={6}>
            <Card
              sx={{
                height: '100%',
                cursor: 'pointer',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 40px rgba(0,0,0,0.15)'
                }
              }}
              component={Link}
              to="/template/shopping"
              sx={{ textDecoration: 'none' }}
            >
              <Box sx={{ position: 'relative', overflow: 'hidden' }}>
                <Box
                  sx={{
                    height: 300,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white'
                  }}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    <Box sx={{ fontSize: '4rem', mb: 2 }}>🛍️</Box>
                    <Typography variant="h5" sx={{ fontWeight: 600 }}>
                      قالب المتجر الإلكتروني
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label="الأكثر شعبية"
                  color="success"
                  sx={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    fontWeight: 600
                  }}
                />
              </Box>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  قالب المتجر الإلكتروني
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  قالب متكامل لعرض المنتجات مع نموذج طلب احترافي وتصميم جذاب يزيد من معدل التحويل
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label="نموذج طلب" size="small" variant="outlined" />
                  <Chip label="معرض صور" size="small" variant="outlined" />
                  <Chip label="تقييمات" size="small" variant="outlined" />
                  <Chip label="واتساب" size="small" variant="outlined" />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Coming Soon Templates */}
          <Grid item xs={12} md={6}>
            <Card
              sx={{
                height: '100%',
                opacity: 0.7,
                position: 'relative'
              }}
            >
              <Box sx={{ position: 'relative', overflow: 'hidden' }}>
                <Box
                  sx={{
                    height: 300,
                    background: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white'
                  }}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    <Box sx={{ fontSize: '4rem', mb: 2 }}>📋</Box>
                    <Typography variant="h5" sx={{ fontWeight: 600 }}>
                      قالب الخدمات
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label="قريباً"
                  color="warning"
                  sx={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    fontWeight: 600
                  }}
                />
              </Box>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  قالب الخدمات
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  قالب مخصص لعرض الخدمات المهنية مع إمكانية حجز المواعيد والاستشارات
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label="حجز مواعيد" size="small" variant="outlined" />
                  <Chip label="عرض خدمات" size="small" variant="outlined" />
                  <Chip label="استشارات" size="small" variant="outlined" />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 6 }}>
          <Button
            variant="contained"
            size="large"
            component={Link}
            to="/template/shopping"
            sx={{
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600,
              mr: 2
            }}
          >
            جرب القالب الآن
          </Button>
          <Button
            variant="outlined"
            size="large"
            component={Link}
            to="/templates"
            sx={{
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600
            }}
          >
            عرض جميع القوالب
          </Button>
        </Box>
      </Container>

      {/* Products Preview */}
      {products && products.length > 0 && (
        <Box sx={{ bgcolor: 'grey.50', py: 8 }}>
          <Container maxWidth="lg">
            <Typography
              variant="h3"
              textAlign="center"
              gutterBottom
              sx={{
                fontWeight: 700,
                mb: 6,
                color: 'text.primary'
              }}
            >
              منتجات مميزة
            </Typography>
            <Grid container spacing={4}>
              {products.slice(0, 6).map((product) => (
                <Grid item xs={12} sm={6} md={4} key={product.id}>
                  <ProductCard product={product} />
                </Grid>
              ))}
            </Grid>
            <Box sx={{ textAlign: 'center', mt: 6 }}>
              <Button
                variant="outlined"
                size="large"
                component={Link}
                to="/products"
                sx={{
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600
                }}
              >
                عرض جميع المنتجات
              </Button>
            </Box>
          </Container>
        </Box>
      )}

      {/* CTA Section */}
      <Box sx={{ py: 8, bgcolor: 'primary.main', color: 'white' }}>
        <Container maxWidth="md" sx={{ textAlign: 'center' }}>
          <Typography
            variant="h3"
            gutterBottom
            sx={{ fontWeight: 700, mb: 3 }}
          >
            جاهز للبدء؟
          </Typography>
          <Typography
            variant="h6"
            sx={{ mb: 4, opacity: 0.9 }}
          >
            انضم إلى آلاف التجار الذين يستخدمون Lnk2Store لتنمية أعمالهم
          </Typography>
          {!isAuthenticated && (
            <Button
              variant="contained"
              size="large"
              component={Link}
              to="/register"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
                '&:hover': {
                  bgcolor: 'grey.100'
                }
              }}
            >
              إنشاء حساب مجاني
            </Button>
          )}
        </Container>
      </Box>
    </Box>
  );
};

export default HomePage;
