import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card
} from '@mui/material';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

const HomePage = () => {
  const { isAuthenticated } = useAuth();

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          minHeight: { xs: '70vh', md: '75vh' },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden',
          width: '100vw',
          marginLeft: { xs: '-16px', md: '-24px' },
          marginRight: { xs: '-16px', md: '-24px' }
        }}
      >
        {/* Background Video */}
        <Box
          component="video"
          autoPlay
          muted
          loop
          playsInline
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 0,
            opacity: 0.3
          }}
        >
          <source src="/logo_video.mp4" type="video/mp4" />
        </Box>
        
        {/* Content */}
        <Container maxWidth="md" sx={{ position: 'relative', zIndex: 1, px: { xs: 3, md: 4 } }}>
          <Typography
            variant="h1"
            component="h1"
            sx={{
              fontWeight: 700,
              mb: { xs: 2, md: 3 },
              fontSize: { xs: '2rem', sm: '2.5rem', md: '3.5rem' },
              textAlign: 'center',
              lineHeight: { xs: 1.2, md: 1.1 }
            }}
          >
            مرحباً بك في Lnk2Store
          </Typography>

          <Typography
            variant="h5"
            sx={{
              mb: { xs: 4, md: 5 },
              opacity: 0.9,
              fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' },
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.5
            }}
          >
            منصة SaaS لإنشاء صفحات تسويقية احترافية مع نظام جمع الطلبات
          </Typography>
          
          {!isAuthenticated ? (
            <Box sx={{
              display: 'flex',
              gap: { xs: 2, md: 3 },
              justifyContent: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: 'center',
              maxWidth: '400px',
              mx: 'auto'
            }}>
              <Button
                variant="contained"
                size="large"
                component={Link}
                to="/register"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  px: { xs: 3, md: 4 },
                  py: { xs: 1.2, md: 1.5 },
                  fontSize: { xs: '1rem', md: '1.1rem' },
                  fontWeight: 600,
                  minWidth: { xs: '200px', sm: 'auto' },
                  borderRadius: 2,
                  '&:hover': {
                    bgcolor: 'grey.100',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                ابدأ الآن مجاناً
              </Button>
              <Button
                variant="outlined"
                size="large"
                component={Link}
                to="/login"
                sx={{
                  borderColor: 'white',
                  color: 'white',
                  px: { xs: 3, md: 4 },
                  py: { xs: 1.2, md: 1.5 },
                  fontSize: { xs: '1rem', md: '1.1rem' },
                  fontWeight: 600,
                  minWidth: { xs: '200px', sm: 'auto' },
                  borderRadius: 2,
                  borderWidth: 2,
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255,255,255,0.15)',
                    transform: 'translateY(-2px)',
                    borderWidth: 2
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                تسجيل الدخول
              </Button>
            </Box>
          ) : (
            <Button
              variant="contained"
              size="large"
              component={Link}
              to="/dashboard"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                px: { xs: 3, md: 4 },
                py: { xs: 1.2, md: 1.5 },
                fontSize: { xs: '1rem', md: '1.1rem' },
                fontWeight: 600,
                borderRadius: 2,
                '&:hover': {
                  bgcolor: 'grey.100',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              انتقل إلى لوحة التحكم
            </Button>
          )}
        </Container>
      </Box>

      {/* Features Section */}
      <Box sx={{ py: { xs: 6, md: 8 }, bgcolor: 'grey.50', width: '100vw', marginLeft: { xs: '-16px', md: '-24px' }, marginRight: { xs: '-16px', md: '-24px' } }}>
        <Container maxWidth="lg" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', px: { xs: 3, md: 4 } }}>
          <Typography
            variant="h3"
            textAlign="center"
            gutterBottom
            sx={{
              fontWeight: 700,
              mb: { xs: 4, md: 6 },
              color: 'text.primary',
              fontSize: { xs: '2rem', md: '2.5rem' }
            }}
          >
            المزايا الرئيسية
          </Typography>
          
          {/* Icons Row with Avatars */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              gap: { xs: 3, sm: 4, md: 6 },
              mb: { xs: 4, md: 6 },
              flexWrap: 'wrap',
              maxWidth: '600px',
              mx: 'auto'
            }}
          >
            <Box sx={{ textAlign: 'center', flex: '1 1 auto', minWidth: '140px' }}>
              <Box
                component="img"
                src="/avatar1.png"
                alt="نظام الدفع"
                sx={{
                  width: { xs: '70px', sm: '80px', md: '90px' },
                  height: { xs: '70px', sm: '80px', md: '90px' },
                  borderRadius: '50%',
                  mb: { xs: 1.5, md: 2 },
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'transform 0.3s ease',
                  '&:hover': {
                    transform: 'scale(1.05)'
                  }
                }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, fontSize: { xs: '0.85rem', md: '0.9rem' } }}>
                نظام الدفع لكل طلب
              </Typography>
            </Box>

            <Box sx={{ textAlign: 'center', flex: '1 1 auto', minWidth: '140px' }}>
              <Box
                component="img"
                src="/avatar2.png"
                alt="واتساب"
                sx={{
                  width: { xs: '70px', sm: '80px', md: '90px' },
                  height: { xs: '70px', sm: '80px', md: '90px' },
                  borderRadius: '50%',
                  mb: { xs: 1.5, md: 2 },
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'transform 0.3s ease',
                  '&:hover': {
                    transform: 'scale(1.05)'
                  }
                }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, fontSize: { xs: '0.85rem', md: '0.9rem' } }}>
                إرسال فوري للواتساب
              </Typography>
            </Box>

            <Box sx={{ textAlign: 'center', flex: '1 1 auto', minWidth: '140px' }}>
              <Box
                component="img"
                src="/avatar3.png"
                alt="قوالب"
                sx={{
                  width: { xs: '70px', sm: '80px', md: '90px' },
                  height: { xs: '70px', sm: '80px', md: '90px' },
                  borderRadius: '50%',
                  mb: { xs: 1.5, md: 2 },
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'transform 0.3s ease',
                  '&:hover': {
                    transform: 'scale(1.05)'
                  }
                }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, fontSize: { xs: '0.85rem', md: '0.9rem' } }}>
                قوالب جاهزة
              </Typography>
            </Box>
          </Box>

          {/* Feature Cards */}
          <Grid container spacing={{ xs: 3, md: 4 }} sx={{ maxWidth: '900px', mx: 'auto' }}>
            <Grid item xs={12} md={4}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: { xs: 2.5, md: 3 },
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'all 0.3s ease',
                  borderRadius: 3,
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 8px 30px rgba(0,0,0,0.15)'
                  }
                }}
              >
                <Typography variant="h6" gutterBottom color="primary" fontWeight={600} sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}>
                  نظام الدفع لكل طلب
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.9rem', md: '1rem' }, lineHeight: 1.6 }}>
                  ادفع فقط مقابل الطلبات التي تستقبلها. نظام عادل وشفاف يضمن لك الحصول على قيمة حقيقية مقابل استثمارك.
                </Typography>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: { xs: 2.5, md: 3 },
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'all 0.3s ease',
                  borderRadius: 3,
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 8px 30px rgba(0,0,0,0.15)'
                  }
                }}
              >
                <Typography variant="h6" gutterBottom color="primary" fontWeight={600} sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}>
                  إرسال فوري للواتساب
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.9rem', md: '1rem' }, lineHeight: 1.6 }}>
                  استقبل الطلبات مباشرة على الواتساب فور تأكيد الرصيد. تواصل سريع ومباشر مع عملائك المحتملين.
                </Typography>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: { xs: 2.5, md: 3 },
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  transition: 'all 0.3s ease',
                  borderRadius: 3,
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 8px 30px rgba(0,0,0,0.15)'
                  }
                }}
              >
                <Typography variant="h6" gutterBottom color="primary" fontWeight={600} sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}>
                  قوالب جاهزة
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.9rem', md: '1rem' }, lineHeight: 1.6 }}>
                  اختر من مجموعة قوالب تسويقية احترافية قابلة للتخصيص. صمم صفحتك في دقائق معدودة.
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box sx={{
        py: { xs: 6, md: 8 },
        bgcolor: 'primary.main',
        color: 'white',
        width: '100vw',
        marginLeft: { xs: '-16px', md: '-24px' },
        marginRight: { xs: '-16px', md: '-24px' },
        display: 'flex',
        justifyContent: 'center'
      }}>
        <Container maxWidth="sm" sx={{ textAlign: 'center', px: { xs: 3, md: 4 } }}>
          <Typography
            variant="h3"
            gutterBottom
            sx={{
              fontWeight: 700,
              mb: { xs: 2, md: 3 },
              fontSize: { xs: '1.8rem', md: '2.5rem' }
            }}
          >
            جاهز للبدء؟
          </Typography>
          <Typography
            variant="h6"
            sx={{
              mb: { xs: 3, md: 4 },
              opacity: 0.9,
              fontSize: { xs: '1rem', md: '1.2rem' },
              lineHeight: 1.5,
              maxWidth: '400px',
              mx: 'auto'
            }}
          >
            انضم إلى آلاف التجار الذين يستخدمون Lnk2Store لتنمية أعمالهم
          </Typography>
          {!isAuthenticated && (
            <Button
              variant="contained"
              size="large"
              component={Link}
              to="/register"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                px: { xs: 3, md: 4 },
                py: { xs: 1.2, md: 1.5 },
                fontSize: { xs: '1rem', md: '1.1rem' },
                fontWeight: 600,
                borderRadius: 2,
                minWidth: '200px',
                '&:hover': {
                  bgcolor: 'grey.100',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              إنشاء حساب مجاني
            </Button>
          )}
        </Container>
      </Box>
    </Box>
  );
};

export default HomePage;
