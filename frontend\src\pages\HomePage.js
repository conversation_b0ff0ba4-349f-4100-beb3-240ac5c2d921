import React from 'react';
import {
  Box,
  Typography,
  But<PERSON>,
  Container,
  <PERSON>rid,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import ProductCard from '../components/ProductCard';
import { useApi } from '../hooks/useApi';
import { productsAPI } from '../services/api';

const HomePage = () => {
  const { isAuthenticated } = useAuth();
  const { data: products, loading } = useApi(() => productsAPI.getProducts());

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
          color: 'white',
          py: 8,
          textAlign: 'center',
          mb: 6
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h2" component="h1" gutterBottom>
            مرحباً بك في Lnk2Store
          </Typography>
          <Typography variant="h5" sx={{ mb: 4, opacity: 0.9 }}>
            منصة SaaS لإنشاء صفحات تسويقية احترافية مع نظام جمع الطلبات
          </Typography>
          {!isAuthenticated ? (
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button
                variant="contained"
                size="large"
                component={Link}
                to="/register"
                sx={{ bgcolor: 'white', color: 'primary.main' }}
              >
                ابدأ الآن مجاناً
              </Button>
              <Button
                variant="outlined"
                size="large"
                component={Link}
                to="/login"
                sx={{ borderColor: 'white', color: 'white' }}
              >
                تسجيل الدخول
              </Button>
            </Box>
          ) : (
            <Button
              variant="contained"
              size="large"
              component={Link}
              to="/dashboard"
              sx={{ bgcolor: 'white', color: 'primary.main' }}
            >
              انتقل إلى لوحة التحكم
            </Button>
          )}
        </Container>
      </Box>

      <Container maxWidth="lg">
        {/* Features Section */}
        <Box sx={{ mb: 8 }}>
          <Typography variant="h3" textAlign="center" gutterBottom>
            المزايا الرئيسية
          </Typography>
          <Grid container spacing={4} sx={{ mt: 2 }}>
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h5" gutterBottom color="primary">
                    💰 نظام الدفع لكل طلب
                  </Typography>
                  <Typography variant="body1">
                    ادفع فقط مقابل الطلبات التي تستقبلها. نظام عادل وشفاف.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h5" gutterBottom color="primary">
                    📱 إرسال فوري للواتساب
                  </Typography>
                  <Typography variant="body1">
                    استقبل الطلبات مباشرة على الواتساب فور تأكيد الرصيد.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', textAlign: 'center' }}>
                <CardContent>
                  <Typography variant="h5" gutterBottom color="primary">
                    🎨 قوالب جاهزة
                  </Typography>
                  <Typography variant="body1">
                    اختر من مجموعة قوالب تسويقية احترافية قابلة للتخصيص.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* Products Preview */}
        {products && products.length > 0 && (
          <Box sx={{ mb: 8 }}>
            <Typography variant="h3" textAlign="center" gutterBottom>
              منتجات مميزة
            </Typography>
            <Grid container spacing={3} sx={{ mt: 2 }}>
              {products.slice(0, 6).map((product) => (
                <Grid item xs={12} sm={6} md={4} key={product.id}>
                  <ProductCard product={product} />
                </Grid>
              ))}
            </Grid>
            <Box sx={{ textAlign: 'center', mt: 4 }}>
              <Button
                variant="outlined"
                size="large"
                component={Link}
                to="/products"
              >
                عرض جميع المنتجات
              </Button>
            </Box>
          </Box>
        )}

        {/* CTA Section */}
        <Box
          sx={{
            bgcolor: 'grey.100',
            p: 6,
            borderRadius: 2,
            textAlign: 'center',
            mb: 4
          }}
        >
          <Typography variant="h4" gutterBottom>
            جاهز للبدء؟
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            انضم إلى آلاف التجار الذين يستخدمون Lnk2Store لتنمية أعمالهم
          </Typography>
          {!isAuthenticated && (
            <Button
              variant="contained"
              size="large"
              component={Link}
              to="/register"
            >
              إنشاء حساب مجاني
            </Button>
          )}
        </Box>
      </Container>
    </Box>
  );
};

export default HomePage;
