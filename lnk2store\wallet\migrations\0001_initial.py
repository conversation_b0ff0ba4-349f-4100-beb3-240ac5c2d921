# Generated by Django 5.2.4 on 2025-07-10 18:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Wallet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('low_balance_threshold', models.DecimalField(decimal_places=2, default=50.0, max_digits=10)),
                ('last_low_balance_alert', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='wallet', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('transaction_type', models.CharField(choices=[('recharge', 'Recharge'), ('deduction', 'Deduction'), ('refund', 'Refund'), ('bonus', 'Bonus')], default='recharge', max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('description', models.CharField(blank=True, max_length=200)),
                ('reference_id', models.CharField(blank=True, max_length=100)),
                ('wallet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='wallet.wallet')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
    ]
