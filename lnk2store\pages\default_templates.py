"""
Default marketing templates for lnk2store
"""

PRODUCT_SHOWCASE_TEMPLATE = {
    'name': 'عرض منتج احترافي',
    'description': 'قالب لعرض منتج واحد بشكل احترافي مع نموذج طلب',
    'template_type': 'product_showcase',
    'category': 'ecommerce',
    'html_structure': '''
<div class="container-fluid p-0">
    <!-- Hero Section -->
    <section class="hero-section" style="background: linear-gradient(135deg, {{primary_color}} 0%, {{secondary_color}} 100%); min-height: 100vh; display: flex; align-items: center;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 text-white fw-bold mb-4">{{product_title}}</h1>
                    <p class="lead text-white mb-4">{{product_description}}</p>
                    <div class="price-section mb-4">
                        {% if show_discount %}
                        <span class="old-price text-white-50 text-decoration-line-through fs-4">{{old_price}} ريال</span>
                        {% endif %}
                        <span class="current-price text-white fw-bold fs-2 ms-3">{{current_price}} ريال</span>
                    </div>
                    <button class="btn btn-light btn-lg px-5 py-3" onclick="scrollToOrder()">
                        <i class="fas fa-shopping-cart me-2"></i>
                        اطلب الآن
                    </button>
                </div>
                <div class="col-lg-6 text-center">
                    <img src="{{product_image}}" alt="{{product_title}}" class="img-fluid rounded shadow-lg" style="max-height: 500px;">
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
        <div class="container">
            <h2 class="text-center mb-5">مميزات المنتج</h2>
            <div class="row">
                {% for feature in features %}
                <div class="col-md-4 mb-4">
                    <div class="feature-card text-center p-4 h-100 border rounded">
                        <i class="{{feature.icon}} fa-3x text-primary mb-3"></i>
                        <h5>{{feature.title}}</h5>
                        <p class="text-muted">{{feature.description}}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Order Form Section -->
    <section id="order-section" class="order-section py-5" style="background-color: #f8f9fa;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="card shadow">
                        <div class="card-body p-5">
                            <h3 class="text-center mb-4">اطلب {{product_title}} الآن</h3>
                            <form id="orderForm">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" name="phone" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات إضافية</label>
                                    <textarea class="form-control" name="message" rows="3"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال الطلب
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section py-5" style="background-color: {{primary_color}};">
        <div class="container text-center text-white">
            <h3 class="mb-4">تواصل معنا</h3>
            <div class="row justify-content-center">
                {% if show_phone %}
                <div class="col-md-4 mb-3">
                    <i class="fas fa-phone fa-2x mb-2"></i>
                    <p>{{contact_phone}}</p>
                </div>
                {% endif %}
                {% if show_email %}
                <div class="col-md-4 mb-3">
                    <i class="fas fa-envelope fa-2x mb-2"></i>
                    <p>{{contact_email}}</p>
                </div>
                {% endif %}
                {% if show_whatsapp %}
                <div class="col-md-4 mb-3">
                    <i class="fab fa-whatsapp fa-2x mb-2"></i>
                    <p>{{whatsapp_number}}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </section>
</div>
    ''',
    'css_styles': '''
.hero-section {
    position: relative;
}

.feature-card {
    transition: transform 0.3s ease;
    border: 2px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.price-section {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    display: inline-block;
}

.order-section .card {
    border: none;
    border-radius: 15px;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    border-radius: 25px;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

@media (max-width: 768px) {
    .hero-section {
        text-align: center;
    }
    
    .display-4 {
        font-size: 2rem;
    }
}
    ''',
    'js_scripts': '''
function scrollToOrder() {
    document.getElementById('order-section').scrollIntoView({
        behavior: 'smooth'
    });
}

document.getElementById('orderForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // Add product information
    data.product_id = USER_CONFIG.product_id;
    data.user_id = USER_CONFIG.user_id;
    
    // Send to API
    fetch('/api/v1/leads/create/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.lead_id) {
            alert('تم إرسال طلبك بنجاح! سنتواصل معك قريباً.');
            this.reset();
        } else {
            alert('حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى.');
    });
});
    ''',
    'config_schema': {
        'required': ['product_title', 'product_description', 'current_price', 'product_image'],
        'fields': {
            'product_title': {
                'type': 'string',
                'label': 'عنوان المنتج',
                'max_length': 100
            },
            'product_description': {
                'type': 'string',
                'label': 'وصف المنتج',
                'max_length': 500
            },
            'current_price': {
                'type': 'number',
                'label': 'السعر الحالي',
                'min_value': 0
            },
            'old_price': {
                'type': 'number',
                'label': 'السعر القديم (اختياري)',
                'min_value': 0
            },
            'show_discount': {
                'type': 'boolean',
                'label': 'إظهار الخصم',
                'default': False
            },
            'product_image': {
                'type': 'string',
                'label': 'رابط صورة المنتج'
            },
            'primary_color': {
                'type': 'string',
                'label': 'اللون الأساسي',
                'default': '#007bff'
            },
            'secondary_color': {
                'type': 'string',
                'label': 'اللون الثانوي',
                'default': '#6f42c1'
            },
            'features': {
                'type': 'array',
                'label': 'مميزات المنتج',
                'default': [
                    {'icon': 'fas fa-star', 'title': 'جودة عالية', 'description': 'منتج عالي الجودة'},
                    {'icon': 'fas fa-shipping-fast', 'title': 'شحن سريع', 'description': 'توصيل سريع لجميع المناطق'},
                    {'icon': 'fas fa-shield-alt', 'title': 'ضمان', 'description': 'ضمان شامل على المنتج'}
                ]
            },
            'contact_phone': {
                'type': 'string',
                'label': 'رقم الهاتف'
            },
            'contact_email': {
                'type': 'string',
                'label': 'البريد الإلكتروني'
            },
            'whatsapp_number': {
                'type': 'string',
                'label': 'رقم الواتساب'
            },
            'show_phone': {
                'type': 'boolean',
                'label': 'إظهار الهاتف',
                'default': True
            },
            'show_email': {
                'type': 'boolean',
                'label': 'إظهار البريد الإلكتروني',
                'default': True
            },
            'show_whatsapp': {
                'type': 'boolean',
                'label': 'إظهار الواتساب',
                'default': True
            }
        }
    }
}

SERVICE_LANDING_TEMPLATE = {
    'name': 'صفحة خدمة احترافية',
    'description': 'قالب لعرض خدمة مع تفاصيل شاملة ونموذج تواصل',
    'template_type': 'service_landing',
    'category': 'services',
    'html_structure': '''
<div class="service-landing">
    <!-- Header -->
    <header class="service-header py-5" style="background: {{header_bg_color}};">
        <div class="container text-center text-white">
            <h1 class="display-3 fw-bold mb-3">{{service_title}}</h1>
            <p class="lead mb-4">{{service_subtitle}}</p>
            <button class="btn btn-light btn-lg" onclick="scrollToContact()">
                احصل على الخدمة الآن
            </button>
        </div>
    </header>

    <!-- Service Details -->
    <section class="service-details py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h2 class="text-center mb-5">تفاصيل الخدمة</h2>
                    <div class="service-content">
                        {{service_description}}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section id="contact-section" class="contact-form-section py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <h3 class="text-center mb-4">تواصل معنا للحصول على الخدمة</h3>
                    <form id="serviceForm">
                        <div class="mb-3">
                            <input type="text" class="form-control" name="name" placeholder="الاسم الكامل" required>
                        </div>
                        <div class="mb-3">
                            <input type="tel" class="form-control" name="phone" placeholder="رقم الهاتف" required>
                        </div>
                        <div class="mb-3">
                            <input type="email" class="form-control" name="email" placeholder="البريد الإلكتروني">
                        </div>
                        <div class="mb-3">
                            <textarea class="form-control" name="message" rows="4" placeholder="تفاصيل إضافية عن الخدمة المطلوبة"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100">إرسال الطلب</button>
                    </form>
                </div>
            </div>
        </div>
    </section>
</div>
    ''',
    'css_styles': '''
.service-header {
    background: linear-gradient(135deg, var(--header-bg-color) 0%, #6f42c1 100%);
}

.service-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.contact-form-section .form-control {
    border-radius: 10px;
    padding: 15px;
    border: 2px solid #e9ecef;
}

.contact-form-section .form-control:focus {
    border-color: var(--header-bg-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
    ''',
    'js_scripts': '''
function scrollToContact() {
    document.getElementById('contact-section').scrollIntoView({
        behavior: 'smooth'
    });
}

document.getElementById('serviceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    data.user_id = USER_CONFIG.user_id;
    
    fetch('/api/v1/leads/create/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.lead_id) {
            alert('تم إرسال طلبك بنجاح! سنتواصل معك قريباً.');
            this.reset();
        } else {
            alert('حدث خطأ في إرسال الطلب.');
        }
    });
});
    ''',
    'config_schema': {
        'required': ['service_title', 'service_subtitle', 'service_description'],
        'fields': {
            'service_title': {
                'type': 'string',
                'label': 'عنوان الخدمة',
                'max_length': 100
            },
            'service_subtitle': {
                'type': 'string',
                'label': 'العنوان الفرعي',
                'max_length': 200
            },
            'service_description': {
                'type': 'string',
                'label': 'وصف الخدمة',
                'max_length': 2000
            },
            'header_bg_color': {
                'type': 'string',
                'label': 'لون خلفية الرأس',
                'default': '#007bff'
            }
        }
    }
}

# List of all default templates
DEFAULT_TEMPLATES = [
    PRODUCT_SHOWCASE_TEMPLATE,
    SERVICE_LANDING_TEMPLATE,
]
