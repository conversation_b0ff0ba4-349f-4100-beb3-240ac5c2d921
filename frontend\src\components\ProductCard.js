import React from 'react';
import {
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box
} from '@mui/material';
import { Link } from 'react-router-dom';

const ProductCard = ({ product, showActions = true }) => {
  const defaultImage = '/placeholder-product.jpg'; // You'll need to add this to public folder

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardMedia
        component="img"
        height="200"
        image={product.image || defaultImage}
        alt={product.name}
        sx={{ objectFit: 'cover' }}
      />
      
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography gutterBottom variant="h6" component="div">
          {product.name}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {product.description?.length > 100 
            ? `${product.description.substring(0, 100)}...` 
            : product.description
          }
        </Typography>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" color="primary">
            {product.price} ريال
          </Typography>
          
          {product.colors && product.colors.length > 0 && (
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {product.colors.slice(0, 3).map((color, index) => (
                <Chip 
                  key={index} 
                  label={color.color} 
                  size="small" 
                  variant="outlined" 
                />
              ))}
              {product.colors.length > 3 && (
                <Chip label={`+${product.colors.length - 3}`} size="small" />
              )}
            </Box>
          )}
        </Box>
        
        {product.sizes && product.sizes.length > 0 && (
          <Box sx={{ mt: 1, display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            {product.sizes.slice(0, 4).map((size, index) => (
              <Chip 
                key={index} 
                label={size.size} 
                size="small" 
                variant="outlined" 
              />
            ))}
            {product.sizes.length > 4 && (
              <Chip label={`+${product.sizes.length - 4}`} size="small" />
            )}
          </Box>
        )}
      </CardContent>
      
      {showActions && (
        <CardActions>
          <Button 
            size="small" 
            component={Link} 
            to={`/product/${product.id}`}
          >
            عرض التفاصيل
          </Button>
          <Button 
            size="small" 
            variant="contained" 
            component={Link} 
            to={`/product/${product.id}/order`}
          >
            اطلب الآن
          </Button>
        </CardActions>
      )}
    </Card>
  );
};

export default ProductCard;
