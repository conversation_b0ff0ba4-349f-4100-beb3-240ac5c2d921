import React from 'react';
import {
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
  IconButton
} from '@mui/material';
import { 
  Edit, 
  Visibility, 
  Star
} from '@mui/icons-material';
import { Link } from 'react-router-dom';

const ProductCard = ({ product, showActions = true, viewMode = 'grid' }) => {
  const defaultImage = '/placeholder-product.jpg';

  // دالة تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const hasDiscount = product.old_price && product.old_price > product.price;
  const discountPercentage = hasDiscount 
    ? Math.round(((product.old_price - product.price) / product.old_price) * 100)
    : 0;

  if (viewMode === 'list') {
    return (
      <Card sx={{ 
        display: 'flex', 
        height: 200,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 4
        }
      }}>
        <CardMedia
          component="img"
          sx={{ width: 200, objectFit: 'cover' }}
          image={product.image || defaultImage}
          alt={product.name}
        />
        
        <Box sx={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
          <CardContent sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
              <Typography variant="h6" component="div">
                {product.name}
              </Typography>
              
              {product.is_featured && (
                <Chip 
                  icon={<Star />}
                  label="مميز" 
                  color="warning"
                  size="small"
                />
              )}
            </Box>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {product.description?.length > 150 
                ? `${product.description.substring(0, 150)}...` 
                : product.description
              }
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                {hasDiscount && (
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      textDecoration: 'line-through', 
                      color: 'text.secondary',
                      mr: 1
                    }}
                  >
                    {formatCurrency(product.old_price)}
                  </Typography>
                )}
                <Typography variant="h6" color="primary" component="span">
                  {formatCurrency(product.price)}
                </Typography>
                {hasDiscount && (
                  <Chip 
                    label={`-${discountPercentage}%`}
                    color="error"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                )}
              </Box>
              
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip 
                  label={product.status === 'active' ? 'نشط' : 'غير نشط'} 
                  color={product.status === 'active' ? 'success' : 'default'}
                  size="small"
                />
              </Box>
            </Box>
          </CardContent>
          
          {showActions && (
            <CardActions sx={{ justifyContent: 'flex-end' }}>
              <IconButton 
                component={Link} 
                to={`/products/${product.id}`}
                color="primary"
              >
                <Visibility />
              </IconButton>
              <IconButton 
                component={Link} 
                to={`/products/${product.id}/edit`}
                color="primary"
              >
                <Edit />
              </IconButton>
            </CardActions>
          )}
        </Box>
      </Card>
    );
  }

  return (
    <Card sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      position: 'relative',
      transition: 'all 0.3s ease',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: 6
      }
    }}>
      <Box sx={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}>
        {product.is_featured && (
          <Chip 
            icon={<Star />}
            label="مميز" 
            color="warning"
            size="small"
            sx={{ mb: 0.5 }}
          />
        )}
        {hasDiscount && (
          <Chip 
            label={`-${discountPercentage}%`}
            color="error"
            size="small"
          />
        )}
      </Box>
      
      <CardMedia
        component="img"
        height="200"
        image={product.image || defaultImage}
        alt={product.name}
        sx={{ objectFit: 'cover' }}
      />
      
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography gutterBottom variant="h6" component="div">
          {product.name}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {product.description?.length > 100 
            ? `${product.description.substring(0, 100)}...` 
            : product.description
          }
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          {hasDiscount && (
            <Typography 
              variant="body2" 
              sx={{ 
                textDecoration: 'line-through', 
                color: 'text.secondary'
              }}
            >
              {formatCurrency(product.old_price)}
            </Typography>
          )}
          <Typography variant="h6" color="primary">
            {formatCurrency(product.price)}
          </Typography>
        </Box>
        
        {product.colors && product.colors.length > 0 && (
          <Box sx={{ display: 'flex', gap: 0.5, mb: 2 }}>
            {product.colors.slice(0, 4).map((color, index) => (
              <Box
                key={index}
                sx={{
                  width: 20,
                  height: 20,
                  borderRadius: '50%',
                  backgroundColor: color.color?.toLowerCase() || '#ccc',
                  border: '2px solid',
                  borderColor: 'divider'
                }}
                title={color.color}
              />
            ))}
            {product.colors.length > 4 && (
              <Typography variant="caption" color="text.secondary">
                +{product.colors.length - 4}
              </Typography>
            )}
          </Box>
        )}
        
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip 
            label={product.status === 'active' ? 'نشط' : 'غير نشط'} 
            color={product.status === 'active' ? 'success' : 'default'}
            size="small"
          />
          
          {product.view_count > 0 && (
            <Chip 
              label={`${product.view_count} مشاهدة`}
              size="small"
              variant="outlined"
            />
          )}
          
          {product.lead_count > 0 && (
            <Chip 
              label={`${product.lead_count} طلب`}
              size="small"
              variant="outlined"
              color="primary"
            />
          )}
        </Box>
      </CardContent>
      
      {showActions && (
        <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
          <Button 
            size="small" 
            component={Link} 
            to={`/products/${product.id}`}
            startIcon={<Visibility />}
            variant="outlined"
          >
            عرض
          </Button>
          
          <Button 
            size="small" 
            component={Link} 
            to={`/products/${product.id}/edit`}
            startIcon={<Edit />}
            variant="contained"
          >
            تعديل
          </Button>
        </CardActions>
      )}
    </Card>
  );
};

export default ProductCard;
