from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.utils import timezone
from datetime import timedelta
from django.db.models import Q
from .models import Lead
from .serializers import (
    LeadCreateSerializer,
    LeadListSerializer,
    LeadDetailSerializer,
    LeadStatsSerializer
)


@api_view(['POST'])
@permission_classes([AllowAny])
def create_lead(request):
    """إنشاء طلب جديد - متاح للجميع (للزوار)"""
    serializer = LeadCreateSerializer(data=request.data)
    if serializer.is_valid():
        # Get user from product or page context
        product_id = request.data.get('product')
        page_slug = request.data.get('page_slug')  # Get from page URL
        user_id = request.data.get('user_id')  # Fallback

        target_user = None

        # Try to determine user from product
        if product_id:
            try:
                from products.models import Product
                product = Product.objects.select_related('user').get(id=product_id)
                target_user = product.user
            except Product.DoesNotExist:
                return Response({
                    'error': 'المنتج غير موجود'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Try to determine user from page slug
        elif page_slug:
            try:
                from pages.models import UserPage
                user_page = UserPage.objects.select_related('user').get(slug=page_slug)
                target_user = user_page.user
                # Increment page views and leads
                user_page.increment_views()
                user_page.increment_leads()
            except UserPage.DoesNotExist:
                # Try legacy page or username-based page
                try:
                    from accounts.models import User
                    target_user = User.objects.get(username=page_slug)
                except User.DoesNotExist:
                    return Response({
                        'error': 'الصفحة غير موجودة'
                    }, status=status.HTTP_400_BAD_REQUEST)

        # Fallback to user_id
        elif user_id:
            try:
                from accounts.models import User
                target_user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response({
                    'error': 'المستخدم غير موجود'
                }, status=status.HTTP_400_BAD_REQUEST)

        if not target_user:
            return Response({
                'error': 'لا يمكن تحديد المستخدم المستهدف'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create lead
        lead = serializer.save(user=target_user)

        # Increment product leads if product specified
        if product_id:
            try:
                product.increment_leads()
            except:
                pass  # Don't fail if product increment fails

        # Process lead automatically (deduct from wallet and send WhatsApp)
        process_result = lead.process_lead()

        if process_result['success']:
            return Response({
                'message': 'تم إرسال طلبك بنجاح وسيتم التواصل معك قريباً',
                'lead_id': lead.id,
                'whatsapp_sent': process_result['whatsapp_sent']
            }, status=status.HTTP_201_CREATED)
        else:
            # Lead created but processing failed
            return Response({
                'message': 'تم إرسال طلبك ولكن حدث خطأ في المعالجة',
                'lead_id': lead.id,
                'error': process_result['error'],
                'warning': 'قد يكون رصيد صاحب المنتج غير كافي'
            }, status=status.HTTP_201_CREATED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserLeadsListView(generics.ListAPIView):
    """عرض طلبات المستخدم"""
    serializer_class = LeadListSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Lead.objects.filter(user=self.request.user).order_by('-created_at')


class LeadDetailView(generics.RetrieveAPIView):
    """عرض تفاصيل طلب"""
    serializer_class = LeadDetailSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Lead.objects.filter(user=self.request.user)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def lead_stats(request):
    """إحصائيات الطلبات للمستخدم"""
    user = request.user
    now = timezone.now()
    
    # Calculate date ranges
    today = now.date()
    week_ago = now - timedelta(days=7)
    month_ago = now - timedelta(days=30)
    
    # Get statistics
    total_leads = Lead.objects.filter(user=user).count()
    pending_leads = Lead.objects.filter(user=user, deducted=False).count()
    processed_leads = Lead.objects.filter(user=user, deducted=True).count()
    today_leads = Lead.objects.filter(user=user, created_at__date=today).count()
    this_week_leads = Lead.objects.filter(user=user, created_at__gte=week_ago).count()
    this_month_leads = Lead.objects.filter(user=user, created_at__gte=month_ago).count()
    
    stats = {
        'total_leads': total_leads,
        'pending_leads': pending_leads,
        'processed_leads': processed_leads,
        'today_leads': today_leads,
        'this_week_leads': this_week_leads,
        'this_month_leads': this_month_leads,
    }
    
    serializer = LeadStatsSerializer(stats)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_lead_processed(request, lead_id):
    """تحديد طلب كمعالج"""
    try:
        lead = Lead.objects.get(id=lead_id, user=request.user)
        lead.deducted = True
        lead.save()
        
        return Response({
            'message': 'تم تحديث حالة الطلب بنجاح',
            'lead': LeadDetailSerializer(lead).data
        })
    except Lead.DoesNotExist:
        return Response({
            'error': 'الطلب غير موجود'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def recent_leads(request):
    """آخر الطلبات للمستخدم"""
    limit = int(request.GET.get('limit', 10))
    leads = Lead.objects.filter(user=request.user).order_by('-created_at')[:limit]
    serializer = LeadListSerializer(leads, many=True)
    return Response(serializer.data)
