# Generated by Django 5.2.4 on 2025-07-09 22:45

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wallet', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='transaction',
            options={'ordering': ['-timestamp']},
        ),
        migrations.AddField(
            model_name='transaction',
            name='reference_id',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='transaction',
            name='transaction_type',
            field=models.CharField(choices=[('recharge', 'Recharge'), ('deduction', 'Deduction'), ('refund', 'Refund'), ('bonus', 'Bonus')], default='recharge', max_length=20),
        ),
        migrations.AddField(
            model_name='wallet',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='wallet',
            name='last_low_balance_alert',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='wallet',
            name='low_balance_threshold',
            field=models.DecimalField(decimal_places=2, default=50.0, max_digits=10),
        ),
        migrations.AddField(
            model_name='wallet',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
