import React, { useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Tabs,
  Tab,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  Save,
  Preview,
  Publish,
  Palette,
  TextFields,

  Settings
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { useAsyncOperation } from '../hooks/useApi';

const TemplateCustomizePage = () => {
  // const { templateId } = useParams(); // Will be used when implementing template loading
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);
  const [previewMode, setPreviewMode] = useState(false);

  // Mock template data - replace with actual API
  const [customConfig, setCustomConfig] = useState({
    // Basic Info
    title: 'صفحتي التسويقية',
    subtitle: 'أفضل المنتجات والخدمات',
    description: 'وصف مختصر عن ما تقدمه',
    
    // Contact Info
    phone: '',
    email: '',
    whatsapp: '',
    address: '',
    
    // Design
    primaryColor: '#1976d2',
    secondaryColor: '#dc004e',
    backgroundColor: '#ffffff',
    textColor: '#333333',
    fontFamily: 'Cairo',
    
    // Layout
    showHeader: true,
    showFooter: true,
    showContactForm: true,
    showSocialLinks: true,
    
    // Content
    heroImage: '',
    logoImage: '',
    backgroundImage: '',
    
    // SEO
    metaTitle: '',
    metaDescription: '',
    metaKeywords: ''
  });

  const { loading: saving, execute: executeSave } = useAsyncOperation();

  const handleConfigChange = (field, value) => {
    setCustomConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    // Save as draft
    const result = await executeSave(() => {
      // API call to save user page
      return Promise.resolve({ success: true });
    });
    
    if (result.success) {
      alert('تم حفظ التغييرات');
    }
  };

  const handlePublish = async () => {
    // Publish the page
    const result = await executeSave(() => {
      // API call to publish user page
      return Promise.resolve({ success: true });
    });
    
    if (result.success) {
      navigate('/my-pages');
    }
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        تخصيص القالب
      </Typography>

      <Grid container spacing={3}>
        {/* Customization Panel */}
        <Grid item xs={12} md={4}>
          <Paper elevation={3}>
            <Tabs
              value={activeTab}
              onChange={(e, newValue) => setActiveTab(newValue)}
              variant="fullWidth"
            >
              <Tab icon={<TextFields />} label="المحتوى" />
              <Tab icon={<Palette />} label="التصميم" />
              <Tab icon={<Settings />} label="الإعدادات" />
            </Tabs>

            {/* Content Tab */}
            <TabPanel value={activeTab} index={0}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  label="عنوان الصفحة"
                  value={customConfig.title}
                  onChange={(e) => handleConfigChange('title', e.target.value)}
                  fullWidth
                />
                
                <TextField
                  label="العنوان الفرعي"
                  value={customConfig.subtitle}
                  onChange={(e) => handleConfigChange('subtitle', e.target.value)}
                  fullWidth
                />
                
                <TextField
                  label="الوصف"
                  value={customConfig.description}
                  onChange={(e) => handleConfigChange('description', e.target.value)}
                  multiline
                  rows={3}
                  fullWidth
                />

                <Divider />

                <Typography variant="h6">معلومات التواصل</Typography>
                
                <TextField
                  label="رقم الهاتف"
                  value={customConfig.phone}
                  onChange={(e) => handleConfigChange('phone', e.target.value)}
                  fullWidth
                />
                
                <TextField
                  label="البريد الإلكتروني"
                  value={customConfig.email}
                  onChange={(e) => handleConfigChange('email', e.target.value)}
                  fullWidth
                />
                
                <TextField
                  label="رقم الواتساب"
                  value={customConfig.whatsapp}
                  onChange={(e) => handleConfigChange('whatsapp', e.target.value)}
                  fullWidth
                />
                
                <TextField
                  label="العنوان"
                  value={customConfig.address}
                  onChange={(e) => handleConfigChange('address', e.target.value)}
                  multiline
                  rows={2}
                  fullWidth
                />
              </Box>
            </TabPanel>

            {/* Design Tab */}
            <TabPanel value={activeTab} index={1}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Typography variant="h6">الألوان</Typography>
                
                <Box>
                  <Typography variant="body2" gutterBottom>اللون الأساسي</Typography>
                  <input
                    type="color"
                    value={customConfig.primaryColor}
                    onChange={(e) => handleConfigChange('primaryColor', e.target.value)}
                    style={{ width: '100%', height: 40, border: 'none', borderRadius: 4 }}
                  />
                </Box>
                
                <Box>
                  <Typography variant="body2" gutterBottom>اللون الثانوي</Typography>
                  <input
                    type="color"
                    value={customConfig.secondaryColor}
                    onChange={(e) => handleConfigChange('secondaryColor', e.target.value)}
                    style={{ width: '100%', height: 40, border: 'none', borderRadius: 4 }}
                  />
                </Box>
                
                <Box>
                  <Typography variant="body2" gutterBottom>لون الخلفية</Typography>
                  <input
                    type="color"
                    value={customConfig.backgroundColor}
                    onChange={(e) => handleConfigChange('backgroundColor', e.target.value)}
                    style={{ width: '100%', height: 40, border: 'none', borderRadius: 4 }}
                  />
                </Box>

                <Divider />

                <Typography variant="h6">الخطوط</Typography>
                
                <TextField
                  select
                  label="نوع الخط"
                  value={customConfig.fontFamily}
                  onChange={(e) => handleConfigChange('fontFamily', e.target.value)}
                  fullWidth
                  SelectProps={{ native: true }}
                >
                  <option value="Cairo">Cairo</option>
                  <option value="Tajawal">Tajawal</option>
                  <option value="Amiri">Amiri</option>
                  <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                </TextField>

                <Divider />

                <Typography variant="h6">الصور</Typography>
                
                <Button variant="outlined" component="label" fullWidth>
                  رفع صورة الشعار
                  <input type="file" hidden accept="image/*" />
                </Button>
                
                <Button variant="outlined" component="label" fullWidth>
                  رفع صورة الخلفية
                  <input type="file" hidden accept="image/*" />
                </Button>
                
                <Button variant="outlined" component="label" fullWidth>
                  رفع صورة البطل
                  <input type="file" hidden accept="image/*" />
                </Button>
              </Box>
            </TabPanel>

            {/* Settings Tab */}
            <TabPanel value={activeTab} index={2}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Typography variant="h6">عناصر الصفحة</Typography>
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={customConfig.showHeader}
                      onChange={(e) => handleConfigChange('showHeader', e.target.checked)}
                    />
                  }
                  label="إظهار الرأس"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={customConfig.showFooter}
                      onChange={(e) => handleConfigChange('showFooter', e.target.checked)}
                    />
                  }
                  label="إظهار التذييل"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={customConfig.showContactForm}
                      onChange={(e) => handleConfigChange('showContactForm', e.target.checked)}
                    />
                  }
                  label="إظهار نموذج التواصل"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={customConfig.showSocialLinks}
                      onChange={(e) => handleConfigChange('showSocialLinks', e.target.checked)}
                    />
                  }
                  label="إظهار روابط التواصل الاجتماعي"
                />

                <Divider />

                <Typography variant="h6">تحسين محركات البحث</Typography>
                
                <TextField
                  label="عنوان الصفحة (SEO)"
                  value={customConfig.metaTitle}
                  onChange={(e) => handleConfigChange('metaTitle', e.target.value)}
                  fullWidth
                  helperText="60 حرف كحد أقصى"
                />
                
                <TextField
                  label="وصف الصفحة (SEO)"
                  value={customConfig.metaDescription}
                  onChange={(e) => handleConfigChange('metaDescription', e.target.value)}
                  multiline
                  rows={2}
                  fullWidth
                  helperText="160 حرف كحد أقصى"
                />
                
                <TextField
                  label="الكلمات المفتاحية"
                  value={customConfig.metaKeywords}
                  onChange={(e) => handleConfigChange('metaKeywords', e.target.value)}
                  fullWidth
                  helperText="افصل بين الكلمات بفاصلة"
                />
              </Box>
            </TabPanel>

            {/* Action Buttons */}
            <Box sx={{ p: 2, display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                onClick={handleSave}
                disabled={saving}
                startIcon={saving ? <CircularProgress size={20} /> : <Save />}
                fullWidth
              >
                حفظ
              </Button>
              <Button
                variant="contained"
                onClick={handlePublish}
                disabled={saving}
                startIcon={<Publish />}
                fullWidth
              >
                نشر
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Preview Panel */}
        <Grid item xs={12} md={8}>
          <Paper elevation={3} sx={{ height: '80vh', overflow: 'auto' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">معاينة الصفحة</Typography>
                <Button
                  variant="outlined"
                  startIcon={<Preview />}
                  onClick={() => setPreviewMode(!previewMode)}
                >
                  {previewMode ? 'وضع التحرير' : 'معاينة كاملة'}
                </Button>
              </Box>
            </Box>
            
            <Box sx={{ p: 3 }}>
              {/* Preview Content */}
              <Box
                sx={{
                  backgroundColor: customConfig.backgroundColor,
                  color: customConfig.textColor,
                  fontFamily: customConfig.fontFamily,
                  minHeight: 400,
                  p: 3,
                  borderRadius: 1
                }}
              >
                {customConfig.showHeader && (
                  <Box sx={{ mb: 4, textAlign: 'center' }}>
                    <Typography variant="h3" sx={{ color: customConfig.primaryColor, mb: 1 }}>
                      {customConfig.title}
                    </Typography>
                    <Typography variant="h5" sx={{ color: customConfig.secondaryColor }}>
                      {customConfig.subtitle}
                    </Typography>
                  </Box>
                )}
                
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <Typography variant="body1" paragraph>
                    {customConfig.description}
                  </Typography>
                </Box>
                
                {customConfig.showContactForm && (
                  <Card sx={{ maxWidth: 400, mx: 'auto', mb: 4 }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        تواصل معنا
                      </Typography>
                      <TextField fullWidth label="الاسم" sx={{ mb: 2 }} />
                      <TextField fullWidth label="الهاتف" sx={{ mb: 2 }} />
                      <TextField fullWidth label="الرسالة" multiline rows={3} sx={{ mb: 2 }} />
                      <Button 
                        variant="contained" 
                        fullWidth
                        sx={{ backgroundColor: customConfig.primaryColor }}
                      >
                        إرسال
                      </Button>
                    </CardContent>
                  </Card>
                )}
                
                {customConfig.showFooter && (
                  <Box sx={{ textAlign: 'center', mt: 4, pt: 2, borderTop: 1, borderColor: 'divider' }}>
                    <Typography variant="body2">
                      {customConfig.phone && `📞 ${customConfig.phone}`}
                      {customConfig.email && ` | 📧 ${customConfig.email}`}
                    </Typography>
                    {customConfig.address && (
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        📍 {customConfig.address}
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TemplateCustomizePage;
