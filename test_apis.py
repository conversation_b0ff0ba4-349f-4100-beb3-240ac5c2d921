#!/usr/bin/env python3
"""
اختبار المزايا الرئيسية لمنصة lnk2store
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_authentication():
    """اختبار نظام المصادقة"""
    print("🔐 اختبار نظام المصادقة...")
    
    # تسجيل الدخول
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{API_BASE}/accounts/login/", json=login_data)
        print(f"   📊 حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ تسجيل الدخول نجح")
            print(f"   👤 المستخدم: {data.get('user', {}).get('username', 'غير محدد')}")
            return data.get('tokens', {}).get('access')
        else:
            print(f"   ❌ فشل تسجيل الدخول: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("   ❌ لا يمكن الاتصال بالخادم")
        return None
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return None

def test_products_api(token):
    """اختبار API المنتجات"""
    print("\n📦 اختبار نظام المنتجات...")
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    try:
        # جلب قائمة المنتجات
        response = requests.get(f"{API_BASE}/products/", headers=headers)
        print(f"   📊 حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            products = response.json()
            print(f"   ✅ تم جلب {len(products)} منتج")
            return True
        else:
            print(f"   ❌ فشل جلب المنتجات: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def test_leads_api(token):
    """اختبار نظام الطلبات"""
    print("\n📋 اختبار نظام الطلبات...")
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    try:
        # جلب الطلبات
        response = requests.get(f"{API_BASE}/leads/my-leads/", headers=headers)
        print(f"   📊 حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            leads = response.json()
            print(f"   ✅ تم جلب الطلبات بنجاح")
            return True
        else:
            print(f"   ❌ فشل جلب الطلبات: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def test_wallet_api(token):
    """اختبار نظام المحفظة"""
    print("\n💰 اختبار نظام المحفظة...")
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    try:
        # جلب معلومات المحفظة
        response = requests.get(f"{API_BASE}/wallet/balance/", headers=headers)
        print(f"   📊 حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            wallet = response.json()
            print(f"   ✅ تم جلب معلومات المحفظة")
            print(f"   💵 الرصيد: {wallet.get('balance', 0)}")
            return True
        else:
            print(f"   ❌ فشل جلب معلومات المحفظة: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def test_templates_api():
    """اختبار نظام القوالب"""
    print("\n🎨 اختبار نظام القوالب...")
    
    try:
        # جلب القوالب المتاحة
        response = requests.get(f"{API_BASE}/templates/")
        print(f"   📊 حالة الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            templates = response.json()
            print(f"   ✅ تم جلب {len(templates)} قالب")
            return True
        else:
            print(f"   ❌ فشل جلب القوالب: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def test_server_health():
    """اختبار صحة الخادم"""
    print("🏥 اختبار صحة الخادم...")
    
    try:
        response = requests.get(f"{BASE_URL}/admin/", timeout=5)
        print(f"   📊 حالة الاستجابة: {response.status_code}")
        
        if response.status_code in [200, 302]:  # 302 للتوجيه لصفحة تسجيل الدخول
            print("   ✅ الخادم يعمل بشكل طبيعي")
            return True
        else:
            print(f"   ⚠️  الخادم يستجيب لكن بحالة غير متوقعة: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ لا يمكن الاتصال بالخادم")
        return False
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار المزايا الرئيسية لمنصة lnk2store")
    print("=" * 50)
    
    # اختبار صحة الخادم أولاً
    if not test_server_health():
        print("\n❌ الخادم لا يعمل. يرجى التأكد من تشغيل الخادم الخلفي.")
        return False
    
    # اختبار المصادقة
    token = test_authentication()
    
    # اختبار باقي المزايا
    results = []
    results.append(test_products_api(token))
    results.append(test_leads_api(token))
    results.append(test_wallet_api(token))
    results.append(test_templates_api())
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    
    passed = sum(results) + (1 if token else 0)  # +1 للمصادقة
    total = len(results) + 2  # +2 للخادم والمصادقة
    
    print(f"   ✅ نجح: {passed}/{total}")
    print(f"   ❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع المزايا تعمل بشكل ممتاز!")
        return True
    else:
        print(f"\n⚠️  بعض المزايا تحتاج إلى مراجعة")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
