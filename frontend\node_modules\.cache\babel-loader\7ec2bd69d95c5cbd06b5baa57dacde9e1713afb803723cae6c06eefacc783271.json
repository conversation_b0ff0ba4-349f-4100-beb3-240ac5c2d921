{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getAccordionDetailsUtilityClass } from \"./accordionDetailsClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAccordionDetailsUtilityClass, classes);\n};\nconst AccordionDetailsRoot = styled('div', {\n  name: 'MuiAccordionDetails',\n  slot: 'Root'\n})(memoTheme(({\n  theme\n}) => ({\n  padding: theme.spacing(1, 2, 2)\n})));\nconst AccordionDetails = /*#__PURE__*/React.forwardRef(function AccordionDetails(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAccordionDetails'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AccordionDetailsRoot, {\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AccordionDetails.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AccordionDetails;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "memoTheme", "useDefaultProps", "getAccordionDetailsUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "AccordionDetailsRoot", "name", "slot", "theme", "padding", "spacing", "AccordionDetails", "forwardRef", "inProps", "ref", "props", "className", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["D:/apps/lnk2store/frontend/node_modules/@mui/material/esm/AccordionDetails/AccordionDetails.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getAccordionDetailsUtilityClass } from \"./accordionDetailsClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAccordionDetailsUtilityClass, classes);\n};\nconst AccordionDetailsRoot = styled('div', {\n  name: 'MuiAccordionDetails',\n  slot: 'Root'\n})(memoTheme(({\n  theme\n}) => ({\n  padding: theme.spacing(1, 2, 2)\n})));\nconst AccordionDetails = /*#__PURE__*/React.forwardRef(function AccordionDetails(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAccordionDetails'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AccordionDetailsRoot, {\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AccordionDetails.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AccordionDetails;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,+BAA+B,QAAQ,8BAA8B;AAC9E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOX,cAAc,CAACU,KAAK,EAAEN,+BAA+B,EAAEK,OAAO,CAAC;AACxE,CAAC;AACD,MAAMG,oBAAoB,GAAGX,MAAM,CAAC,KAAK,EAAE;EACzCY,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE;AACR,CAAC,CAAC,CAACZ,SAAS,CAAC,CAAC;EACZa;AACF,CAAC,MAAM;EACLC,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAChC,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,gBAAgB,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,SAASD,gBAAgBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7F,MAAMC,KAAK,GAAGnB,eAAe,CAAC;IAC5BmB,KAAK,EAAEF,OAAO;IACdP,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJU,SAAS;IACT,GAAGC;EACL,CAAC,GAAGF,KAAK;EACT,MAAMd,UAAU,GAAGc,KAAK;EACxB,MAAMb,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,oBAAoB,EAAE;IAC7CW,SAAS,EAAExB,IAAI,CAACU,OAAO,CAACE,IAAI,EAAEY,SAAS,CAAC;IACxCF,GAAG,EAAEA,GAAG;IACRb,UAAU,EAAEA,UAAU;IACtB,GAAGgB;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,gBAAgB,CAACU,SAAS,CAAC,yBAAyB;EAC1F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAE/B,SAAS,CAACgC,IAAI;EACxB;AACF;AACA;EACErB,OAAO,EAAEX,SAAS,CAACiC,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAEzB,SAAS,CAACkC,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAEnC,SAAS,CAACoC,SAAS,CAAC,CAACpC,SAAS,CAACqC,OAAO,CAACrC,SAAS,CAACoC,SAAS,CAAC,CAACpC,SAAS,CAACsC,IAAI,EAAEtC,SAAS,CAACiC,MAAM,EAAEjC,SAAS,CAACuC,IAAI,CAAC,CAAC,CAAC,EAAEvC,SAAS,CAACsC,IAAI,EAAEtC,SAAS,CAACiC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeb,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}