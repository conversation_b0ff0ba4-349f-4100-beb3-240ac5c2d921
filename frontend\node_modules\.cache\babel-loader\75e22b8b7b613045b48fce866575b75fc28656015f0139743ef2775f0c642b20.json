{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\pages\\\\LoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Paper, TextField, Button, Typography, Alert, CircularProgress, Link as MuiLink } from '@mui/material';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Redirect to intended page after login\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/dashboard';\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    const result = await login(formData);\n    if (result.success) {\n      navigate(from, {\n        replace: true\n      });\n    } else {\n      setError(result.error);\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      minHeight: '80vh'\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 4,\n        maxWidth: 400,\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        textAlign: \"center\",\n        gutterBottom: true,\n        children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0627\\u062E\\u062A\\u0628\\u0627\\u0631:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645: ahmed.hassan\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 39\n          }, this), \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631: testpassword123\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        component: \"form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",\n          name: \"username\",\n          value: formData.username,\n          onChange: handleChange,\n          required: true,\n          margin: \"normal\",\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\",\n          name: \"password\",\n          type: \"password\",\n          value: formData.password,\n          onChange: handleChange,\n          required: true,\n          margin: \"normal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          fullWidth: true,\n          variant: \"contained\",\n          size: \"large\",\n          disabled: loading,\n          sx: {\n            mt: 3,\n            mb: 2\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 24\n          }, this) : 'تسجيل الدخول'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"\\u0644\\u064A\\u0633 \\u0644\\u062F\\u064A\\u0643 \\u062D\\u0633\\u0627\\u0628\\u061F\", ' ', /*#__PURE__*/_jsxDEV(MuiLink, {\n              component: Link,\n              to: \"/register\",\n              children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628 \\u062C\\u062F\\u064A\\u062F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"HlAWOoLp1Vv/tdE2MbaF8MssENE=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Paper", "TextField", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "CircularProgress", "Link", "MuiLink", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "_location$state", "_location$state$from", "formData", "setFormData", "username", "password", "loading", "setLoading", "error", "setError", "login", "navigate", "location", "from", "state", "pathname", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "replace", "sx", "display", "justifyContent", "alignItems", "minHeight", "children", "elevation", "p", "max<PERSON><PERSON><PERSON>", "width", "variant", "textAlign", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "component", "onSubmit", "fullWidth", "label", "onChange", "required", "margin", "autoFocus", "type", "size", "disabled", "mt", "to", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Paper,\n  TextField,\n  Button,\n  Typography,\n  Alert,\n  CircularProgress,\n  Link as MuiLink\n} from '@mui/material';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\n\nconst LoginPage = () => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const { login } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Redirect to intended page after login\n  const from = location.state?.from?.pathname || '/dashboard';\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    const result = await login(formData);\n\n    if (result.success) {\n      navigate(from, { replace: true });\n    } else {\n      setError(result.error);\n    }\n\n    setLoading(false);\n  };\n\n  return (\n    <Box\n      sx={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '80vh'\n      }}\n    >\n      <Paper elevation={3} sx={{ p: 4, maxWidth: 400, width: '100%' }}>\n        <Typography variant=\"h4\" textAlign=\"center\" gutterBottom>\n          تسجيل الدخول\n        </Typography>\n\n        {/* Test User Info */}\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          <Typography variant=\"body2\" sx={{ mb: 1 }}>\n            <strong>بيانات الاختبار:</strong>\n          </Typography>\n          <Typography variant=\"body2\">\n            اسم المستخدم: ahmed.hassan<br/>\n            كلمة المرور: testpassword123\n          </Typography>\n        </Alert>\n\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Box component=\"form\" onSubmit={handleSubmit}>\n          <TextField\n            fullWidth\n            label=\"اسم المستخدم\"\n            name=\"username\"\n            value={formData.username}\n            onChange={handleChange}\n            required\n            margin=\"normal\"\n            autoFocus\n          />\n\n          <TextField\n            fullWidth\n            label=\"كلمة المرور\"\n            name=\"password\"\n            type=\"password\"\n            value={formData.password}\n            onChange={handleChange}\n            required\n            margin=\"normal\"\n          />\n\n          <Button\n            type=\"submit\"\n            fullWidth\n            variant=\"contained\"\n            size=\"large\"\n            disabled={loading}\n            sx={{ mt: 3, mb: 2 }}\n          >\n            {loading ? <CircularProgress size={24} /> : 'تسجيل الدخول'}\n          </Button>\n\n          <Box textAlign=\"center\">\n            <Typography variant=\"body2\">\n              ليس لديك حساب؟{' '}\n              <MuiLink component={Link} to=\"/register\">\n                إنشاء حساب جديد\n              </MuiLink>\n            </Typography>\n          </Box>\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,IAAIC,OAAO,QACV,eAAe;AACtB,SAASD,IAAI,EAAEE,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAE2B;EAAM,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3B,MAAMgB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMmB,IAAI,GAAG,EAAAb,eAAA,GAAAY,QAAQ,CAACE,KAAK,cAAAd,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBa,IAAI,cAAAZ,oBAAA,uBAApBA,oBAAA,CAAsBc,QAAQ,KAAI,YAAY;EAE3D,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1Bd,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACe,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBf,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMc,MAAM,GAAG,MAAMb,KAAK,CAACR,QAAQ,CAAC;IAEpC,IAAIqB,MAAM,CAACC,OAAO,EAAE;MAClBb,QAAQ,CAACE,IAAI,EAAE;QAAEY,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC,CAAC,MAAM;MACLhB,QAAQ,CAACc,MAAM,CAACf,KAAK,CAAC;IACxB;IAEAD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEV,OAAA,CAACb,GAAG;IACF0C,EAAE,EAAE;MACFC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,eAEFlC,OAAA,CAACZ,KAAK;MAAC+C,SAAS,EAAE,CAAE;MAACN,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEC,QAAQ,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBAC9DlC,OAAA,CAACT,UAAU;QAACgD,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,QAAQ;QAACC,YAAY;QAAAP,QAAA,EAAC;MAEzD;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb7C,OAAA,CAACR,KAAK;QAACsD,QAAQ,EAAC,MAAM;QAACjB,EAAE,EAAE;UAAEkB,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,gBACnClC,OAAA,CAACT,UAAU;UAACgD,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,eACxClC,OAAA;YAAAkC,QAAA,EAAQ;UAAgB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACb7C,OAAA,CAACT,UAAU;UAACgD,OAAO,EAAC,OAAO;UAAAL,QAAA,GAAC,mFACA,eAAAlC,OAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kFAEjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAEPlC,KAAK,iBACJX,OAAA,CAACR,KAAK;QAACsD,QAAQ,EAAC,OAAO;QAACjB,EAAE,EAAE;UAAEkB,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,EACnCvB;MAAK;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED7C,OAAA,CAACb,GAAG;QAAC6D,SAAS,EAAC,MAAM;QAACC,QAAQ,EAAEzB,YAAa;QAAAU,QAAA,gBAC3ClC,OAAA,CAACX,SAAS;UACR6D,SAAS;UACTC,KAAK,EAAC,qEAAc;UACpB7B,IAAI,EAAC,UAAU;UACfC,KAAK,EAAElB,QAAQ,CAACE,QAAS;UACzB6C,QAAQ,EAAEjC,YAAa;UACvBkC,QAAQ;UACRC,MAAM,EAAC,QAAQ;UACfC,SAAS;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEF7C,OAAA,CAACX,SAAS;UACR6D,SAAS;UACTC,KAAK,EAAC,+DAAa;UACnB7B,IAAI,EAAC,UAAU;UACfkC,IAAI,EAAC,UAAU;UACfjC,KAAK,EAAElB,QAAQ,CAACG,QAAS;UACzB4C,QAAQ,EAAEjC,YAAa;UACvBkC,QAAQ;UACRC,MAAM,EAAC;QAAQ;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEF7C,OAAA,CAACV,MAAM;UACLkE,IAAI,EAAC,QAAQ;UACbN,SAAS;UACTX,OAAO,EAAC,WAAW;UACnBkB,IAAI,EAAC,OAAO;UACZC,QAAQ,EAAEjD,OAAQ;UAClBoB,EAAE,EAAE;YAAE8B,EAAE,EAAE,CAAC;YAAEZ,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EAEpBzB,OAAO,gBAAGT,OAAA,CAACP,gBAAgB;YAACgE,IAAI,EAAE;UAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAc;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAET7C,OAAA,CAACb,GAAG;UAACqD,SAAS,EAAC,QAAQ;UAAAN,QAAA,eACrBlC,OAAA,CAACT,UAAU;YAACgD,OAAO,EAAC,OAAO;YAAAL,QAAA,GAAC,4EACZ,EAAC,GAAG,eAClBlC,OAAA,CAACL,OAAO;cAACqD,SAAS,EAAEtD,IAAK;cAACkE,EAAE,EAAC,WAAW;cAAA1B,QAAA,EAAC;YAEzC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAnHID,SAAS;EAAA,QAQKH,OAAO,EACRF,WAAW,EACXC,WAAW;AAAA;AAAAgE,EAAA,GAVxB5D,SAAS;AAqHf,eAAeA,SAAS;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}