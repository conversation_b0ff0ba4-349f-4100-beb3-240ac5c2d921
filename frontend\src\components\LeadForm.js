import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  MenuItem,
  Select,
  FormControl,
  InputLabel
} from '@mui/material';
import { leadsAPI, productsAPI } from '../services/api';
import { useApi, useAsyncOperation } from '../hooks/useApi';

const LeadForm = ({ productId = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone_number: '',
    message: '',
    product: productId || ''
  });

  const { data: products } = useApi(() => productsAPI.getProducts());
  const { loading, error, execute } = useAsyncOperation();
  const [success, setSuccess] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const result = await execute(() => leadsAPI.createLead(formData));
    
    if (result.success) {
      setSuccess(true);
      setFormData({
        name: '',
        email: '',
        phone_number: '',
        message: '',
        product: productId || ''
      });
      
      if (onSuccess) {
        onSuccess(result.data);
      }
      
      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    }
  };

  if (success) {
    return (
      <Box sx={{ textAlign: 'center', p: 3 }}>
        <Alert severity="success" sx={{ mb: 2 }}>
          تم إرسال طلبك بنجاح! سنتواصل معك قريباً.
        </Alert>
        <Button variant="outlined" onClick={() => setSuccess(false)}>
          إرسال طلب آخر
        </Button>
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ maxWidth: 500, mx: 'auto' }}>
      <Typography variant="h5" gutterBottom textAlign="center">
        اطلب الآن
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TextField
        fullWidth
        label="الاسم *"
        name="name"
        value={formData.name}
        onChange={handleChange}
        required
        margin="normal"
      />

      <TextField
        fullWidth
        label="البريد الإلكتروني"
        name="email"
        type="email"
        value={formData.email}
        onChange={handleChange}
        margin="normal"
      />

      <TextField
        fullWidth
        label="رقم الهاتف *"
        name="phone_number"
        value={formData.phone_number}
        onChange={handleChange}
        required
        margin="normal"
      />

      {!productId && products && (
        <FormControl fullWidth margin="normal">
          <InputLabel>المنتج</InputLabel>
          <Select
            name="product"
            value={formData.product}
            onChange={handleChange}
            label="المنتج"
          >
            <MenuItem value="">اختر المنتج</MenuItem>
            {products.map((product) => (
              <MenuItem key={product.id} value={product.id}>
                {product.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}

      <TextField
        fullWidth
        label="رسالة إضافية"
        name="message"
        value={formData.message}
        onChange={handleChange}
        multiline
        rows={4}
        margin="normal"
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        size="large"
        disabled={loading}
        sx={{ mt: 3, mb: 2 }}
      >
        {loading ? <CircularProgress size={24} /> : 'إرسال الطلب'}
      </Button>
    </Box>
  );
};

export default LeadForm;
