from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from datetime import timedelta
from django.db.models import Sum, Avg
from decimal import Decimal
from .models import Wallet, Transaction
from .serializers import (
    WalletSerializer,
    TransactionSerializer,
    RechargeSerializer,
    WalletStatsSerializer
)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def wallet_detail(request):
    """عرض تفاصيل المحفظة"""
    wallet, created = Wallet.objects.get_or_create(user=request.user)
    serializer = WalletSerializer(wallet)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def recharge_wallet(request):
    """شحن المحفظة"""
    serializer = RechargeSerializer(data=request.data)
    if serializer.is_valid():
        amount = serializer.validated_data['amount']
        payment_method = serializer.validated_data['payment_method']
        
        # TODO: Integrate with actual payment gateway
        # For now, we'll simulate successful payment
        
        wallet, created = Wallet.objects.get_or_create(user=request.user)
        wallet.balance += amount
        wallet.save()
        
        # Create transaction record
        Transaction.objects.create(
            wallet=wallet,
            amount=amount,
            description=f'شحن المحفظة - {payment_method}'
        )
        
        return Response({
            'message': f'تم شحن المحفظة بمبلغ {amount} ريال بنجاح',
            'new_balance': wallet.balance
        }, status=status.HTTP_200_OK)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def transaction_history(request):
    """تاريخ المعاملات"""
    wallet, created = Wallet.objects.get_or_create(user=request.user)
    
    # Pagination parameters
    page_size = int(request.GET.get('page_size', 20))
    page = int(request.GET.get('page', 1))
    offset = (page - 1) * page_size
    
    transactions = wallet.transactions.all().order_by('-timestamp')[offset:offset + page_size]
    serializer = TransactionSerializer(transactions, many=True)
    
    return Response({
        'transactions': serializer.data,
        'page': page,
        'page_size': page_size,
        'total_count': wallet.transactions.count()
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def wallet_stats(request):
    """إحصائيات المحفظة"""
    wallet, created = Wallet.objects.get_or_create(user=request.user)
    
    # Calculate statistics
    current_balance = wallet.balance
    
    # Total recharged (positive transactions)
    total_recharged = wallet.transactions.filter(amount__gt=0).aggregate(
        total=Sum('amount')
    )['total'] or Decimal('0.00')
    
    # Total spent (negative transactions)
    total_spent = abs(wallet.transactions.filter(amount__lt=0).aggregate(
        total=Sum('amount')
    )['total'] or Decimal('0.00'))
    
    # This month spending
    month_ago = timezone.now() - timedelta(days=30)
    this_month_spent = abs(wallet.transactions.filter(
        amount__lt=0,
        timestamp__gte=month_ago
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00'))
    
    # Average lead cost (assuming each negative transaction is a lead)
    negative_transactions = wallet.transactions.filter(amount__lt=0)
    average_lead_cost = abs(negative_transactions.aggregate(
        avg=Avg('amount')
    )['avg'] or Decimal('0.00'))
    
    transactions_count = wallet.transactions.count()
    
    stats = {
        'current_balance': current_balance,
        'total_recharged': total_recharged,
        'total_spent': total_spent,
        'this_month_spent': this_month_spent,
        'average_lead_cost': average_lead_cost,
        'transactions_count': transactions_count,
    }
    
    serializer = WalletStatsSerializer(stats)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def deduct_for_lead(request):
    """خصم مبلغ مقابل lead (للاستخدام الداخلي)"""
    amount = Decimal('10.00')  # Fixed amount per lead
    
    wallet, created = Wallet.objects.get_or_create(user=request.user)
    
    if wallet.balance < amount:
        return Response({
            'error': 'رصيد المحفظة غير كافي',
            'current_balance': wallet.balance,
            'required_amount': amount
        }, status=status.HTTP_400_BAD_REQUEST)
    
    wallet.balance -= amount
    wallet.save()
    
    # Create transaction record
    Transaction.objects.create(
        wallet=wallet,
        amount=-amount,
        description='خصم مقابل lead جديد'
    )
    
    return Response({
        'message': 'تم خصم المبلغ بنجاح',
        'deducted_amount': amount,
        'new_balance': wallet.balance
    })
