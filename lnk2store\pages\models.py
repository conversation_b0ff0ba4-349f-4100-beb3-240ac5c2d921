from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.validators import URLValidator
import json

User = get_user_model()

class Template(models.Model):
    """Marketing page templates"""
    TEMPLATE_TYPES = [
        ('product_showcase', 'عرض منتج'),
        ('service_landing', 'صفحة خدمة'),
        ('portfolio', 'معرض أعمال'),
        ('restaurant_menu', 'قائمة مطعم'),
        ('event_promotion', 'ترويج فعالية'),
        ('business_profile', 'ملف تجاري'),
    ]

    CATEGORIES = [
        ('ecommerce', 'تجارة إلكترونية'),
        ('services', 'خدمات'),
        ('food', 'طعام ومشروبات'),
        ('fashion', 'أزياء'),
        ('technology', 'تكنولوجيا'),
        ('health', 'صحة وجمال'),
        ('education', 'تعليم'),
        ('real_estate', 'عقارات'),
    ]

    name = models.CharField(max_length=200)
    description = models.TextField()
    template_type = models.CharField(max_length=50, choices=TEMPLATE_TYPES)
    category = models.CharField(max_length=50, choices=CATEGORIES)
    preview_image = models.ImageField(upload_to='templates/previews/', blank=True)
    is_premium = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    usage_count = models.IntegerField(default=0)

    # Template structure and styling
    html_structure = models.TextField(help_text="HTML template structure with placeholders")
    css_styles = models.TextField(help_text="CSS styles for the template")
    js_scripts = models.TextField(blank=True, help_text="JavaScript for interactive elements")

    # Configuration schema
    config_schema = models.JSONField(
        default=dict,
        help_text="JSON schema defining customizable fields"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usage_count', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    def increment_usage(self):
        """Increment usage counter"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

class Page(models.Model):
    """Legacy page model - kept for backward compatibility"""
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    content = models.TextField()
    template_name = models.CharField(max_length=255, default='pages/default.html')

    def __str__(self):
        return self.title

class UserPage(models.Model):
    """User's customized marketing pages"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('published', 'منشور'),
        ('archived', 'مؤرشف'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='marketing_pages')
    template = models.ForeignKey(Template, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    # Customization data
    custom_config = models.JSONField(
        default=dict,
        help_text="User's customization data"
    )

    # SEO fields
    meta_title = models.CharField(max_length=60, blank=True)
    meta_description = models.CharField(max_length=160, blank=True)
    meta_keywords = models.CharField(max_length=255, blank=True)

    # Analytics
    view_count = models.IntegerField(default=0)
    lead_count = models.IntegerField(default=0)
    conversion_rate = models.FloatField(default=0.0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        unique_together = ['user', 'slug']

    def __str__(self):
        return f"{self.title} - {self.user.username}"

    def increment_views(self):
        """Increment view counter"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def increment_leads(self):
        """Increment lead counter and update conversion rate"""
        self.lead_count += 1
        if self.view_count > 0:
            self.conversion_rate = (self.lead_count / self.view_count) * 100
        self.save(update_fields=['lead_count', 'conversion_rate'])

    def get_rendered_html(self):
        """Generate final HTML with user customizations"""
        from .template_renderer import TemplateRenderer
        renderer = TemplateRenderer(self.template, self.custom_config)
        return renderer.render()

class TemplateAsset(models.Model):
    """Assets for templates (images, icons, etc.)"""
    ASSET_TYPES = [
        ('image', 'صورة'),
        ('icon', 'أيقونة'),
        ('video', 'فيديو'),
        ('font', 'خط'),
    ]

    template = models.ForeignKey(Template, on_delete=models.CASCADE, related_name='assets')
    name = models.CharField(max_length=200)
    asset_type = models.CharField(max_length=20, choices=ASSET_TYPES)
    file = models.FileField(upload_to='templates/assets/')
    is_required = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.template.name} - {self.name}"

class TemplateReview(models.Model):
    """User reviews for templates"""
    template = models.ForeignKey(Template, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    rating = models.IntegerField(choices=[(i, i) for i in range(1, 6)])
    comment = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['template', 'user']

    def __str__(self):
        return f"{self.template.name} - {self.rating} stars by {self.user.username}"