from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import Product, ProductImage
from .serializers import (
    ProductListSerializer,
    ProductDetailSerializer,
    ProductCreateUpdateSerializer
)


class ProductListView(generics.ListAPIView):
    """عرض قائمة المنتجات - متاح للجميع"""
    queryset = Product.objects.all()
    serializer_class = ProductListSerializer
    permission_classes = [AllowAny]


class ProductDetailView(generics.RetrieveAPIView):
    """عرض تفاصيل منتج - متاح للجميع"""
    queryset = Product.objects.all()
    serializer_class = ProductDetailSerializer
    permission_classes = [AllowAny]


class ProductCreateView(generics.CreateAPIView):
    """إنشاء منتج جديد - للمستخدمين المسجلين فقط"""
    serializer_class = ProductCreateUpdateSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        # Check product limit for free users
        user = self.request.user
        if not user.is_premium:
            existing_products = Product.objects.filter(user=user).count()
            if existing_products >= 3:
                from rest_framework.exceptions import ValidationError
                raise ValidationError('المستخدمون المجانيون يمكنهم إنشاء 3 منتجات فقط')

        serializer.save(user=user)


class ProductUpdateView(generics.UpdateAPIView):
    """تحديث منتج - للمستخدمين المسجلين فقط"""
    serializer_class = ProductCreateUpdateSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Product.objects.filter(user=self.request.user)


class ProductDeleteView(generics.DestroyAPIView):
    """حذف منتج - للمستخدمين المسجلين فقط"""
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Product.objects.filter(user=self.request.user)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_product_images(request, product_id):
    """رفع صور إضافية للمنتج"""
    product = get_object_or_404(Product, id=product_id)
    
    # TODO: Add permission check to ensure user owns the product
    
    uploaded_files = request.FILES.getlist('images')
    if not uploaded_files:
        return Response({'error': 'لم يتم رفع أي صور'}, 
                       status=status.HTTP_400_BAD_REQUEST)
    
    created_images = []
    for file in uploaded_files:
        image = ProductImage.objects.create(product=product, image=file)
        created_images.append({
            'id': image.id,
            'image': image.image.url
        })
    
    return Response({
        'message': f'تم رفع {len(created_images)} صورة بنجاح',
        'images': created_images
    }, status=status.HTTP_201_CREATED)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_product_image(request, product_id, image_id):
    """حذف صورة من المنتج"""
    product = get_object_or_404(Product, id=product_id)
    image = get_object_or_404(ProductImage, id=image_id, product=product)
    
    # TODO: Add permission check to ensure user owns the product
    
    image.delete()
    return Response({'message': 'تم حذف الصورة بنجاح'})


@api_view(['GET'])
@permission_classes([AllowAny])
def product_search(request):
    """البحث في المنتجات"""
    query = request.GET.get('q', '')
    if not query:
        return Response({'error': 'يجب إدخال كلمة البحث'}, 
                       status=status.HTTP_400_BAD_REQUEST)
    
    products = Product.objects.filter(
        name__icontains=query
    ) | Product.objects.filter(
        description__icontains=query
    )
    
    serializer = ProductListSerializer(products, many=True)
    return Response({
        'query': query,
        'count': products.count(),
        'results': serializer.data
    })
