# Generated by Django 5.2.4 on 2025-07-10 18:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Page',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('content', models.TextField()),
                ('template_name', models.CharField(default='pages/default.html', max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='Template',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('template_type', models.CharField(choices=[('product_showcase', 'عرض منتج'), ('service_landing', 'صفحة خدمة'), ('portfolio', 'معرض أعمال'), ('restaurant_menu', 'قائمة مطعم'), ('event_promotion', 'ترويج فعالية'), ('business_profile', 'ملف تجاري')], max_length=50)),
                ('category', models.CharField(choices=[('ecommerce', 'تجارة إلكترونية'), ('services', 'خدمات'), ('food', 'طعام ومشروبات'), ('fashion', 'أزياء'), ('technology', 'تكنولوجيا'), ('health', 'صحة وجمال'), ('education', 'تعليم'), ('real_estate', 'عقارات')], max_length=50)),
                ('preview_image', models.ImageField(blank=True, upload_to='templates/previews/')),
                ('is_premium', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('usage_count', models.IntegerField(default=0)),
                ('html_structure', models.TextField(help_text='HTML template structure with placeholders')),
                ('css_styles', models.TextField(help_text='CSS styles for the template')),
                ('js_scripts', models.TextField(blank=True, help_text='JavaScript for interactive elements')),
                ('config_schema', models.JSONField(default=dict, help_text='JSON schema defining customizable fields')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-usage_count', 'name'],
            },
        ),
        migrations.CreateModel(
            name='TemplateAsset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('asset_type', models.CharField(choices=[('image', 'صورة'), ('icon', 'أيقونة'), ('video', 'فيديو'), ('font', 'خط')], max_length=20)),
                ('file', models.FileField(upload_to='templates/assets/')),
                ('is_required', models.BooleanField(default=False)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assets', to='pages.template')),
            ],
        ),
        migrations.CreateModel(
            name='TemplateReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)])),
                ('comment', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='pages.template')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('template', 'user')},
            },
        ),
        migrations.CreateModel(
            name='UserPage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('published', 'منشور'), ('archived', 'مؤرشف')], default='draft', max_length=20)),
                ('custom_config', models.JSONField(default=dict, help_text="User's customization data")),
                ('meta_title', models.CharField(blank=True, max_length=60)),
                ('meta_description', models.CharField(blank=True, max_length=160)),
                ('meta_keywords', models.CharField(blank=True, max_length=255)),
                ('view_count', models.IntegerField(default=0)),
                ('lead_count', models.IntegerField(default=0)),
                ('conversion_rate', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pages.template')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='marketing_pages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('user', 'slug')},
            },
        ),
    ]
