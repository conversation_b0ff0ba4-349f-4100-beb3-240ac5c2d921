{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormControlLabelUtilityClasses(slot) {\n  return generateUtilityClass('MuiFormControlLabel', slot);\n}\nconst formControlLabelClasses = generateUtilityClasses('MuiFormControlLabel', ['root', 'labelPlacementStart', 'labelPlacementTop', 'labelPlacementBottom', 'disabled', 'label', 'error', 'required', 'asterisk']);\nexport default formControlLabelClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getFormControlLabelUtilityClasses", "slot", "formControlLabelClasses"], "sources": ["D:/apps/lnk2store/frontend/node_modules/@mui/material/esm/FormControlLabel/formControlLabelClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormControlLabelUtilityClasses(slot) {\n  return generateUtilityClass('MuiFormControlLabel', slot);\n}\nconst formControlLabelClasses = generateUtilityClasses('MuiFormControlLabel', ['root', 'labelPlacementStart', 'labelPlacementTop', 'labelPlacementBottom', 'disabled', 'label', 'error', 'required', 'asterisk']);\nexport default formControlLabelClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,iCAAiCA,CAACC,IAAI,EAAE;EACtD,OAAOF,oBAAoB,CAAC,qBAAqB,EAAEE,IAAI,CAAC;AAC1D;AACA,MAAMC,uBAAuB,GAAGJ,sBAAsB,CAAC,qBAAqB,EAAE,CAAC,MAAM,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AACjN,eAAeI,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}