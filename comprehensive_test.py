#!/usr/bin/env python3
"""
اختبار شامل لجميع المزايا الرئيسية لمنصة lnk2store
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"
FRONTEND_URL = "http://localhost:3000"

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_feature(feature_name, status, details=""):
    """طباعة حالة الميزة"""
    status_icon = "✅" if status else "❌"
    print(f"{status_icon} {feature_name}")
    if details:
        print(f"   📝 {details}")

def test_comprehensive_features():
    """اختبار شامل لجميع المزايا"""
    
    print_header("اختبار شامل لمنصة lnk2store")
    
    results = {}
    
    # 1. اختبار الخوادم
    print_header("1. اختبار الخوادم والبنية التحتية")
    
    try:
        # اختبار الخادم الخلفي
        backend_response = requests.get(f"{BASE_URL}/admin/", timeout=5)
        backend_working = backend_response.status_code in [200, 302]
        print_feature("الخادم الخلفي (Django)", backend_working, 
                     f"حالة الاستجابة: {backend_response.status_code}")
        results['backend'] = backend_working
        
        # اختبار الخادم الأمامي
        frontend_response = requests.get(FRONTEND_URL, timeout=5)
        frontend_working = frontend_response.status_code == 200
        print_feature("الخادم الأمامي (React)", frontend_working,
                     f"حالة الاستجابة: {frontend_response.status_code}")
        results['frontend'] = frontend_working
        
    except Exception as e:
        print_feature("الخوادم", False, f"خطأ: {str(e)}")
        results['backend'] = False
        results['frontend'] = False
    
    # 2. اختبار نظام المصادقة
    print_header("2. نظام المصادقة والمستخدمين")
    
    try:
        # تسجيل الدخول
        login_data = {"username": "admin", "password": "admin123"}
        login_response = requests.post(f"{API_BASE}/accounts/login/", json=login_data)
        auth_working = login_response.status_code == 200
        
        if auth_working:
            token_data = login_response.json()
            token = token_data.get('tokens', {}).get('access')
            user_info = token_data.get('user', {})
            print_feature("تسجيل الدخول", True, 
                         f"المستخدم: {user_info.get('username')}")
            
            # اختبار معلومات المستخدم
            headers = {"Authorization": f"Bearer {token}"}
            profile_response = requests.get(f"{API_BASE}/accounts/profile/", headers=headers)
            profile_working = profile_response.status_code == 200
            print_feature("ملف المستخدم", profile_working)
            
            # اختبار الإحصائيات
            stats_response = requests.get(f"{API_BASE}/accounts/stats/", headers=headers)
            stats_working = stats_response.status_code == 200
            if stats_working:
                stats = stats_response.json()
                print_feature("إحصائيات المستخدم", True,
                             f"الطلبات: {stats.get('total_leads', 0)}, الرصيد: {stats.get('wallet_balance', 0)}")
            
        else:
            print_feature("تسجيل الدخول", False, f"خطأ: {login_response.text[:100]}")
            token = None
            
        results['authentication'] = auth_working
        
    except Exception as e:
        print_feature("نظام المصادقة", False, f"خطأ: {str(e)}")
        results['authentication'] = False
        token = None
    
    # 3. اختبار نظام المنتجات
    print_header("3. نظام إدارة المنتجات")
    
    try:
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        # جلب المنتجات
        products_response = requests.get(f"{API_BASE}/products/", headers=headers)
        products_working = products_response.status_code == 200
        
        if products_working:
            products = products_response.json()
            print_feature("جلب المنتجات", True, f"عدد المنتجات: {len(products)}")
            
            # اختبار تفاصيل منتج إذا كان متوفراً
            if products:
                product_id = products[0].get('id')
                detail_response = requests.get(f"{API_BASE}/products/{product_id}/")
                detail_working = detail_response.status_code == 200
                print_feature("تفاصيل المنتج", detail_working)
        else:
            print_feature("نظام المنتجات", False)
            
        results['products'] = products_working
        
    except Exception as e:
        print_feature("نظام المنتجات", False, f"خطأ: {str(e)}")
        results['products'] = False
    
    # 4. اختبار نظام الطلبات
    print_header("4. نظام جمع الطلبات")
    
    try:
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        # جلب الطلبات
        leads_response = requests.get(f"{API_BASE}/leads/my-leads/", headers=headers)
        leads_working = leads_response.status_code == 200
        
        if leads_working:
            leads = leads_response.json()
            print_feature("جلب الطلبات", True, f"عدد الطلبات: {len(leads)}")
            
            # اختبار إحصائيات الطلبات
            stats_response = requests.get(f"{API_BASE}/leads/stats/", headers=headers)
            stats_working = stats_response.status_code == 200
            print_feature("إحصائيات الطلبات", stats_working)
        else:
            print_feature("نظام الطلبات", False)
            
        results['leads'] = leads_working
        
    except Exception as e:
        print_feature("نظام الطلبات", False, f"خطأ: {str(e)}")
        results['leads'] = False
    
    # 5. اختبار نظام المحفظة
    print_header("5. نظام المحفظة والدفع")
    
    try:
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        # جلب معلومات المحفظة
        wallet_response = requests.get(f"{API_BASE}/wallet/", headers=headers)
        wallet_working = wallet_response.status_code == 200
        
        if wallet_working:
            wallet = wallet_response.json()
            print_feature("معلومات المحفظة", True, 
                         f"الرصيد: {wallet.get('balance', 0)}")
            
            # اختبار تاريخ المعاملات
            transactions_response = requests.get(f"{API_BASE}/wallet/transactions/", headers=headers)
            transactions_working = transactions_response.status_code == 200
            print_feature("تاريخ المعاملات", transactions_working)
            
            # اختبار إحصائيات المحفظة
            wallet_stats_response = requests.get(f"{API_BASE}/wallet/stats/", headers=headers)
            wallet_stats_working = wallet_stats_response.status_code == 200
            print_feature("إحصائيات المحفظة", wallet_stats_working)
        else:
            print_feature("نظام المحفظة", False)
            
        results['wallet'] = wallet_working
        
    except Exception as e:
        print_feature("نظام المحفظة", False, f"خطأ: {str(e)}")
        results['wallet'] = False
    
    # 6. اختبار نظام القوالب
    print_header("6. نظام القوالب والصفحات")
    
    try:
        # جلب القوالب
        templates_response = requests.get(f"{API_BASE}/templates/templates/")
        templates_working = templates_response.status_code == 200
        
        if templates_working:
            templates = templates_response.json()
            print_feature("جلب القوالب", True, f"عدد القوالب: {len(templates)}")
            
            # اختبار فئات القوالب
            categories_response = requests.get(f"{API_BASE}/templates/templates/categories/")
            categories_working = categories_response.status_code == 200
            print_feature("فئات القوالب", categories_working)
            
            # اختبار صفحات المستخدم
            if token:
                headers = {"Authorization": f"Bearer {token}"}
                pages_response = requests.get(f"{API_BASE}/templates/my-pages/", headers=headers)
                pages_working = pages_response.status_code == 200
                print_feature("صفحات المستخدم", pages_working)
        else:
            print_feature("نظام القوالب", False)
            
        results['templates'] = templates_working
        
    except Exception as e:
        print_feature("نظام القوالب", False, f"خطأ: {str(e)}")
        results['templates'] = False
    
    # النتائج النهائية
    print_header("📊 ملخص النتائج النهائية")
    
    total_features = len(results)
    working_features = sum(results.values())
    
    print(f"🎯 المزايا التي تعمل: {working_features}/{total_features}")
    print(f"📈 نسبة النجاح: {(working_features/total_features)*100:.1f}%")
    
    if working_features == total_features:
        print("\n🎉 تهانينا! جميع المزايا الرئيسية تعمل بشكل ممتاز!")
        print("✨ منصة lnk2store جاهزة للاستخدام")
    elif working_features >= total_features * 0.8:
        print("\n👍 ممتاز! معظم المزايا تعمل بشكل جيد")
        print("🔧 بعض المزايا تحتاج إلى تحسينات طفيفة")
    else:
        print("\n⚠️ تحتاج المنصة إلى مراجعة وإصلاحات")
    
    # تفاصيل المزايا
    print("\n📋 تفاصيل المزايا:")
    feature_names = {
        'backend': 'الخادم الخلفي',
        'frontend': 'الخادم الأمامي', 
        'authentication': 'نظام المصادقة',
        'products': 'نظام المنتجات',
        'leads': 'نظام الطلبات',
        'wallet': 'نظام المحفظة',
        'templates': 'نظام القوالب'
    }
    
    for key, working in results.items():
        status = "✅ يعمل" if working else "❌ لا يعمل"
        print(f"   {feature_names.get(key, key)}: {status}")
    
    return working_features == total_features

if __name__ == "__main__":
    success = test_comprehensive_features()
    sys.exit(0 if success else 1)
