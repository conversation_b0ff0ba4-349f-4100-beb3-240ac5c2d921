from django.urls import path
from . import api_views

urlpatterns = [
    # Product CRUD endpoints
    path('', api_views.ProductListView.as_view(), name='api_product_list'),
    path('create/', api_views.ProductCreateView.as_view(), name='api_product_create'),
    path('<int:pk>/', api_views.ProductDetailView.as_view(), name='api_product_detail'),
    path('<int:pk>/update/', api_views.ProductUpdateView.as_view(), name='api_product_update'),
    path('<int:pk>/delete/', api_views.ProductDeleteView.as_view(), name='api_product_delete'),
    
    # Product images endpoints
    path('<int:product_id>/images/', api_views.upload_product_images, name='api_product_images_upload'),
    path('<int:product_id>/images/<int:image_id>/delete/', api_views.delete_product_image, name='api_product_image_delete'),
    
    # Search endpoint
    path('search/', api_views.product_search, name='api_product_search'),

    # Categories endpoint
    path('categories/', api_views.product_categories, name='api_product_categories'),
]
