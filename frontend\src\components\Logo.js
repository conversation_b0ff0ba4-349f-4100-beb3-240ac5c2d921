import React from 'react';
import { Box } from '@mui/material';
import { Link } from 'react-router-dom';

const Logo = ({
  height = '40px',
  width = 'auto',
  variant = 'light', // 'light' or 'dark'
  linkTo = '/',
  forHeader = false, // Special handling for header
  sx = {},
  ...props
}) => {
  // Handle responsive height
  const logoSx = {
    height: typeof height === 'object' ? height : height,
    width,
    objectFit: 'contain',
    // Apply filter only for header on dark background
    filter: forHeader ? 'brightness(0) invert(1)' : 'none',
    ...sx
  };

  const LogoImage = () => (
    <Box
      component="img"
      src="/logo-transparent.svg"
      alt="Lnk2Store"
      sx={logoSx}
      onError={(e) => {
        // Fallback to PNG logo, then to text if both fail
        if (e.target.src.includes('transparent.svg')) {
          e.target.src = '/logo.png';
        } else {
          e.target.style.display = 'none';
          e.target.nextSibling.style.display = 'block';
        }
      }}
      {...props}
    />
  );

  const FallbackText = () => (
    <Box
      component="span"
      sx={{
        display: 'none',
        fontSize: typeof height === 'object' ? { xs: '1.2rem', md: '1.8rem' } : '1.5rem',
        fontWeight: 'bold',
        color: forHeader ? 'white' : 'primary.main',
        fontFamily: '"Cairo", "Roboto", "Helvetica", "Arial", sans-serif'
      }}
    >
      Lnk2Store
    </Box>
  );

  if (linkTo) {
    return (
      <Link 
        to={linkTo} 
        style={{ 
          textDecoration: 'none', 
          display: 'flex', 
          alignItems: 'center' 
        }}
      >
        <LogoImage />
        <FallbackText />
      </Link>
    );
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <LogoImage />
      <FallbackText />
    </Box>
  );
};

export default Logo;
