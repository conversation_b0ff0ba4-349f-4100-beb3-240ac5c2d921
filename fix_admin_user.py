#!/usr/bin/env python3
"""
إصلاح المستخدم الإداري
"""
import os
import sys
import django

# إعداد Django
sys.path.append('lnk2store')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'lnk2store.settings.development')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

def fix_admin_user():
    """إصلاح المستخدم الإداري"""
    print("🔧 إصلاح المستخدم الإداري...")
    
    try:
        # حذف المستخدم الحالي إذا كان موجوداً
        try:
            admin = User.objects.get(username='admin')
            admin.delete()
            print("✅ تم حذف المستخدم الإداري القديم")
        except User.DoesNotExist:
            print("ℹ️ لا يوجد مستخدم إداري سابق")
        
        # إنشاء مستخدم إداري جديد
        admin = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        admin.is_premium = True
        admin.save()
        
        print("✅ تم إنشاء المستخدم الإداري الجديد")
        print(f"   👤 اسم المستخدم: {admin.username}")
        print(f"   📧 البريد الإلكتروني: {admin.email}")
        print(f"   🔑 كلمة المرور: admin123")
        print(f"   ⭐ مستخدم مميز: {admin.is_premium}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_admin_user()
    sys.exit(0 if success else 1)
