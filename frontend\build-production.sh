#!/bin/bash

# React Production Build Script for lnk2store
# This script builds the React frontend for production deployment

set -e  # Exit on any error

echo "🚀 Building React frontend for production..."

# Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the frontend directory."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Create production environment file
echo "⚙️ Creating production environment configuration..."
cat > .env.production <<EOF
REACT_APP_API_URL=https://lnk2store.com/api/v1
REACT_APP_ENVIRONMENT=production
REACT_APP_SENTRY_DSN=
GENERATE_SOURCEMAP=false
EOF

# Build for production
echo "🔨 Building React application..."
npm run build

# Optimize build
echo "🔧 Optimizing build..."
if command -v gzip &> /dev/null; then
    echo "📦 Compressing static files..."
    find build/static -name "*.js" -exec gzip -k {} \;
    find build/static -name "*.css" -exec gzip -k {} \;
fi

# Create deployment package
echo "📦 Creating deployment package..."
tar -czf lnk2store-frontend-$(date +%Y%m%d-%H%M%S).tar.gz build/

echo "✅ Frontend build completed successfully!"
echo ""
echo "📁 Build files are in the 'build' directory"
echo "📦 Deployment package created: lnk2store-frontend-*.tar.gz"
echo ""
echo "🚀 To deploy:"
echo "1. Upload the build directory to your server"
echo "2. Configure Nginx to serve the static files"
echo "3. Update API endpoints in the backend"
