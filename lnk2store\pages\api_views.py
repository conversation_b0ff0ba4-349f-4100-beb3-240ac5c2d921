from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import Page
from .serializers import (
    PageListSerializer,
    PageDetailSerializer,
    PageCreateUpdateSerializer
)


class PageListView(generics.ListAPIView):
    """عرض قائمة الصفحات - متاح للجميع"""
    queryset = Page.objects.all()
    serializer_class = PageListSerializer
    permission_classes = [AllowAny]


class PageDetailView(generics.RetrieveAPIView):
    """عرض تفاصيل صفحة - متاح للجميع"""
    queryset = Page.objects.all()
    serializer_class = PageDetailSerializer
    permission_classes = [AllowAny]
    lookup_field = 'slug'


@api_view(['GET'])
@permission_classes([AllowAny])
def page_by_slug(request, slug):
    """الحصول على صفحة بالـ slug"""
    try:
        page = Page.objects.get(slug=slug)
        serializer = PageDetailSerializer(page)
        return Response(serializer.data)
    except Page.DoesNotExist:
        return Response({
            'error': 'الصفحة غير موجودة'
        }, status=status.HTTP_404_NOT_FOUND)


class PageCreateView(generics.CreateAPIView):
    """إنشاء صفحة جديدة - للمستخدمين المسجلين فقط"""
    serializer_class = PageCreateUpdateSerializer
    permission_classes = [IsAuthenticated]


class PageUpdateView(generics.UpdateAPIView):
    """تحديث صفحة - للمستخدمين المسجلين فقط"""
    queryset = Page.objects.all()
    serializer_class = PageCreateUpdateSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'slug'


class PageDeleteView(generics.DestroyAPIView):
    """حذف صفحة - للمستخدمين المسجلين فقط"""
    queryset = Page.objects.all()
    permission_classes = [IsAuthenticated]
    lookup_field = 'slug'


@api_view(['GET'])
@permission_classes([AllowAny])
def user_page(request, username):
    """الحصول على صفحة المستخدم"""
    try:
        from accounts.models import User
        user = User.objects.get(username=username)
        page = Page.objects.get(slug=username)
        
        serializer = PageDetailSerializer(page)
        return Response({
            'page': serializer.data,
            'user': {
                'username': user.username,
                'business_type': user.business_type,
                'date_joined': user.date_joined
            }
        })
    except (User.DoesNotExist, Page.DoesNotExist):
        return Response({
            'error': 'صفحة المستخدم غير موجودة'
        }, status=status.HTTP_404_NOT_FOUND)
