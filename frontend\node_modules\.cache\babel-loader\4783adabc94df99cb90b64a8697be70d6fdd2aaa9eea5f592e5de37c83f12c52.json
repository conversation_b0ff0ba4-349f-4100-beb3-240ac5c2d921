{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, Button, Box, Container } from '@mui/material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport Logo from './Logo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    await logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"static\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Logo, {\n            variant: \"dark\",\n            height: \"40px\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/dashboard\",\n            children: \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/products\",\n            children: \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/my-pages\",\n            children: \"\\u0635\\u0641\\u062D\\u0627\\u062A\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/templates\",\n            children: \"\\u0627\\u0644\\u0642\\u0648\\u0627\\u0644\\u0628\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/leads\",\n            children: \"\\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/wallet\",\n            children: \"\\u0627\\u0644\\u0645\\u062D\\u0641\\u0638\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this), ((user === null || user === void 0 ? void 0 : user.is_staff) || (user === null || user === void 0 ? void 0 : user.is_superuser)) && /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/admin\",\n            children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            children: [\"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C (\", user === null || user === void 0 ? void 0 : user.username, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/login\",\n            children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            component: Link,\n            to: \"/register\",\n            children: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0633\\u0627\\u0628\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        mt: 4,\n        mb: 4\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"B5aHiu91piX0w1CsOFDPXF/GbhU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "Container", "Link", "useNavigate", "useAuth", "Logo", "jsxDEV", "_jsxDEV", "Layout", "children", "_s", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "sx", "flexGrow", "position", "variant", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gap", "color", "component", "to", "is_staff", "is_superuser", "onClick", "username", "max<PERSON><PERSON><PERSON>", "mt", "mb", "_c", "$RefreshReg$"], "sources": ["D:/apps/lnk2store/frontend/src/components/Layout.js"], "sourcesContent": ["import React from 'react';\nimport { App<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON>, Button, Box, Container } from '@mui/material';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../hooks/useAuth';\nimport Logo from './Logo';\n\nconst Layout = ({ children }) => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/');\n  };\n\n  return (\n    <Box sx={{ flexGrow: 1 }}>\n      <AppBar position=\"static\">\n        <Toolbar>\n          <Box sx={{ flexGrow: 1 }}>\n            <Logo variant=\"dark\" height=\"40px\" />\n          </Box>\n          \n          {isAuthenticated ? (\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button color=\"inherit\" component={Link} to=\"/dashboard\">\n                لوحة التحكم\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/products\">\n                المنتجات\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/my-pages\">\n                صفحاتي\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/templates\">\n                القوالب\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/leads\">\n                الطلبات\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/wallet\">\n                المحفظة\n              </Button>\n              {(user?.is_staff || user?.is_superuser) && (\n                <Button color=\"inherit\" component={Link} to=\"/admin\">\n                  إدارة النظام\n                </Button>\n              )}\n              <Button color=\"inherit\" onClick={handleLogout}>\n                تسجيل الخروج ({user?.username})\n              </Button>\n            </Box>\n          ) : (\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button color=\"inherit\" component={Link} to=\"/login\">\n                تسجيل الدخول\n              </Button>\n              <Button color=\"inherit\" component={Link} to=\"/register\">\n                إنشاء حساب\n              </Button>\n            </Box>\n          )}\n        </Toolbar>\n      </AppBar>\n      \n      <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n        {children}\n      </Container>\n    </Box>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,eAAe;AACnF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,IAAI,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnD,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMF,MAAM,CAAC,CAAC;IACdC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEP,OAAA,CAACP,GAAG;IAACgB,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAE,CAAE;IAAAR,QAAA,gBACvBF,OAAA,CAACX,MAAM;MAACsB,QAAQ,EAAC,QAAQ;MAAAT,QAAA,eACvBF,OAAA,CAACV,OAAO;QAAAY,QAAA,gBACNF,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAR,QAAA,eACvBF,OAAA,CAACF,IAAI;YAACc,OAAO,EAAC,MAAM;YAACC,MAAM,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EAELZ,eAAe,gBACdL,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAjB,QAAA,gBACnCF,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,YAAY;YAAApB,QAAA,EAAC;UAEzD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjB,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,WAAW;YAAApB,QAAA,EAAC;UAExD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjB,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,WAAW;YAAApB,QAAA,EAAC;UAExD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjB,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,YAAY;YAAApB,QAAA,EAAC;UAEzD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjB,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,QAAQ;YAAApB,QAAA,EAAC;UAErD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjB,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,SAAS;YAAApB,QAAA,EAAC;UAEtD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACR,CAAC,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,QAAQ,MAAInB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,YAAY,mBACpCxB,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,QAAQ;YAAApB,QAAA,EAAC;UAErD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDjB,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACK,OAAO,EAAEjB,YAAa;YAAAN,QAAA,GAAC,uEAC/B,EAACE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,QAAQ,EAAC,GAChC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENjB,OAAA,CAACP,GAAG;UAACgB,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAjB,QAAA,gBACnCF,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,QAAQ;YAAApB,QAAA,EAAC;UAErD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjB,OAAA,CAACR,MAAM;YAAC4B,KAAK,EAAC,SAAS;YAACC,SAAS,EAAE1B,IAAK;YAAC2B,EAAE,EAAC,WAAW;YAAApB,QAAA,EAAC;UAExD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAETjB,OAAA,CAACN,SAAS;MAACiC,QAAQ,EAAC,IAAI;MAAClB,EAAE,EAAE;QAAEmB,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA3B,QAAA,EAC3CA;IAAQ;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACd,EAAA,CAhEIF,MAAM;EAAA,QACgCJ,OAAO,EAChCD,WAAW;AAAA;AAAAkC,EAAA,GAFxB7B,MAAM;AAkEZ,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}