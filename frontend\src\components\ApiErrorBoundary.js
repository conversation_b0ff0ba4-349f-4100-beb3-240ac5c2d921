import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  But<PERSON>,
  Al<PERSON>,
  <PERSON><PERSON><PERSON>,
  Icon<PERSON>utton
} from '@mui/material';
import {
  Warning,
  Refresh,
  ExpandMore,
  ExpandLess
} from '@mui/icons-material';

const ApiErrorBoundary = ({ 
  children, 
  error, 
  onRetry, 
  fallbackMessage = "حدث خطأ أثناء تحميل البيانات" 
}) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!error) {
    return children;
  }

  const getErrorMessage = (error) => {
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }
    
    if (error?.message) {
      return error.message;
    }

    switch (error?.response?.status) {
      case 400:
        return 'طلب غير صحيح - يرجى التحقق من البيانات المدخلة';
      case 401:
        return 'يجب تسجيل الدخول للوصول لهذه الصفحة';
      case 403:
        return 'ليس لديك صلاحية للوصول لهذا المحتوى';
      case 404:
        return 'المحتوى المطلوب غير موجود';
      case 429:
        return 'تم تجاوز الحد المسموح من الطلبات - يرجى المحاولة لاحقاً';
      case 500:
        return 'خطأ في الخادم - يرجى المحاولة لاحقاً';
      case 503:
        return 'الخدمة غير متاحة حالياً - يرجى المحاولة لاحقاً';
      default:
        return fallbackMessage;
    }
  };

  const getErrorSeverity = (error) => {
    const status = error?.response?.status;
    if (status >= 500) return 'error';
    if (status >= 400) return 'warning';
    return 'info';
  };

  return (
    <Box sx={{ p: 2 }}>
      <Alert 
        severity={getErrorSeverity(error)}
        icon={<Warning />}
        action={
          onRetry && (
            <Button
              color="inherit"
              size="small"
              startIcon={<Refresh />}
              onClick={onRetry}
            >
              إعادة المحاولة
            </Button>
          )
        }
      >
        <Typography variant="body1">
          {getErrorMessage(error)}
        </Typography>
        
        {process.env.NODE_ENV === 'development' && (
          <>
            <IconButton
              size="small"
              onClick={() => setShowDetails(!showDetails)}
              sx={{ ml: 1 }}
            >
              {showDetails ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
            
            <Collapse in={showDetails}>
              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                <Typography variant="caption" display="block" gutterBottom>
                  تفاصيل الخطأ (وضع التطوير):
                </Typography>
                <Typography 
                  variant="body2" 
                  component="pre" 
                  sx={{ 
                    fontSize: '0.75rem',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word'
                  }}
                >
                  {JSON.stringify(error, null, 2)}
                </Typography>
              </Box>
            </Collapse>
          </>
        )}
      </Alert>
    </Box>
  );
};

export default ApiErrorBoundary;
