from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import api_views

urlpatterns = [
    # Authentication endpoints
    path('register/', api_views.register, name='api_register'),
    path('login/', api_views.login, name='api_login'),
    path('logout/', api_views.logout, name='api_logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # User profile endpoints
    path('profile/', api_views.UserProfileView.as_view(), name='api_user_profile'),
    path('stats/', api_views.user_stats, name='api_user_stats'),
]
