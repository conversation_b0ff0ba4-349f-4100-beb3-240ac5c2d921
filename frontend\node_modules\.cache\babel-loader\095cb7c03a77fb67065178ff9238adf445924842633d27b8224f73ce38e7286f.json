{"ast": null, "code": "var _jsxFileName = \"D:\\\\apps\\\\lnk2store\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.js\";\nimport React from 'react';\nimport { Box, Typography, Button, Alert, Container, Paper } from '@mui/material';\nimport { ErrorOutline, Refresh, Home } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.handleRetry = () => {\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null\n      });\n    };\n    this.handleGoHome = () => {\n      window.location.href = '/dashboard';\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Log the error to console and potentially to a logging service\n    console.error('Error caught by boundary:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n\n    // You can also log the error to an error reporting service here\n    // logErrorToService(error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      return /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"md\",\n        sx: {\n          mt: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 3,\n          sx: {\n            p: 4,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ErrorOutline, {\n            sx: {\n              fontSize: 80,\n              color: 'error.main',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            gutterBottom: true,\n            color: \"error\",\n            children: \"\\u0639\\u0630\\u0631\\u0627\\u064B\\u060C \\u062D\\u062F\\u062B \\u062E\\u0637\\u0623 \\u063A\\u064A\\u0631 \\u0645\\u062A\\u0648\\u0642\\u0639\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: this.props.fallbackMessage || 'حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى أو العودة للصفحة الرئيسية.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), process.env.NODE_ENV === 'development' && this.state.error && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 3,\n              textAlign: 'left'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0637\\u0623 (\\u0648\\u0636\\u0639 \\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              component: \"pre\",\n              sx: {\n                fontSize: '0.8rem'\n              },\n              children: [this.state.error.toString(), this.state.errorInfo.componentStack]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 2,\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 28\n              }, this),\n              onClick: this.handleRetry,\n              size: \"large\",\n              children: \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0648\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 28\n              }, this),\n              onClick: this.handleGoHome,\n              size: \"large\",\n              children: \"\\u0627\\u0644\\u0639\\u0648\\u062F\\u0629 \\u0644\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this);\n    }\n\n    // If no error, render children normally\n    return this.props.children;\n  }\n}\n\n// Higher-order component for easier usage\nexport const withErrorBoundary = (Component, fallbackMessage) => {\n  return function WrappedComponent(props) {\n    return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n      fallbackMessage: fallbackMessage,\n      children: /*#__PURE__*/_jsxDEV(Component, {\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this);\n  };\n};\n\n// Hook for functional components to trigger error boundary\nexport const useErrorHandler = () => {\n  return (error, errorInfo) => {\n    console.error('Error handled:', error, errorInfo);\n    // This will trigger the error boundary\n    throw error;\n  };\n};\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "Container", "Paper", "ErrorOutline", "Refresh", "Home", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "handleGoHome", "window", "location", "href", "state", "getDerivedStateFromError", "componentDidCatch", "console", "render", "max<PERSON><PERSON><PERSON>", "sx", "mt", "children", "elevation", "p", "textAlign", "fontSize", "color", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "fallbackMessage", "process", "env", "NODE_ENV", "severity", "component", "toString", "componentStack", "display", "gap", "justifyContent", "startIcon", "onClick", "size", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WrappedComponent", "useErrorHandler"], "sources": ["D:/apps/lnk2store/frontend/src/components/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON>,\n  <PERSON>po<PERSON>,\n  <PERSON><PERSON>,\n  Al<PERSON>,\n  Container,\n  Paper\n} from '@mui/material';\nimport {\n  ErrorOutline,\n  Refresh,\n  Home\n} from '@mui/icons-material';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { \n      hasError: false, \n      error: null, \n      errorInfo: null \n    };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log the error to console and potentially to a logging service\n    console.error('Error caught by boundary:', error, errorInfo);\n    \n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n\n    // You can also log the error to an error reporting service here\n    // logErrorToService(error, errorInfo);\n  }\n\n  handleRetry = () => {\n    this.setState({ \n      hasError: false, \n      error: null, \n      errorInfo: null \n    });\n  };\n\n  handleGoHome = () => {\n    window.location.href = '/dashboard';\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      return (\n        <Container maxWidth=\"md\" sx={{ mt: 4 }}>\n          <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>\n            <ErrorOutline \n              sx={{ \n                fontSize: 80, \n                color: 'error.main', \n                mb: 2 \n              }} \n            />\n            \n            <Typography variant=\"h4\" gutterBottom color=\"error\">\n              عذراً، حدث خطأ غير متوقع\n            </Typography>\n            \n            <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              {this.props.fallbackMessage || \n                'حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى أو العودة للصفحة الرئيسية.'}\n            </Typography>\n\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <Alert severity=\"error\" sx={{ mb: 3, textAlign: 'left' }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  تفاصيل الخطأ (وضع التطوير):\n                </Typography>\n                <Typography variant=\"body2\" component=\"pre\" sx={{ fontSize: '0.8rem' }}>\n                  {this.state.error.toString()}\n                  {this.state.errorInfo.componentStack}\n                </Typography>\n              </Alert>\n            )}\n\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>\n              <Button\n                variant=\"contained\"\n                startIcon={<Refresh />}\n                onClick={this.handleRetry}\n                size=\"large\"\n              >\n                إعادة المحاولة\n              </Button>\n              \n              <Button\n                variant=\"outlined\"\n                startIcon={<Home />}\n                onClick={this.handleGoHome}\n                size=\"large\"\n              >\n                العودة للرئيسية\n              </Button>\n            </Box>\n          </Paper>\n        </Container>\n      );\n    }\n\n    // If no error, render children normally\n    return this.props.children;\n  }\n}\n\n// Higher-order component for easier usage\nexport const withErrorBoundary = (Component, fallbackMessage) => {\n  return function WrappedComponent(props) {\n    return (\n      <ErrorBoundary fallbackMessage={fallbackMessage}>\n        <Component {...props} />\n      </ErrorBoundary>\n    );\n  };\n};\n\n// Hook for functional components to trigger error boundary\nexport const useErrorHandler = () => {\n  return (error, errorInfo) => {\n    console.error('Error handled:', error, errorInfo);\n    // This will trigger the error boundary\n    throw error;\n  };\n};\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,KAAK,QACA,eAAe;AACtB,SACEC,YAAY,EACZC,OAAO,EACPC,IAAI,QACC,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,SAASZ,KAAK,CAACa,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAAC,KA0BfC,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QACZC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDC,YAAY,GAAG,MAAM;MACnBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY;IACrC,CAAC;IAnCC,IAAI,CAACC,KAAK,GAAG;MACXP,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE;IACb,CAAC;EACH;EAEA,OAAOM,wBAAwBA,CAACP,KAAK,EAAE;IACrC;IACA,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAS,iBAAiBA,CAACR,KAAK,EAAEC,SAAS,EAAE;IAClC;IACAQ,OAAO,CAACT,KAAK,CAAC,2BAA2B,EAAEA,KAAK,EAAEC,SAAS,CAAC;IAE5D,IAAI,CAACH,QAAQ,CAAC;MACZE,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;;IAEF;IACA;EACF;EAcAS,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACJ,KAAK,CAACP,QAAQ,EAAE;MACvB;MACA,oBACEP,OAAA,CAACN,SAAS;QAACyB,QAAQ,EAAC,IAAI;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eACrCtB,OAAA,CAACL,KAAK;UAAC4B,SAAS,EAAE,CAAE;UAACH,EAAE,EAAE;YAAEI,CAAC,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACrDtB,OAAA,CAACJ,YAAY;YACXwB,EAAE,EAAE;cACFM,QAAQ,EAAE,EAAE;cACZC,KAAK,EAAE,YAAY;cACnBC,EAAE,EAAE;YACN;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFhC,OAAA,CAACT,UAAU;YAAC0C,OAAO,EAAC,IAAI;YAACC,YAAY;YAACP,KAAK,EAAC,OAAO;YAAAL,QAAA,EAAC;UAEpD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbhC,OAAA,CAACT,UAAU;YAAC0C,OAAO,EAAC,OAAO;YAACN,KAAK,EAAC,gBAAgB;YAACP,EAAE,EAAE;cAAEQ,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,EAC9D,IAAI,CAAClB,KAAK,CAAC+B,eAAe,IACzB;UAAmF;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,EAEZI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAACxB,KAAK,CAACN,KAAK,iBACzDR,OAAA,CAACP,KAAK;YAAC8C,QAAQ,EAAC,OAAO;YAACnB,EAAE,EAAE;cAAEQ,EAAE,EAAE,CAAC;cAAEH,SAAS,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACvDtB,OAAA,CAACT,UAAU;cAAC0C,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAE7C;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhC,OAAA,CAACT,UAAU;cAAC0C,OAAO,EAAC,OAAO;cAACO,SAAS,EAAC,KAAK;cAACpB,EAAE,EAAE;gBAAEM,QAAQ,EAAE;cAAS,CAAE;cAAAJ,QAAA,GACpE,IAAI,CAACR,KAAK,CAACN,KAAK,CAACiC,QAAQ,CAAC,CAAC,EAC3B,IAAI,CAAC3B,KAAK,CAACL,SAAS,CAACiC,cAAc;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR,eAEDhC,OAAA,CAACV,GAAG;YAAC8B,EAAE,EAAE;cAAEuB,OAAO,EAAE,MAAM;cAAEC,GAAG,EAAE,CAAC;cAAEC,cAAc,EAAE;YAAS,CAAE;YAAAvB,QAAA,gBAC7DtB,OAAA,CAACR,MAAM;cACLyC,OAAO,EAAC,WAAW;cACnBa,SAAS,eAAE9C,OAAA,CAACH,OAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBe,OAAO,EAAE,IAAI,CAAC1C,WAAY;cAC1B2C,IAAI,EAAC,OAAO;cAAA1B,QAAA,EACb;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEThC,OAAA,CAACR,MAAM;cACLyC,OAAO,EAAC,UAAU;cAClBa,SAAS,eAAE9C,OAAA,CAACF,IAAI;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACpBe,OAAO,EAAE,IAAI,CAACrC,YAAa;cAC3BsC,IAAI,EAAC,OAAO;cAAA1B,QAAA,EACb;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEhB;;IAEA;IACA,OAAO,IAAI,CAAC5B,KAAK,CAACkB,QAAQ;EAC5B;AACF;;AAEA;AACA,OAAO,MAAM2B,iBAAiB,GAAGA,CAAC/C,SAAS,EAAEiC,eAAe,KAAK;EAC/D,OAAO,SAASe,gBAAgBA,CAAC9C,KAAK,EAAE;IACtC,oBACEJ,OAAA,CAACC,aAAa;MAACkC,eAAe,EAAEA,eAAgB;MAAAb,QAAA,eAC9CtB,OAAA,CAACE,SAAS;QAAA,GAAKE;MAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEpB,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMmB,eAAe,GAAGA,CAAA,KAAM;EACnC,OAAO,CAAC3C,KAAK,EAAEC,SAAS,KAAK;IAC3BQ,OAAO,CAACT,KAAK,CAAC,gBAAgB,EAAEA,KAAK,EAAEC,SAAS,CAAC;IACjD;IACA,MAAMD,KAAK;EACb,CAAC;AACH,CAAC;AAED,eAAeP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}