"""
Management command to create default page templates
"""
from django.core.management.base import BaseCommand
from pages.models import PageTemplate


class Command(BaseCommand):
    help = 'Create default page templates'

    def handle(self, *args, **options):
        self.stdout.write('Creating default templates...')
        
        # Modern Template
        modern_template, created = PageTemplate.objects.get_or_create(
            name='modern',
            defaults={
                'display_name': 'قالب عصري',
                'description': 'قالب عصري ونظيف مناسب لجميع أنواع المنتجات',
                'category': 'business',
                'is_premium': False,
                'html_structure': self.get_modern_html(),
                'css_styles': self.get_modern_css(),
                'js_code': self.get_modern_js(),
                'config_schema': self.get_modern_config_schema(),
                'preview_image': 'templates/previews/modern.jpg'
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Modern template created'))
        else:
            self.stdout.write('Modern template already exists')

        # Minimal Template
        minimal_template, created = PageTemplate.objects.get_or_create(
            name='minimal',
            defaults={
                'display_name': 'قالب بسيط',
                'description': 'قالب بسيط وأنيق للمنتجات الفاخرة',
                'category': 'luxury',
                'is_premium': False,
                'html_structure': self.get_minimal_html(),
                'css_styles': self.get_minimal_css(),
                'js_code': self.get_minimal_js(),
                'config_schema': self.get_minimal_config_schema(),
                'preview_image': 'templates/previews/minimal.jpg'
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Minimal template created'))
        else:
            self.stdout.write('Minimal template already exists')

        # E-commerce Template
        ecommerce_template, created = PageTemplate.objects.get_or_create(
            name='ecommerce',
            defaults={
                'display_name': 'قالب متجر إلكتروني',
                'description': 'قالب متخصص للمتاجر الإلكترونية مع عرض متقدم للمنتجات',
                'category': 'ecommerce',
                'is_premium': True,
                'html_structure': self.get_ecommerce_html(),
                'css_styles': self.get_ecommerce_css(),
                'js_code': self.get_ecommerce_js(),
                'config_schema': self.get_ecommerce_config_schema(),
                'preview_image': 'templates/previews/ecommerce.jpg'
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✓ E-commerce template created'))
        else:
            self.stdout.write('E-commerce template already exists')

        self.stdout.write(self.style.SUCCESS('Default templates setup complete!'))

    def get_modern_html(self):
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{page_title}}</title>
            <style>{css_content}</style>
        </head>
        <body>
            <header class="header">
                <div class="container">
                    <h1 class="logo">{{business_name}}</h1>
                    <nav class="nav">
                        <a href="#products">المنتجات</a>
                        <a href="#contact">تواصل معنا</a>
                    </nav>
                </div>
            </header>
            
            <main>
                <section class="hero">
                    <div class="container">
                        <h2 class="hero-title">{{hero_title}}</h2>
                        <p class="hero-subtitle">{{hero_subtitle}}</p>
                        <a href="#products" class="cta-button">تصفح المنتجات</a>
                    </div>
                </section>
                
                <section id="products" class="products">
                    <div class="container">
                        <h2 class="section-title">منتجاتنا</h2>
                        <div class="products-grid">
                            {{#each products}}
                            <div class="product-card">
                                <img src="{{image}}" alt="{{name}}" class="product-image">
                                <div class="product-info">
                                    <h3 class="product-name">{{name}}</h3>
                                    <p class="product-description">{{description}}</p>
                                    <div class="product-price">{{price}} ريال</div>
                                    <button class="order-button" onclick="openOrderForm('{{id}}')">
                                        اطلب الآن
                                    </button>
                                </div>
                            </div>
                            {{/each}}
                        </div>
                    </div>
                </section>
                
                <section id="contact" class="contact">
                    <div class="container">
                        <h2 class="section-title">تواصل معنا</h2>
                        <div class="contact-info">
                            <p>{{contact_phone}}</p>
                            <p>{{contact_email}}</p>
                        </div>
                    </div>
                </section>
            </main>
            
            <footer class="footer">
                <div class="container">
                    <p>&copy; 2024 {{business_name}}. جميع الحقوق محفوظة.</p>
                </div>
            </footer>
            
            <script>{js_content}</script>
        </body>
        </html>
        '''

    def get_modern_css(self):
        return '''
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: {{colors.text}};
            background-color: {{colors.background}};
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .header {
            background: {{colors.primary}};
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .nav a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            transition: opacity 0.3s;
        }
        
        .nav a:hover {
            opacity: 0.8;
        }
        
        .hero {
            background: linear-gradient(135deg, {{colors.primary}}, {{colors.secondary}});
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            background: white;
            color: {{colors.primary}};
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: transform 0.3s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .products {
            padding: 80px 0;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: {{colors.primary}};
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .product-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
        }
        
        .product-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }
        
        .product-info {
            padding: 20px;
        }
        
        .product-name {
            font-size: 1.3rem;
            margin-bottom: 10px;
            color: {{colors.primary}};
        }
        
        .product-description {
            color: #666;
            margin-bottom: 15px;
        }
        
        .product-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: {{colors.secondary}};
            margin-bottom: 15px;
        }
        
        .order-button {
            width: 100%;
            background: {{colors.primary}};
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .order-button:hover {
            background: {{colors.primary}}dd;
        }
        
        .contact {
            background: #f8f9fa;
            padding: 60px 0;
            text-align: center;
        }
        
        .contact-info p {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .header .container {
                flex-direction: column;
                gap: 10px;
            }
        }
        '''

    def get_modern_js(self):
        return '''
        function openOrderForm(productId) {
            // This will be implemented to show order form
            alert('سيتم فتح نموذج الطلب للمنتج: ' + productId);
        }
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        '''

    def get_modern_config_schema(self):
        return {
            "colors": {
                "type": "object",
                "properties": {
                    "primary": {"type": "string", "default": "#2196F3"},
                    "secondary": {"type": "string", "default": "#FFC107"},
                    "background": {"type": "string", "default": "#FFFFFF"},
                    "text": {"type": "string", "default": "#333333"}
                }
            },
            "content": {
                "type": "object", 
                "properties": {
                    "business_name": {"type": "string", "default": "اسم النشاط التجاري"},
                    "hero_title": {"type": "string", "default": "مرحباً بكم في متجرنا"},
                    "hero_subtitle": {"type": "string", "default": "نقدم أفضل المنتجات بأعلى جودة"},
                    "contact_phone": {"type": "string", "default": "0501234567"},
                    "contact_email": {"type": "string", "default": "<EMAIL>"}
                }
            }
        }

    def get_minimal_html(self):
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{page_title}}</title>
            <style>{css_content}</style>
        </head>
        <body>
            <div class="container">
                <header class="header">
                    <h1 class="brand">{{business_name}}</h1>
                    <p class="tagline">{{tagline}}</p>
                </header>
                
                <main class="main">
                    {{#each products}}
                    <article class="product">
                        <div class="product-image">
                            <img src="{{image}}" alt="{{name}}">
                        </div>
                        <div class="product-details">
                            <h2 class="product-title">{{name}}</h2>
                            <p class="product-description">{{description}}</p>
                            <div class="product-price">{{price}} ريال</div>
                            <button class="contact-button">تواصل معنا</button>
                        </div>
                    </article>
                    {{/each}}
                </main>
                
                <footer class="footer">
                    <p>{{contact_info}}</p>
                </footer>
            </div>
            
            <script>{js_content}</script>
        </body>
        </html>
        '''

    def get_minimal_css(self):
        return '''
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Georgia', serif;
            line-height: 1.8;
            color: {{colors.text}};
            background: {{colors.background}};
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
            border-bottom: 1px solid #eee;
            padding-bottom: 40px;
        }
        
        .brand {
            font-size: 2.5rem;
            font-weight: 300;
            color: {{colors.primary}};
            margin-bottom: 10px;
        }
        
        .tagline {
            font-style: italic;
            color: #666;
            font-size: 1.1rem;
        }
        
        .product {
            display: flex;
            margin-bottom: 80px;
            align-items: center;
        }
        
        .product:nth-child(even) {
            flex-direction: row-reverse;
        }
        
        .product-image {
            flex: 1;
            margin: 0 40px;
        }
        
        .product-image img {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .product-details {
            flex: 1;
            padding: 20px;
        }
        
        .product-title {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: {{colors.primary}};
            font-weight: 300;
        }
        
        .product-description {
            margin-bottom: 20px;
            color: #555;
        }
        
        .product-price {
            font-size: 1.5rem;
            color: {{colors.secondary}};
            margin-bottom: 25px;
            font-weight: bold;
        }
        
        .contact-button {
            background: transparent;
            border: 2px solid {{colors.primary}};
            color: {{colors.primary}};
            padding: 12px 30px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .contact-button:hover {
            background: {{colors.primary}};
            color: white;
        }
        
        .footer {
            text-align: center;
            margin-top: 60px;
            padding-top: 40px;
            border-top: 1px solid #eee;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .product {
                flex-direction: column !important;
            }
            
            .product-image {
                margin: 0 0 20px 0;
            }
            
            .brand {
                font-size: 2rem;
            }
        }
        '''

    def get_minimal_js(self):
        return '''
        document.querySelectorAll('.contact-button').forEach(button => {
            button.addEventListener('click', function() {
                alert('سيتم التواصل معكم قريباً');
            });
        });
        '''

    def get_minimal_config_schema(self):
        return {
            "colors": {
                "type": "object",
                "properties": {
                    "primary": {"type": "string", "default": "#2C3E50"},
                    "secondary": {"type": "string", "default": "#E74C3C"},
                    "background": {"type": "string", "default": "#FFFFFF"},
                    "text": {"type": "string", "default": "#2C3E50"}
                }
            },
            "content": {
                "type": "object",
                "properties": {
                    "business_name": {"type": "string", "default": "العلامة التجارية"},
                    "tagline": {"type": "string", "default": "الجودة والأناقة"},
                    "contact_info": {"type": "string", "default": "للتواصل: 0501234567"}
                }
            }
        }

    def get_ecommerce_html(self):
        # This would be a more complex e-commerce template
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{page_title}}</title>
            <style>{css_content}</style>
        </head>
        <body>
            <!-- E-commerce template with advanced features -->
            <div class="ecommerce-container">
                <!-- Header with search and cart -->
                <!-- Product grid with filters -->
                <!-- Shopping cart functionality -->
                <!-- Checkout process -->
            </div>
            <script>{js_content}</script>
        </body>
        </html>
        '''

    def get_ecommerce_css(self):
        return '''
        /* Advanced e-commerce styles */
        .ecommerce-container {
            /* Complex grid layouts and responsive design */
        }
        '''

    def get_ecommerce_js(self):
        return '''
        // Advanced e-commerce functionality
        // Cart management, filters, search, etc.
        '''

    def get_ecommerce_config_schema(self):
        return {
            "colors": {
                "type": "object",
                "properties": {
                    "primary": {"type": "string", "default": "#007BFF"},
                    "secondary": {"type": "string", "default": "#28A745"},
                    "background": {"type": "string", "default": "#F8F9FA"},
                    "text": {"type": "string", "default": "#212529"}
                }
            },
            "features": {
                "type": "object",
                "properties": {
                    "show_search": {"type": "boolean", "default": True},
                    "show_cart": {"type": "boolean", "default": True},
                    "show_filters": {"type": "boolean", "default": True}
                }
            }
        }
