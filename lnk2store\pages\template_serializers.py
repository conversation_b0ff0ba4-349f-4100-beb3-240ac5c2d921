from rest_framework import serializers
from .models import Template, UserPage, TemplateAsset, TemplateReview

class TemplateListSerializer(serializers.ModelSerializer):
    """Serializer for template list view"""
    class Meta:
        model = Template
        fields = ('id', 'name', 'description', 'template_type', 'category', 
                 'preview_image', 'is_premium', 'usage_count')

class TemplateDetailSerializer(serializers.ModelSerializer):
    """Serializer for template detail view"""
    class Meta:
        model = Template
        fields = ('id', 'name', 'description', 'template_type', 'category',
                 'preview_image', 'is_premium', 'usage_count', 'config_schema',
                 'html_structure', 'css_styles', 'js_scripts')

class UserPageListSerializer(serializers.ModelSerializer):
    """Serializer for user page list"""
    template_name = serializers.CharField(source='template.name', read_only=True)
    
    class Meta:
        model = UserPage
        fields = ('id', 'title', 'slug', 'status', 'template_name', 
                 'view_count', 'lead_count', 'conversion_rate', 'created_at')

class UserPageDetailSerializer(serializers.ModelSerializer):
    """Serializer for user page detail"""
    template = TemplateDetailSerializer(read_only=True)
    
    class Meta:
        model = UserPage
        fields = ('id', 'title', 'slug', 'status', 'custom_config',
                 'meta_title', 'meta_description', 'meta_keywords',
                 'view_count', 'lead_count', 'conversion_rate',
                 'template', 'created_at', 'updated_at', 'published_at')

class UserPageCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating user pages"""
    class Meta:
        model = UserPage
        fields = ('title', 'slug', 'template', 'custom_config',
                 'meta_title', 'meta_description', 'meta_keywords')

    def validate_slug(self, value):
        # Check if slug already exists
        if UserPage.objects.filter(slug=value).exists():
            raise serializers.ValidationError('صفحة بهذا الرابط موجودة بالفعل')
        return value

class UserPageUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user pages"""
    class Meta:
        model = UserPage
        fields = ('title', 'custom_config', 'status',
                 'meta_title', 'meta_description', 'meta_keywords')

class TemplateReviewSerializer(serializers.ModelSerializer):
    """Serializer for template reviews"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = TemplateReview
        fields = ('id', 'rating', 'comment', 'user_name', 'created_at')

class TemplateReviewCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating template reviews"""
    class Meta:
        model = TemplateReview
        fields = ('template', 'rating', 'comment')

    def validate_rating(self, value):
        if value < 1 or value > 5:
            raise serializers.ValidationError('التقييم يجب أن يكون بين 1 و 5')
        return value
